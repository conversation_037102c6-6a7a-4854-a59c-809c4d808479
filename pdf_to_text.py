import PyPDF2
import sys
import os


def pdf_to_text(pdf_path, output_path=None):
    """
    Convert a PDF file to text

    Args:
        pdf_path (str): Path to the PDF file
        output_path (str, optional): Path to save the text file. If None, will use the same name as the PDF but with .txt extension

    Returns:
        str: Path to the output text file
    """
    if output_path is None:
        output_path = os.path.splitext(pdf_path)[0] + ".txt"

    with open(pdf_path, "rb") as file:
        reader = PyPDF2.PdfReader(file)
        text = ""

        # Extract text from each page
        for page_num in range(len(reader.pages)):
            page = reader.pages[page_num]
            text += page.extract_text()
            text += "\n\n--- Page {} ---\n\n".format(page_num + 1)

    # Write text to file
    with open(output_path, "w", encoding="utf-8") as file:
        file.write(text)

    return output_path


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python pdf_to_text.py <pdf_path> [output_path]")
        sys.exit(1)

    pdf_path = sys.argv[1]
    output_path = sys.argv[2] if len(sys.argv) > 2 else None

    try:
        output_file = pdf_to_text(pdf_path, output_path)
        print(f"Successfully converted PDF to text. Output saved to: {output_file}")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)
