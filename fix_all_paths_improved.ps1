$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
  
  # Calculate the correct relative path to the root based on file depth
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $folderDepth = ($filePath.Split("\").Length - 1)
  $prefix = "../" * $folderDepth
  
  # Fix Entertainment dropdown links
  if ($content -match '<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>\s*<div class="dropdown-content">\s*<a href="[^"]*">Games</a>') {
    $content = $content -replace '(<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>\s*<div class="dropdown-content">\s*<a href=")[^"]*(">Games</a>)', "`${1}${prefix}games.html`${2}"
  }
  
  # Fix Video channels dropdown links
  if ($content -match '<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Videos</a>\s*<div class="dropdown-content" id="channels-dropdown">') {
    # Replace all video channel links with the correct paths
    $content = $content -replace '(<a href=")[.]{3,}(/video.html\?channel=[^"]+)', "`${1}${prefix}`${2}"
  }
  
  # Fix Categories dropdown links
  if ($content -match '<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>\s*<div class="dropdown-content">') {
    # Replace all category links with the correct paths
    $content = $content -replace '(<a href=")[^"]*?(index.html#daily-life">)', "`${1}${prefix}`${2}"
    $content = $content -replace '(<a href=")[^"]*?(index.html#web-development">)', "`${1}${prefix}`${2}"
    $content = $content -replace '(<a href=")[^"]*?(index.html#cleaner">)', "`${1}${prefix}`${2}"
    $content = $content -replace '(<a href=")[^"]*?(index.html#kitchen-assistant">)', "`${1}${prefix}`${2}"
    $content = $content -replace '(<a href=")[^"]*?(index.html#warehouse">)', "`${1}${prefix}`${2}"
  }
  
  # Save the modified content back to the file if changes were made
  if ($content -ne $originalContent) {
    Set-Content -Path $file -Value $content
    Write-Host "Fixed paths in: $file"
  }
}

Write-Host "All paths have been fixed!"