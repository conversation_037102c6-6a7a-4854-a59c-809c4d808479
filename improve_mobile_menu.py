#!/usr/bin/env python3
"""
Script to improve mobile menu functionality with better event handling
"""

import os
import re

# Improved mobile menu JavaScript
IMPROVED_MOBILE_JS = '''// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }'''

def improve_mobile_menu(file_path):
    """Improve mobile menu functionality in a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find the existing mobile menu JavaScript and replace it
        old_js_pattern = r'// Unified Mobile Menu Toggle Functionality\s*document\.addEventListener\(\'DOMContentLoaded\', function\(\) \{.*?// Close mobile menu when clicking outside.*?\}\);\s*\}\);'
        
        if re.search(old_js_pattern, content, re.DOTALL):
            content = re.sub(old_js_pattern, IMPROVED_MOBILE_JS, content, flags=re.DOTALL)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Improved mobile menu: {file_path}")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error improving {file_path}: {e}")
        return False

def find_all_html_files():
    """Find all HTML files in finnish_grammar directory"""
    html_files = []
    
    # Find all HTML files recursively
    for root, dirs, files in os.walk('finnish_grammar'):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    return html_files

def main():
    """Main function"""
    print("Improving mobile menu functionality...")
    
    html_files = find_all_html_files()
    updated_count = 0
    
    for file_path in html_files:
        if improve_mobile_menu(file_path):
            updated_count += 1
    
    print(f"\nImproved mobile menu in {updated_count} files")
    print(f"Total files checked: {len(html_files)}")

if __name__ == "__main__":
    main()
