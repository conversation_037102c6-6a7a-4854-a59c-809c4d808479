# Chapter 32: CSS-tyylittely / CSS Styling

## Objectives / Tavoitteet
- Learn vocabulary related to CSS and web styling in Finnish
- Understand how to discuss CSS properties and selectors
- Be able to explain basic styling concepts and techniques
- Master basic conversations about web design and styling

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Tyylitiedosto - Stylesheet
2. Val<PERSON>in - Selector
3. Ominaisuus - Property
4. Arvo - Value
5. Väri - Color
6. F<PERSON>ti - Font
7. Tausta - Background
8. Reunus - Border
9. Marginaali - Margin
10. Täyte - Padding
11. Asettelu - Layout
12. <PERSON><PERSON><PERSON> - Width
13. <PERSON><PERSON><PERSON> - Height
14. Sijainti - Position
15. Responsiivisuus - Responsiveness

## Grammar Points / Kielioppi
1. **Technical Adjectives in Finnish**:
   - Describing visual properties
   - Example: <PERSON><PERSON><PERSON> on sininen ja lihavoitu. (The text is blue and bold.)

2. **Adessive Case (-lla/-llä) for Styling Methods**:
   - By means of
   - Example: <PERSON>ylitte<PERSON> sivun CSS:llä. (I style the page with CSS.)

3. **Translative Case (-ksi) for Style Changes**:
   - Becoming something
   - Example: <PERSON>utan taustan punaiseksi. (I change the background to red.)

4. **Genitive Case for Style Relationships**:
   - Possession or connection
   - Example: Elementin marginaali on 10 pikseliä. (The element's margin is 10 pixels.)

5. **Partitive Case with Style Quantities**:
   - Partial objects
   - Example: Lisään enemmän täytettä. (I add more padding.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: CSS styling lesson / CSS-tyylittelyn oppitunti

<div class="conversation">
<p><strong>Opettaja</strong>: Tervetuloa CSS-tyylittelyn kurssille! Tänään opimme, miten verkkosivuja voidaan tyylitellä CSS:n avulla.<br>
<em>(ter-ve-tu-lo-a CSS-tyy-lit-te-lyn kurs-sil-le! tä-nään o-pim-me, mi-ten verk-ko-si-vu-ja voi-daan tyy-li-tel-lä CSS:n a-vul-la.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos! Olen jo oppinut HTML-perusteita, mutta sivuni näyttävät aika tylsiltä.<br>
<em>(kii-tos! o-len jo op-pi-nut HTML-pe-rus-tei-ta, mut-ta si-vu-ni näyt-tä-vät ai-ka tyl-sil-tä.)</em></p>

<p><strong>Opettaja</strong>: CSS auttaa juuri siihen! CSS on lyhenne sanoista Cascading Style Sheets. Sen avulla voit muuttaa verkkosivun ulkoasua ilman, että HTML-rakennetta tarvitsee muuttaa.<br>
<em>(CSS aut-taa juu-ri sii-hen! CSS on ly-hen-ne sa-nois-ta cas-ca-ding sty-le sheets. sen a-vul-la voit muut-taa verk-ko-si-vun ul-ko-a-su-a il-man, et-tä HTML-ra-ken-net-ta tar-vit-see muut-taa.)</em></p>

<p><strong>Opiskelija</strong>: Miten CSS-koodi kirjoitetaan?<br>
<em>(mi-ten CSS-koo-di kir-joi-te-taan?)</em></p>

<p><strong>Opettaja</strong>: CSS-koodi koostuu valitsimista ja ominaisuuksista. Valitsin määrittelee, mihin elementtiin tyyli vaikuttaa, ja ominaisuudet määrittelevät, miltä elementti näyttää.<br>
<em>(CSS-koo-di koos-tuu va-lit-si-mis-ta ja o-mi-nai-suuk-sis-ta. va-lit-sin mää-rit-te-lee, mi-hin e-le-ment-tiin tyy-li vai-kut-taa, ja o-mi-nai-suu-det mää-rit-te-le-vät, mil-tä e-le-ment-ti näyt-tää.)</em></p>

<p><strong>Opiskelija</strong>: Voisitko antaa esimerkin?<br>
<em>(voi-sit-ko an-taa e-si-mer-kin?)</em></p>

<p><strong>Opiskelija</strong>: Ymmärrän. Miten voin liittää CSS:n HTML-sivuuni?<br>
<em>(ym-mär-rän. mi-ten voin liit-tää CSS:n HTML-si-vuu-ni?)</em></p>

<p><strong>Opettaja</strong>: Siihen on kolme tapaa. Voit käyttää ulkoista tyylitiedostoa, sisäistä tyylielementtiä tai inline-tyylejä. Ulkoinen tyylitiedosto on yleensä paras vaihtoehto.<br>
<em>(sii-hen on kol-me ta-paa. voit käyt-tää ul-kois-ta tyy-li-tie-dos-to-a, si-säis-tä tyy-li-e-le-ment-ti-ä tai in-line-tyy-le-jä. ul-koi-nen tyy-li-tie-dos-to on y-leen-sä pa-ras vaih-to-eh-to.)</em></p>

<p><strong>Opiskelija</strong>: Miten ulkoinen tyylitiedosto liitetään?<br>
<em>(mi-ten ul-koi-nen tyy-li-tie-dos-to lii-te-tään?)</em></p>

<p><strong>Opiskelija</strong>: Entä yleisimmät CSS-ominaisuudet? Mitä niillä voi tehdä?<br>
<em>(en-tä y-lei-sim-mät CSS-o-mi-nai-suu-det? mi-tä niil-lä voi teh-dä?)</em></p>

<p><strong>Opettaja</strong>: Yleisiä ominaisuuksia ovat esimerkiksi `color` tekstin värille, `background-color` taustavärille, `font-family` fontille, `font-size` fontin koolle, `margin` ulkoreunuksille ja `padding` sisäreunuksille. Näillä voit jo muuttaa sivun ulkoasua paljon.<br>
<em>(y-lei-si-ä o-mi-nai-suuk-si-a o-vat e-si-mer-kik-si co-lor teks-tin vä-ril-le, back-ground-co-lor taus-ta-vä-ril-le, font-fa-mi-ly fon-til-le, font-si-ze fon-tin kool-le, mar-gin ul-ko-reu-nuk-sil-le ja pad-ding si-sä-reu-nuk-sil-le. näil-lä voit jo muut-taa si-vun ul-ko-a-su-a pal-jon.)</em></p>

<p><strong>Opiskelija</strong>: Miten voin valita tietyn elementin, kuten otsikon?<br>
<em>(mi-ten voin va-li-ta tie-tyn e-le-men-tin, ku-ten ot-si-kon?)</em></p>

<p><strong>Opettaja</strong>: Voit käyttää elementtivalitsinta, kuten `h1` kaikille pääotsikoille. Voit myös käyttää luokkavalitsinta, joka alkaa pisteellä, kuten `.otsikko`, tai id-valitsinta, joka alkaa risuaidalla, kuten `#pääotsikko`.<br>
<em>(voit käyt-tää e-le-ment-ti-va-lit-sin-ta, ku-ten h1 kai-kil-le pää-ot-si-koil-le. voit my-ös käyt-tää luok-ka-va-lit-sin-ta, jo-ka al-kaa pis-teel-lä, ku-ten .ot-sik-ko, tai id-va-lit-sin-ta, jo-ka al-kaa ri-su-ai-dal-la, ku-ten #pää-ot-sik-ko.)</em></p>

<p><strong>Opiskelija</strong>: Miten luokka ja id lisätään HTML-elementtiin?<br>
<em>(mi-ten luok-ka ja id li-sä-tään HTML-e-le-ment-tiin?)</em></p>

<p><strong>Opiskelija</strong>: Entä jos haluan muuttaa elementin sijaintia sivulla?<br>
<em>(en-tä jos ha-lu-an muut-taa e-le-men-tin si-jain-ti-a si-vul-la?)</em></p>

<p><strong>Opettaja</strong>: Siihen voit käyttää `position`-ominaisuutta, jolla on arvoja kuten `static`, `relative`, `absolute` ja `fixed`. Voit myös käyttää `float`-ominaisuutta tai modernimpia asettelumenetelmiä kuten Flexbox tai Grid.<br>
<em>(sii-hen voit käyt-tää po-si-tion-o-mi-nai-suut-ta, jol-la on ar-vo-ja ku-ten sta-tic, re-la-ti-ve, ab-so-lu-te ja fi-xed. voit my-ös käyt-tää float-o-mi-nai-suut-ta tai mo-der-nim-pi-a a-set-te-lu-me-ne-tel-mi-ä ku-ten flex-box tai grid.)</em></p>

<p><strong>Opiskelija</strong>: Miten voin tehdä responsiivisen sivun, joka toimii hyvin eri laitteilla?<br>
<em>(mi-ten voin teh-dä res-pon-sii-vi-sen si-vun, jo-ka toi-mii hy-vin e-ri lait-teil-la?)</em></p>

<p><strong>Opiskelija</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tyylitellä aiemmin tekemääni HTML-sivua?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la tyy-li-tel-lä ai-em-min te-ke-mää-ni HTML-si-vu-a?)</em></p>

<p><strong>Opettaja</strong>: Ehdottomasti! Aloita perustyyleistä, kuten väreistä ja fonteista, ja siirry sitten monimutkaisempiin asetteluihin. Muista, että CSS:n oppiminen vie aikaa, mutta harjoittelemalla tulet paremmaksi.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta pe-rus-tyy-leis-tä, ku-ten vä-reis-tä ja fon-teis-ta, ja siir-ry sit-ten mo-ni-mut-kai-sem-piin a-set-te-lui-hin. muis-ta, et-tä CSS:n op-pi-mi-nen vie ai-kaa, mut-ta har-joit-te-le-mal-la tu-let pa-rem-mak-si.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos paljon tästä oppitunnista! Nyt ymmärrän, miten voin tehdä verkkosivuistani kauniimpia CSS:n avulla.<br>
<em>(kii-tos pal-jon täs-tä op-pi-tun-nis-ta! nyt ym-mär-rän, mi-ten voin teh-dä verk-ko-si-vuis-ta-ni kau-niim-pi-a CSS:n a-vul-la.)</em></p>
</div>

### Cultural Notes:
- Finnish design is known for its minimalism and functionality, which often translates to clean CSS styling
- Finnish tech education emphasizes both practical skills and theoretical understanding
- In Finland, web accessibility is not just good practice but often a legal requirement
- Finnish web designers often value white space and clean typography, reflecting Nordic design principles
- The Finnish tech community actively follows and contributes to modern web development practices


