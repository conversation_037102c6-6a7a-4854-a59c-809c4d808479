#!/usr/bin/env python3
"""
Script to add e.stopPropagation() to mobile dropdown handlers
"""

import os
import re

def fix_mobile_dropdown(file_path):
    """Add e.stopPropagation() to mobile dropdown handler"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find and replace the mobile dropdown handler
        old_pattern = r'(\s+)if \(window\.innerWidth <= 767\) \{\s+e\.preventDefault\(\);\s+const dropdown = this\.parentElement;'
        new_replacement = r'\1if (window.innerWidth <= 767) {\n\1    e.preventDefault();\n\1    e.stopPropagation();\n\1    const dropdown = this.parentElement;'
        
        content = re.sub(old_pattern, new_replacement, content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed mobile dropdown: {file_path}")
            return True
        else:
            print(f"- Already fixed or no changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def find_all_html_files():
    """Find all HTML files in finnish_grammar directory"""
    html_files = []
    
    # Find all HTML files recursively
    for root, dirs, files in os.walk('finnish_grammar'):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    return html_files

def main():
    """Main function"""
    print("Adding e.stopPropagation() to mobile dropdown handlers...")
    
    html_files = find_all_html_files()
    updated_count = 0
    
    for file_path in html_files:
        if fix_mobile_dropdown(file_path):
            updated_count += 1
    
    print(f"\nFixed {updated_count} files")
    print(f"Total files checked: {len(html_files)}")

if __name__ == "__main__":
    main()
