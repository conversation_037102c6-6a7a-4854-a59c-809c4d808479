# Chapter 38: Tietokannat ja SQL / Databases & SQL

## Objectives / Tavoitteet
- Learn vocabulary related to databases and SQL in Finnish
- Understand how to discuss database concepts and operations
- Be able to explain basic SQL queries and database structures
- Master basic conversations about storing and retrieving data

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Tietokanta - Database
2. Taulu - Table
3. Rivi - Row
4. Sarake - Column
5. Avain - Key
6. Kysely - Query
7. Hakea - To retrieve/fetch
8. <PERSON>sätä - To insert/add
9. P<PERSON><PERSON>itt<PERSON><PERSON> - To update
10. Poistaa - To delete
11. Yhteys - Connection
12. Relaatio - Relation
13. Indeksi - Index
14. Viiteavain - Foreign key
15. Pääavain - Primary key

## Grammar Points / Kielioppi
1. **Technical Verbs for Database Operations**:
   - Action verbs for data manipulation
   - Example: Haen tietoja tietokannasta. (I retrieve data from the database.)

2. **Conditional Forms for Database Logic**:
   - Expressing database conditions
   - Example: <PERSON><PERSON> on olemassa, päivittäisimme tiedot. (If the user exists, we would update the information.)

3. **Inessive Case (-ssa/-ssä) for Database Contexts**:
   - In database structures
   - Example: Taulussa on useita sarakkeita. (There are several columns in the table.)

4. **Elative Case (-sta/-stä) for Data Sources**:
   - From database sources
   - Example: Tiedot haetaan asiakastaulusta. (The information is retrieved from the customer table.)

5. **Illative Case (-Vn) for Data Destinations**:
   - Into database structures
   - Example: Tiedot tallennetaan tietokantaan. (The data is saved into the database.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Database and SQL workshop / Tietokanta- ja SQL-työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa tietokantojen ja SQL:n työpajaan! Tänään opimme, miten tietokantoja käytetään tiedon tallentamiseen ja hakemiseen.<br>
<em>(ter-ve-tu-lo-a tie-to-kan-to-jen ja SQL:n työ-pa-jaan! tä-nään o-pim-me, mi-ten tie-to-kan-to-ja käy-te-tään tie-don tal-len-ta-mi-seen ja ha-ke-mi-seen.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen kuullut tietokannoista, mutta en ole käyttänyt niitä aiemmin.<br>
<em>(kii-tos! o-len kuul-lut tie-to-kan-nois-ta, mut-ta en o-le käyt-tä-nyt nii-tä ai-em-min.)</em></p>

<p><strong>Ohjaaja</strong>: Aloitetaan perusteista. Tietokanta on järjestelmä, joka mahdollistaa tiedon järjestelmällisen tallentamisen, hakemisen ja hallinnan. Relaatiotietokannat ovat yleisimpiä, ja niissä tieto järjestetään tauluihin, jotka voivat olla yhteydessä toisiinsa.<br>
<em>(a-loi-te-taan pe-rus-teis-ta. tie-to-kan-ta on jär-jes-tel-mä, jo-ka mah-dol-lis-taa tie-don jär-jes-tel-mäl-li-sen tal-len-ta-mi-sen, ha-ke-mi-sen ja hal-lin-nan. re-laa-ti-o-tie-to-kan-nat o-vat y-lei-sim-pi-ä, ja niis-sä tie-to jär-jes-te-tään tau-lui-hin, jot-ka voi-vat ol-la yh-tey-des-sä toi-siin-sa.)</em></p>

<p><strong>Osallistuja</strong>: Mitä ovat taulut ja miten ne toimivat?<br>
<em>(mi-tä o-vat tau-lut ja mi-ten ne toi-mi-vat?)</em></p>

<p><strong>Ohjaaja</strong>: Taulu on kuin taulukko, jossa on rivejä ja sarakkeita. Jokainen sarake määrittelee tietyn tietotyypin, kuten teksti, numero tai päivämäärä. Jokainen rivi edustaa yhtä tietuetta. Esimerkiksi "käyttäjät"-taulussa voisi olla sarakkeita kuten id, nimi, sähköposti ja salasana, ja jokainen rivi edustaisi yhtä käyttäjää.<br>
<em>(tau-lu on kuin tau-luk-ko, jos-sa on ri-ve-jä ja sa-rak-kei-ta. jo-kai-nen sa-ra-ke mää-rit-te-lee tie-tyn tie-to-tyy-pin, ku-ten teks-ti, nu-me-ro tai päi-vä-mää-rä. jo-kai-nen ri-vi e-dus-taa yh-tä tie-tu-et-ta. e-si-mer-kik-si "käyt-tä-jät"-tau-lus-sa voi-si ol-la sa-rak-kei-ta ku-ten id, ni-mi, säh-kö-pos-ti ja sa-la-sa-na, ja jo-kai-nen ri-vi e-dus-tai-si yh-tä käyt-tä-jää.)</em></p>

<p><strong>Osallistuja</strong>: Miten tauluja luodaan?<br>
<em>(mi-ten tau-lu-ja luo-daan?)</em></p>

<p><strong>Osallistuja</strong>: Entä miten tietoja lisätään tauluun?<br>
<em>(en-tä mi-ten tie-to-ja li-sä-tään tau-luun?)</em></p>

<p><strong>Osallistuja</strong>: Miten tietoja haetaan tietokannasta?<br>
<em>(mi-ten tie-to-ja ha-e-taan tie-to-kan-nas-ta?)</em></p>

<p><strong>Osallistuja</strong>: Entä tietojen päivittäminen ja poistaminen?<br>
<em>(en-tä tie-to-jen päi-vit-tä-mi-nen ja pois-ta-mi-nen?)</em></p>

<p><strong>Osallistuja</strong>: Miten taulut voivat olla yhteydessä toisiinsa?<br>
<em>(mi-ten tau-lut voi-vat ol-la yh-tey-des-sä toi-siin-sa?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin hakea tietoja useasta taulusta samanaikaisesti?<br>
<em>(mi-ten voin ha-ke-a tie-to-ja u-se-as-ta tau-lus-ta sa-man-ai-kai-ses-ti?)</em></p>

<p><strong>Osallistuja</strong>: Miten tietokantoja käytetään verkkosovelluksissa?<br>
<em>(mi-ten tie-to-kan-to-ja käy-te-tään verk-ko-so-vel-luk-sis-sa?)</em></p>

<p><strong>Osallistuja</strong>: Mitä tietokantajärjestelmiä on olemassa?<br>
<em>(mi-tä tie-to-kan-ta-jär-jes-tel-mi-ä on o-le-mas-sa?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen tietokannan?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen tie-to-kan-nan?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Voit käyttää esimerkiksi MySQL Workbenchiä tai phpMyAdminia tietokannan luomiseen ja hallintaan. Aloita luomalla yksinkertainen tietokanta, jossa on muutama taulu, ja kokeile sitten erilaisia SQL-kyselyitä. Muista suunnitella tietokannan rakenne huolellisesti ennen toteutusta.<br>
<em>(eh-dot-to-mas-ti! voit käyt-tää e-si-mer-kik-si MySQL Work-ben-chi-ä tai php-My-Ad-mi-ni-a tie-to-kan-nan luo-mi-seen ja hal-lin-taan. a-loi-ta luo-mal-la yk-sin-ker-tai-nen tie-to-kan-ta, jos-sa on muu-ta-ma tau-lu, ja ko-kei-le sit-ten e-ri-lai-si-a SQL-ky-se-ly-i-tä. muis-ta suun-ni-tel-la tie-to-kan-nan ra-ken-ne huo-lel-li-ses-ti en-nen to-teu-tus-ta.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten tietokantoja käytetään ja miten SQL-kyselyjä kirjoitetaan.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten tie-to-kan-to-ja käy-te-tään ja mi-ten SQL-ky-se-ly-jä kir-joi-te-taan.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong database expertise, with many companies specializing in database management and development
- Finnish education system emphasizes database design and SQL skills as fundamental for IT professionals
- Many Finnish public services rely on well-designed databases for efficient operation
- Data privacy and security are particularly important in Finnish database design due to strict privacy laws
- Finnish tech companies often use both traditional relational databases and modern NoSQL solutions depending on the use case


