# Chapter 40: Node.js-perusteet / Node.js Basics

## Objectives / Tavoitteet
- Learn vocabulary related to Node.js development in Finnish
- Understand how to discuss server-side JavaScript programming
- Be able to explain Node.js concepts and functionality
- Master basic conversations about creating web applications with Node.js

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Asynkroninen - Asynchronous
2. <PERSON><PERSON><PERSON><PERSON><PERSON>h<PERSON>inen - Event-driven
3. Mo<PERSON>uli - Module
4. Paketti - Package
5. Riippuvuus - Dependency
6. <PERSON><PERSON><PERSON><PERSON><PERSON> - Callback
7. Lupaus - Promise
8. Reititys - Routing
9. Palvelin - Server
10. Pääteohjelma - Terminal
11. Tiedostojärjestelmä - File system
12. Puskuri - Buffer
13. Virta - Stream
14. Väliohjelmisto - Middleware
15. Ympäristömuuttuja - Environment variable

## Grammar Points / Kielioppi
1. **Technical Verbs for Node.js Operations**:
   - Action verbs for server programming
   - Example: <PERSON><PERSON><PERSON><PERSON><PERSON> HTTP-pyyntöjä Node.js:llä. (I handle HTTP requests with Node.js.)

2. **Conditional Forms for Node.js Logic**:
   - Expressing programming conditions
   - Example: Jos palvelin käynnistyisi, sovellus alkaisi kuunnella pyyntöjä. (If the server would start, the application would begin listening for requests.)

3. **Inessive Case (-ssa/-ssä) for Node.js Contexts**:
   - In programming environments
   - Example: Node.js-ympäristössä JavaScript toimii palvelimella. (In the Node.js environment, JavaScript runs on the server.)

4. **Elative Case (-sta/-stä) for Node.js Data Sources**:
   - From data sources
   - Example: Tiedot haetaan tiedostosta. (The data is retrieved from the file.)

5. **Translative Case (-ksi) for Node.js Transformations**:
   - Converting data formats
   - Example: Muunnan datan JSON-muotoon. (I convert the data to JSON format.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Node.js programming workshop / Node.js-ohjelmoinnin työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa Node.js-perusteiden työpajaan! Tänään opimme, miten Node.js:ää käytetään palvelinpuolen JavaScript-ohjelmointiin.<br>
<em>(ter-ve-tu-lo-a Node.js-pe-rus-tei-den työ-pa-jaan! tä-nään o-pim-me, mi-ten Node.js:ää käy-te-tään pal-ve-lin-puo-len ja-va-script-oh-jel-moin-tiin.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen käyttänyt JavaScriptiä selainpuolella, mutta en ole kokeillut Node.js:ää.<br>
<em>(kii-tos! o-len käyt-tä-nyt ja-va-scrip-ti-ä se-lain-puo-lel-la, mut-ta en o-le ko-keil-lut Node.js:ää.)</em></p>

<p><strong>Ohjaaja</strong>: Hienoa! Node.js on JavaScript-ajoympäristö, joka mahdollistaa JavaScriptin suorittamisen palvelimella. Se käyttää Googlen V8 JavaScript -moottoria ja on erityisen hyvä asynkronisiin, tapahtumapohjaisia sovelluksia varten.<br>
<em>(hie-no-a! Node.js on ja-va-script-a-jo-ym-pä-ris-tö, jo-ka mah-dol-lis-taa ja-va-scrip-tin suo-rit-ta-mi-sen pal-ve-li-mel-la. se käyt-tää goog-len V8 ja-va-script -moot-to-ri-a ja on e-ri-tyi-sen hy-vä a-synk-ro-ni-siin, ta-pah-tu-ma-poh-jai-si-a so-vel-luk-si-a var-ten.)</em></p>

<p><strong>Osallistuja</strong>: Miten Node.js asennetaan?<br>
<em>(mi-ten Node.js a-sen-ne-taan?)</em></p>

<p><strong>Osallistuja</strong>: Miten luon ensimmäisen Node.js-sovelluksen?<br>
<em>(mi-ten lu-on en-sim-mäi-sen Node.js-so-vel-luk-sen?)</em></p>

<p><strong>Osallistuja</strong>: Miten luon yksinkertaisen web-palvelimen Node.js:llä?<br>
<em>(mi-ten lu-on yk-sin-ker-tai-sen web-pal-ve-li-men Node.js:l-lä?)</em></p>

<p><strong>Osallistuja</strong>: Miten Node.js-projekteja hallitaan?<br>
<em>(mi-ten Node.js-pro-jek-te-ja hal-li-taan?)</em></p>

<p><strong>Osallistuja</strong>: Miten asennan ja käytän ulkoisia paketteja?<br>
<em>(mi-ten a-sen-nan ja käy-tän ul-koi-si-a pa-ket-te-ja?)</em></p>

<p><strong>Osallistuja</strong>: Miten käsittelen asynkronisia operaatioita Node.js:ssä?<br>
<em>(mi-ten kä-sit-te-len a-synk-ro-ni-si-a o-pe-raa-ti-oi-ta Node.js:s-sä?)</em></p>

<p><strong>Osallistuja</strong>: Miten käsittelen tiedostoja Node.js:llä?<br>
<em>(mi-ten kä-sit-te-len tie-dos-to-ja Node.js:l-lä?)</em></p>

<p><strong>Osallistuja</strong>: Miten luon REST API:n Node.js:llä?<br>
<em>(mi-ten lu-on REST API:n Node.js:l-lä?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen Node.js-sovelluksen?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen Node.js-so-vel-luk-sen?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloitetaan luomalla uusi Node.js-projekti ja yksinkertainen web-palvelin Express.js:llä. Tämä antaa sinulle hyvän pohjan, josta voit laajentaa sovellusta tarpeidesi mukaan. Muista, että Node.js-kehitys on iteratiivista, joten aloita pienestä ja lisää ominaisuuksia vähitellen.<br>
<em>(eh-dot-to-mas-ti! a-loi-te-taan luo-mal-la uu-si Node.js-pro-jek-ti ja yk-sin-ker-tai-nen web-pal-ve-lin Express.js:l-lä. tä-mä an-taa si-nul-le hy-vän poh-jan, jos-ta voit laa-jen-taa so-vel-lus-ta tar-pei-de-si mu-kaan. muis-ta, et-tä Node.js-ke-hi-tys on i-te-ra-tii-vis-ta, jo-ten a-loi-ta pie-nes-tä ja li-sää o-mi-nai-suuk-si-a vä-hi-tel-len.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten Node.js toimii ja miten voin käyttää sitä palvelinpuolen JavaScript-ohjelmointiin.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten Node.js toi-mii ja mi-ten voin käyt-tää si-tä pal-ve-lin-puo-len ja-va-script-oh-jel-moin-tiin.)</em></p>
</div>

### Cultural Notes:
- Finland has a vibrant Node.js development community, with many startups and tech companies using it for backend development
- Finnish developers often appreciate Node.js for its efficiency and the ability to use JavaScript throughout the stack
- Many Finnish tech meetups and conferences feature Node.js topics and workshops
- Finnish educational institutions increasingly include Node.js in their web development curricula
- The Finnish tech ecosystem values the scalability and performance that Node.js offers for modern web applications


