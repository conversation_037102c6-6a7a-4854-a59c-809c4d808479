const express = require('express');
const fs = require('fs');
const path = require('path');
const cors = require('cors');
const app = express();
const port = 3000;

// Enable CORS for all routes
app.use(cors());

// Serve static files from the current directory
app.use(express.static(__dirname));

// API endpoint to get chapter content
app.get('/api/chapter/:category/:chapterNumber', (req, res) => {
    const { category, chapterNumber } = req.params;
    
    // Map category parameter to directory name
    const categoryDirs = {
        'daily-life': 'Daily_Life',
        'web-development': 'Web_Development_IT',
        'cleaner': 'Cleaner',
        'kitchen-assistant': 'Kitchen_Assistant',
        'warehouse': 'Warehouse'
    };
    
    const categoryDir = categoryDirs[category];
    
    if (!categoryDir) {
        return res.status(404).send('Category not found');
    }
    
    // Format chapter number with leading zeros if needed
    const paddedNumber = chapterNumber.toString().padStart(2, '0');
    
    // Determine chapter file name pattern based on category
    let chapterFilePattern;
    
    switch(category) {
        case 'daily-life':
            chapterFilePattern = `chapter${paddedNumber}_`;
            break;
        case 'web-development':
            chapterFilePattern = `chapter${chapterNumber}_`;
            break;
        case 'cleaner':
            chapterFilePattern = `chapter${chapterNumber}_`;
            break;
        case 'kitchen-assistant':
            chapterFilePattern = `chapter${chapterNumber}_`;
            break;
        case 'warehouse':
            chapterFilePattern = `chapter${chapterNumber}_`;
            break;
        default:
            chapterFilePattern = `chapter${paddedNumber}_`;
    }
    
    // Directory path
    const dirPath = path.join(__dirname, 'chapters', categoryDir);
    
    try {
        // Read the directory
        const files = fs.readdirSync(dirPath);
        
        // Find the file that matches the pattern
        const chapterFile = files.find(file => 
            file.startsWith(chapterFilePattern) && file.endsWith('.md')
        );
        
        if (!chapterFile) {
            return res.status(404).send(`Chapter file not found for ${chapterNumber} in ${categoryDir}`);
        }
        
        // Read the file content
        const filePath = path.join(dirPath, chapterFile);
        const content = fs.readFileSync(filePath, 'utf8');
        
        // Send the content
        res.send(content);
    } catch (error) {
        console.error('Error reading chapter file:', error);
        res.status(500).send('Error reading chapter file: ' + error.message);
    }
});

// Route for chapter page
app.get('/chapter', (req, res) => {
    res.sendFile(path.join(__dirname, 'chapter.html'));
});

// Start the server
app.listen(port, () => {
    console.log(`Server running at http://localhost:${port}`);
    console.log(`Access the website at http://localhost:${port}/index.html`);
});