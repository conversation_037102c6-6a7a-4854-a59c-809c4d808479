<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Page Not Found - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
    <style>
        :root {
            --highlight-color: #ffff99;
        }
        
        [data-theme="dark"] {
            --highlight-color: #665500;
        }
        
        .error-container {
            text-align: center;
            padding: 50px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .error-code {
            font-size: 120px;
            color: #0066CC;
            margin-bottom: 20px;
        }
        .error-message {
            font-size: 24px;
            margin-bottom: 30px;
        }
        .home-button {
            display: inline-block;
            padding: 12px 24px;
            background-color: #0066CC;
            color: white;
            text-decoration: none;
            border-radius: 4px;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .home-button:hover {
            background-color: #0055AA;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
        </div>
    </nav>

    <div class="main-content">
        <div class="error-container">
            <div class="error-code">404</div>
            <div class="error-message">Oops! Page not found.</div>
            <p>The page you are looking for might have been removed, had its name changed, or is temporarily unavailable.</p>
            <a href="index.html" class="home-button">
                <i class="fas fa-home"></i> Go to Homepage
            </a>
        </div>
    </div>

    <script>
        // Redirect to homepage after 5 seconds
        setTimeout(function() {
            window.location.href = 'index.html';
        }, 5000);
    </script>
</body>
</html>
