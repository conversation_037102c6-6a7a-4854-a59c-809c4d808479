﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Type 3 Nouns - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Nouns</a>
            <span class="separator">></span>
            <span>Type 3: Words ending in consonants</span>
        </div>
        
        <section class="grammar-section">
            <h2>Type 3: Words ending in consonants</h2>
            <p>Type 3 nouns in Finnish are words that end in a consonant. These nouns have specific inflection patterns that differ from other noun types. This page explains how to recognize and inflect Type 3 nouns.</p>
        </section>

        <section class="grammar-category">
            <h3>CHARACTERISTICS OF TYPE 3 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Type 3 nouns have the following characteristics:</p>
                <ul>
                    <li>They end in a consonant in the nominative singular form (typically -n, -s, -l, -r)</li>
                    <li>They add a vowel (usually -e-) before case endings in most inflected forms</li>
                    <li>Common examples include: avain (key), kysymys (question), nainen (woman), mies (man)</li>
                </ul>
                
                <p>This type includes many common Finnish words, including many terms for people, abstract concepts, and objects.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">avain</span> <span class="english">key</span></p>
                    <p><span class="finnish">kysymys</span> <span class="english">question</span></p>
                    <p><span class="finnish">nainen</span> <span class="english">woman</span></p>
                    <p><span class="finnish">mies</span> <span class="english">man</span></p>
                    <p><span class="finnish">työtön</span> <span class="english">unemployed person</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SUBTYPES OF TYPE 3 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Type 3 nouns can be divided into several subtypes based on their endings:</p>
                
                <h4>1. Words ending in -in</h4>
                <div class="grammar-example">
                    <p><span class="finnish">avain (key) → avaimen (of the key)</span></p>
                    <p><span class="finnish">puhelin (telephone) → puhelimen (of the telephone)</span></p>
                </div>
                
                <h4>2. Words ending in -ton/-tön</h4>
                <div class="grammar-example">
                    <p><span class="finnish">työtön (unemployed) → työttömän (of the unemployed)</span></p>
                    <p><span class="finnish">onneton (unhappy) → onnettoman (of the unhappy)</span></p>
                </div>
                
                <h4>3. Words ending in -nen</h4>
                <div class="grammar-example">
                    <p><span class="finnish">nainen (woman) → naisen (of the woman)</span></p>
                    <p><span class="finnish">punainen (red) → punaisen (of the red)</span></p>
                </div>
                
                <h4>4. Words ending in -s</h4>
                <div class="grammar-example">
                    <p><span class="finnish">kysymys (question) → kysymyksen (of the question)</span></p>
                    <p><span class="finnish">vastaus (answer) → vastauksen (of the answer)</span></p>
                </div>
                
                <h4>5. Words ending in -t</h4>
                <div class="grammar-example">
                    <p><span class="finnish">olut (beer) → oluen (of the beer)</span></p>
                    <p><span class="finnish">kevät (spring) → kevään (of the spring)</span></p>
                </div>
                
                <h4>6. Words ending in -ut/-yt</h4>
                <div class="grammar-example">
                    <p><span class="finnish">olut (beer) → oluen (of the beer)</span></p>
                    <p><span class="finnish">lyhyt (short) → lyhyen (of the short)</span></p>
                </div>
                
                <h4>7. Words ending in -is</h4>
                <div class="grammar-example">
                    <p><span class="finnish">valmis (ready) → valmiin (of the ready)</span></p>
                    <p><span class="finnish">kaunis (beautiful) → kauniin (of the beautiful)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INFLECTION PATTERNS</h3>
            
            <div class="grammar-content">
                <p>The key feature of Type 3 nouns is that they add a vowel (usually -e-) before case endings in most inflected forms. Here are some examples of inflection patterns for different subtypes:</p>
                
                <h4>Words ending in -in (e.g., avain - key)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>avain</td>
                        <td>avaimet</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>avaimen</td>
                        <td>avainten / avaimien</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>avainta</td>
                        <td>avaimia</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>avaimessa</td>
                        <td>avaimissa</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>avaimeen</td>
                        <td>avaimiin</td>
                    </tr>
                </table>
                
                <h4>Words ending in -nen (e.g., nainen - woman)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>nainen</td>
                        <td>naiset</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>naisen</td>
                        <td>naisten</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>naista</td>
                        <td>naisia</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>naisessa</td>
                        <td>naisissa</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>naiseen</td>
                        <td>naisiin</td>
                    </tr>
                </table>
                
                <h4>Words ending in -s (e.g., kysymys - question)</h4>
                <table class="grammar-table">
                    <tr>
                        <th>Case</th>
                        <th>Singular</th>
                        <th>Plural</th>
                    </tr>
                    <tr>
                        <td>Nominative</td>
                        <td>kysymys</td>
                        <td>kysymykset</td>
                    </tr>
                    <tr>
                        <td>Genitive</td>
                        <td>kysymyksen</td>
                        <td>kysymysten / kysymyksien</td>
                    </tr>
                    <tr>
                        <td>Partitive</td>
                        <td>kysymystä</td>
                        <td>kysymyksiä</td>
                    </tr>
                    <tr>
                        <td>Inessive</td>
                        <td>kysymyksessä</td>
                        <td>kysymyksissä</td>
                    </tr>
                    <tr>
                        <td>Illative</td>
                        <td>kysymykseen</td>
                        <td>kysymyksiin</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION</h3>
            
            <div class="grammar-content">
                <p>Many Type 3 nouns undergo consonant gradation. This means that certain consonants change in different forms of the word.</p>
                
                <p>For example, with the word "kysymys" (question):</p>
                <ul>
                    <li>The strong grade is "kysymys" (as in "kysymys")</li>
                    <li>The weak grade is "kysymykse-" (as in "kysymyksen")</li>
                </ul>
                
                <p>Another example with "avain" (key):</p>
                <ul>
                    <li>The strong grade is "avain" (as in "avain")</li>
                    <li>The weak grade is "avaime-" (as in "avaimen")</li>
                </ul>
                
                <p>Not all Type 3 nouns undergo consonant gradation. It depends on the specific consonants in the word.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">nainen (woman) → naisen (of the woman)</span> <span class="english">n → s (special change)</span></p>
                    <p><span class="finnish">työtön (unemployed) → työttömän (of the unemployed)</span> <span class="english">tön → tömä (special change)</span></p>
                    <p><span class="finnish">olut (beer) → oluen (of the beer)</span> <span class="english">t → e (special change)</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES OF TYPE 3 NOUNS</h3>
            
            <div class="grammar-content">
                <p>Here are some common Type 3 nouns and their basic forms:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Nominative</th>
                        <th>Genitive</th>
                        <th>Partitive</th>
                        <th>Meaning</th>
                    </tr>
                    <tr>
                        <td>avain</td>
                        <td>avaimen</td>
                        <td>avainta</td>
                        <td>key</td>
                    </tr>
                    <tr>
                        <td>puhelin</td>
                        <td>puhelimen</td>
                        <td>puhelinta</td>
                        <td>telephone</td>
                    </tr>
                    <tr>
                        <td>nainen</td>
                        <td>naisen</td>
                        <td>naista</td>
                        <td>woman</td>
                    </tr>
                    <tr>
                        <td>punainen</td>
                        <td>punaisen</td>
                        <td>punaista</td>
                        <td>red</td>
                    </tr>
                    <tr>
                        <td>kysymys</td>
                        <td>kysymyksen</td>
                        <td>kysymystä</td>
                        <td>question</td>
                    </tr>
                    <tr>
                        <td>vastaus</td>
                        <td>vastauksen</td>
                        <td>vastausta</td>
                        <td>answer</td>
                    </tr>
                    <tr>
                        <td>työtön</td>
                        <td>työttömän</td>
                        <td>työtöntä</td>
                        <td>unemployed</td>
                    </tr>
                    <tr>
                        <td>olut</td>
                        <td>oluen</td>
                        <td>olutta</td>
                        <td>beer</td>
                    </tr>
                    <tr>
                        <td>kevät</td>
                        <td>kevään</td>
                        <td>kevättä</td>
                        <td>spring</td>
                    </tr>
                    <tr>
                        <td>mies</td>
                        <td>miehen</td>
                        <td>miestä</td>
                        <td>man</td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE EXAMPLES</h3>
            
            <div class="grammar-content">
                <p>Here are some examples of Type 3 nouns used in sentences:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Missä avain on?</span> <span class="english">Where is the key?</span></p>
                    <p><span class="finnish">Minulla on tärkeä kysymys.</span> <span class="english">I have an important question.</span></p>
                    <p><span class="finnish">Nainen kävelee kadulla.</span> <span class="english">The woman is walking on the street.</span></p>
                    <p><span class="finnish">Pidän punaisesta autosta.</span> <span class="english">I like the red car.</span></p>
                    <p><span class="finnish">Mies juo olutta.</span> <span class="english">The man is drinking beer.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















