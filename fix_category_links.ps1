$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
  
  # Calculate the correct relative path prefix based on file depth
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $folderDepth = ($filePath.Split("\").Length - 1)
  $prefix = "../" * $folderDepth
  
  # Replace the category links to point to the main index.html page with the appropriate hash
  $pattern = '(<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>\s*<div class="dropdown-content">\s*)<a href="[^"]*#daily-life">Daily Life</a>\s*<a href="[^"]*#web-development">Web Development</a>\s*<a href="[^"]*#cleaner">Cleaner</a>\s*<a href="[^"]*#kitchen-assistant">Kitchen Assistant</a>\s*<a href="[^"]*#warehouse">Warehouse</a>'
  
  $replacement = "`${1}<a href=`"${prefix}index.html#daily-life`">Daily Life</a>`n                        <a href=`"${prefix}index.html#web-development`">Web Development</a>`n                        <a href=`"${prefix}index.html#cleaner`">Cleaner</a>`n                        <a href=`"${prefix}index.html#kitchen-assistant`">Kitchen Assistant</a>`n                        <a href=`"${prefix}index.html#warehouse`">Warehouse</a>"
  
  $newContent = $content -replace $pattern, $replacement
  
  # Save the modified content back to the file if changes were made
  if ($newContent -ne $originalContent) {
    Set-Content -Path $file -Value $newContent
    Write-Host "Fixed category links in: $file"
  }
}

Write-Host "All category links have been fixed!"