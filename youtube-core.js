/**
 * YouTube Core Module
 * Core functionality for YouTube integration
 */

// YouTube API configuration
const YouTubeCore = (function () {
  // Private variables
  const API_KEYS = [
    "AIzaSyBvYJhD4x5LGYopqgbK75HRV_mVKofLmlA", // Key 1
    "AIzaSyDKi3FrLNB2-FmXxGOU2momFdtDjiJsCKM", // Key 2
    "AIzaSyBH4TfcIfig2Ub1_RiYaunoZi7u0e_pjOw", // Key 3
    "AIzaSyCZQkAx-HKqL4jTeeJNXR8Bm8DVuTmEgnU", // Key 4
    "AIzaSyADrQbyDK4cyaWr1Zb08Jq7k6ouSHYIoYo", // Key 5
    "AIzaSyCTS5rhAER5crnL0vzqHgIBRmlT8cFIYK0", // Key 6
  ];

  let currentApiKeyIndex = 1;
  const MAX_RESULTS = 8;

  // YouTube player objects for each channel
  let youtubePlayers = {};

  // Current playing video ID for each channel
  let currentVideoIds = {};

  // Flag to track if the YouTube API is loaded
  let youtubeApiLoaded = false;

  // Current active channel key
  let currentChannelKey = null;

  // Pagination state
  const paginationState = {};

  // Pending page update information
  let pendingPageUpdate = null;

  // Public methods
  return {
    // Initialize the YouTube integration
    init: function () {
      console.log("Initializing YouTube Core...");

      // Create tabs for all channels
      this.createChannelTabs();

      // Load the YouTube API
      this.loadYouTubeApi();

      // Make global references available
      window.YouTubeCore = this;
      window.paginationState = paginationState;
      window.pendingPageUpdate = pendingPageUpdate;

      // Add event listener to ensure pagination is visible for specified channels
      document.addEventListener("YouTubeTabsCreated", () => {
        console.log(
          "YouTubeTabsCreated event received, ensuring pagination for specified channels"
        );
        this.ensurePaginationForSpecifiedChannels();
      });

      // Also add a window load event to ensure pagination is visible
      window.addEventListener("load", () => {
        console.log(
          "Window loaded, ensuring pagination for specified channels"
        );
        setTimeout(() => {
          this.ensurePaginationForSpecifiedChannels();
        }, 1000); // Delay to ensure everything is loaded
      });

      // Check URL for channel parameter
      const urlParams = new URLSearchParams(window.location.search);
      const channelParam = urlParams.get("channel");

      // Determine which channel to load initially
      let initialChannelKey;
      if (channelParam && window.YOUTUBE_CHANNELS[channelParam]) {
        // Use the channel from URL parameter if it exists
        initialChannelKey = channelParam;
        console.log(`Loading channel from URL parameter: ${initialChannelKey}`);
      } else {
        // Otherwise use the first channel
        initialChannelKey = Object.keys(window.YOUTUBE_CHANNELS)[0];
        console.log(`Loading first channel: ${initialChannelKey}`);
      }

      // Set as current channel
      currentChannelKey = initialChannelKey;

      if (initialChannelKey) {
        // Make sure the correct tab is active and visible
        const channel = window.YOUTUBE_CHANNELS[initialChannelKey];
        if (channel && channel.tabId) {
          const tab = document.getElementById(channel.tabId);
          if (tab) {
            // Hide all tabs first
            document.querySelectorAll(".tab-content").forEach((t) => {
              t.classList.remove("active");
              t.style.display = "none";
            });

            // Show the selected tab
            tab.classList.add("active");
            tab.style.display = "block";
            tab.style.width = "100%";
            tab.style.position = "relative";
            tab.style.overflow = "visible";
          }
        }

        // Create player layout for the selected channel
        this.createPlayerLayout(initialChannelKey);

        // Fetch videos with a small delay
        setTimeout(() => {
          this.fetchChannelVideos(initialChannelKey);

          // Force layout recalculation
          const playerLayout = document.getElementById(
            `youtube-player-layout-${initialChannelKey}`
          );
          if (playerLayout) {
            playerLayout.style.display = "flex";
          }

          // Dispatch an event to notify that the initial channel has been loaded
          const event = new CustomEvent("YouTubeInitialChannelLoaded", {
            detail: { channelKey: initialChannelKey },
          });
          document.dispatchEvent(event);
        }, 500);
      }
    },

    // Get the current API key
    getApiKey: function () {
      return API_KEYS[currentApiKeyIndex];
    },

    // Rotate to the next API key
    rotateApiKey: function () {
      currentApiKeyIndex = (currentApiKeyIndex + 1) % API_KEYS.length;
      return this.getApiKey();
    },

    // Handle API requests with key rotation
    fetchWithKeyRotation: function (url, retryCount = 0) {
      return fetch(url).then((response) => {
        if (!response.ok) {
          // If we get a 403 error (Forbidden), it might be due to quota limits
          if (response.status === 403 && retryCount < API_KEYS.length - 1) {
            console.log(
              `API key quota exceeded (403 error). Rotating to next key...`
            );
            // Rotate to the next API key
            this.rotateApiKey();
            // Update the URL with the new API key
            const newUrl = url.replace(/key=[^&]+/, `key=${this.getApiKey()}`);
            console.log(
              `Retrying with new API key (attempt ${retryCount + 1}/${
                API_KEYS.length
              })`
            );
            // Retry the request with the new key
            return this.fetchWithKeyRotation(newUrl, retryCount + 1);
          }
          throw new Error(
            `Network response was not ok: ${response.status} ${response.statusText}`
          );
        }
        return response.json();
      });
    },

    // Create tabs for all configured YouTube channels
    createChannelTabs: function () {
      // Get the tab buttons container
      const tabButtons = document.querySelector(".video-tab-buttons");
      if (!tabButtons) {
        console.error("Tab buttons container not found");
        return;
      }

      // Get the tabs content container
      const tabsContainer = document.querySelector(".video-tabs-content");
      if (!tabsContainer) {
        console.error("Tabs content container not found");
        return;
      }

      // Create tabs for each channel
      console.log(
        "Creating tabs for channels:",
        Object.keys(window.YOUTUBE_CHANNELS)
      );

      Object.keys(window.YOUTUBE_CHANNELS).forEach((channelKey, index) => {
        const channel = window.YOUTUBE_CHANNELS[channelKey];

        // Create a new button for the channel
        const channelButton = document.createElement("button");
        channelButton.className = "video-tab-button";
        channelButton.textContent = channel.name;
        channelButton.dataset.channelKey = channelKey;

        // Check if this channel should be active based on URL or default to first
        const urlParams = new URLSearchParams(window.location.search);
        const channelParam = urlParams.get("channel");

        if (
          (channelParam && channelParam === channelKey) ||
          (!channelParam && index === 0)
        ) {
          channelButton.classList.add("active");
          // Set as current channel
          currentChannelKey = channelKey;
        }

        channelButton.onclick = (event) => {
          // Set current channel
          currentChannelKey = channelButton.dataset.channelKey;

          // Open the tab
          this.openTab(event, channel.tabId);

          // Fetch videos if not already loaded
          if (!channelButton.dataset.loaded) {
            this.fetchChannelVideos(currentChannelKey);
            channelButton.dataset.loaded = "true";
          }
        };

        // Add the button to the tab buttons container
        tabButtons.appendChild(channelButton);

        // Create a new tab content for the channel
        const tabContent = document.createElement("div");
        tabContent.id = channel.tabId;
        tabContent.className = "tab-content";

        // Check if this channel should be active based on URL or default to first
        if (
          (channelParam && channelParam === channelKey) ||
          (!channelParam && index === 0)
        ) {
          tabContent.classList.add("active");
          tabContent.style.display = "block";
          tabContent.style.width = "100%";
          tabContent.style.position = "relative";
          tabContent.style.overflow = "visible";
        } else {
          tabContent.style.display = "none";
        }

        // Create loading indicator
        const loadingElement = document.createElement("div");
        loadingElement.id = `loading-indicator-${channelKey}`;
        loadingElement.className = "loading-indicator";
        loadingElement.innerHTML =
          '<i class="fas fa-spinner fa-spin"></i> Loading videos...';

        // Create container for channel info
        const channelInfoContainer = document.createElement("div");
        channelInfoContainer.id = `youtube-channel-container-${channelKey}`;
        channelInfoContainer.className = "youtube-channel-container";

        // Create container for videos
        const videosContainer = document.createElement("div");
        videosContainer.id = `youtube-videos-container-${channelKey}`;
        videosContainer.className = "youtube-videos-container";

        // Add elements to the tab content
        tabContent.appendChild(loadingElement);
        tabContent.appendChild(channelInfoContainer);
        tabContent.appendChild(videosContainer);

        // Add the tab content to the container
        tabsContainer.appendChild(tabContent);
      });

      // Determine which channel to preload based on URL parameter or default to first
      if (Object.keys(window.YOUTUBE_CHANNELS).length > 0) {
        // Check URL for channel parameter
        const urlParams = new URLSearchParams(window.location.search);
        const channelParam = urlParams.get("channel");

        // Determine which channel to load initially
        let initialChannelKey;
        if (channelParam && window.YOUTUBE_CHANNELS[channelParam]) {
          // Use the channel from URL parameter if it exists
          initialChannelKey = channelParam;
          console.log(
            `Preloading channel from URL parameter: ${initialChannelKey}`
          );
        } else {
          // Otherwise use the first channel
          initialChannelKey = Object.keys(window.YOUTUBE_CHANNELS)[0];
          console.log(`Preloading first channel: ${initialChannelKey}`);
        }

        // Mark the selected channel button as loaded
        const channelButton = document.querySelector(
          `.video-tab-button[data-channel-key="${initialChannelKey}"]`
        );
        if (channelButton) {
          channelButton.dataset.loaded = "true";

          // Make sure this button is active
          channelButton.classList.add("active");

          // Make sure all other buttons are not active
          document.querySelectorAll(".video-tab-button").forEach((button) => {
            if (button !== channelButton) {
              button.classList.remove("active");
            }
          });
        }

        // Create player layout for the selected channel
        this.createPlayerLayout(initialChannelKey);

        // Make sure the selected tab is visible
        const tab = document.getElementById(
          window.YOUTUBE_CHANNELS[initialChannelKey].tabId
        );
        if (tab) {
          // Hide all tabs first
          document.querySelectorAll(".tab-content").forEach((t) => {
            t.classList.remove("active");
            t.style.display = "none";
          });

          // Show the selected tab
          tab.classList.add("active");
          tab.style.display = "block";
          tab.style.width = "100%";
          tab.style.position = "relative";
          tab.style.overflow = "visible";
        }

        // Force layout recalculation
        setTimeout(() => {
          const playerLayout = document.getElementById(
            `youtube-player-layout-${initialChannelKey}`
          );
          if (playerLayout) {
            playerLayout.style.display = "flex";
          }
        }, 100);

        // Fetch videos for the selected channel immediately
        this.fetchChannelVideos(initialChannelKey);

        // Dispatch event to notify that tabs have been created
        const tabsCreatedEvent = new CustomEvent("YouTubeTabsCreated");
        document.dispatchEvent(tabsCreatedEvent);
        console.log("YouTubeTabsCreated event dispatched");
      }
    },

    // Open a tab
    openTab: function (event, tabId) {
      console.log(`Opening tab: ${tabId}`);

      // Get the channel key from the event or find it from the tabId
      let channelKey = null;

      if (
        event &&
        event.currentTarget &&
        event.currentTarget.dataset.channelKey
      ) {
        // Get channel key from the button that was clicked
        channelKey = event.currentTarget.dataset.channelKey;
      } else {
        // Try to find the channel key from the tabId
        for (const key in window.YOUTUBE_CHANNELS) {
          if (window.YOUTUBE_CHANNELS[key].tabId === tabId) {
            channelKey = key;
            break;
          }
        }
      }

      console.log(`Channel key for tab ${tabId}: ${channelKey}`);

      // Set current channel key
      if (channelKey) {
        currentChannelKey = channelKey;
      }

      // Pause all currently playing videos
      this.pauseAllVideos();

      // Hide all tab content
      const tabContent = document.getElementsByClassName("tab-content");
      for (let i = 0; i < tabContent.length; i++) {
        tabContent[i].classList.remove("active");
        tabContent[i].style.display = "none";
      }

      // Remove active class from all tab buttons
      const tabButtons = document.getElementsByClassName("video-tab-button");
      for (let i = 0; i < tabButtons.length; i++) {
        tabButtons[i].classList.remove("active");
      }

      // If we have a channel key, make sure the corresponding button is active
      if (channelKey) {
        const channelButton = document.querySelector(
          `.video-tab-button[data-channel-key="${channelKey}"]`
        );
        if (channelButton) {
          channelButton.classList.add("active");
        }
      }

      // Show the current tab and add an "active" class to the button that opened the tab
      const currentTab = document.getElementById(tabId);
      if (currentTab) {
        currentTab.classList.add("active");
        currentTab.style.display = "block";
        currentTab.style.width = "100%";
        currentTab.style.position = "relative";
        currentTab.style.overflow = "visible";

        // Scroll to top of the tab
        window.scrollTo({
          top: 0,
          behavior: "smooth",
        });

        // Force layout recalculation
        setTimeout(() => {
          // Find the player layout in this tab
          const playerLayout = currentTab.querySelector(
            ".youtube-player-layout"
          );
          if (playerLayout) {
            playerLayout.style.display = "flex";
          }

          // No need to set max-height for video list container anymore
        }, 50);
      }

      if (channelKey) {
        console.log(`Updating navigation for channel: ${channelKey}`);

        // Update URL with the channel key without reloading the page
        const newUrl = new URL(window.location.href);
        newUrl.searchParams.set("channel", channelKey);
        // Remove any hash fragment that might be causing confusion
        newUrl.hash = "";
        window.history.pushState(
          { channelKey: channelKey },
          "",
          newUrl.toString()
        );

        // Update the active state in the navigation dropdown
        const navLinks = document.querySelectorAll(
          "#channels-dropdown a[data-channel-key]"
        );
        navLinks.forEach((link) => {
          if (link.getAttribute("data-channel-key") === channelKey) {
            link.classList.add("active");
          } else {
            link.classList.remove("active");
          }
        });

        // Check if this is one of the specified channels that needs pagination
        const isSpecifiedChannel = [
          "finnishcrashcourse",
          "finnishtogo",
          "suomenkurssiyt",
          "yleareena",
          "yleareena2",
          "yleareena3",
          "yleareena4",
          "yleareena5",
          "pipsapossu",
          "katchatsfinnish",
          "kaapowildbrain",
          "kaapowildbrain2",
          "kaapowildbrain3",
          "kaapowildbrain4",
          "ylepikkukakkonen",
          "ylepikkukakkonen2",
          "ylepikkukakkonen3",
          "ylepikkukakkonen4",
          "ylepikkukakkonen5",
          "ylepikkukakkonen6",
        ].includes(channelKey);

        // If this is a specified channel, ensure pagination is visible
        if (isSpecifiedChannel) {
          console.log(
            `Tab opened for specified channel: ${channelKey}, ensuring pagination`
          );
          setTimeout(() => {
            this.ensurePaginationForSpecifiedChannels();
          }, 500); // Small delay to ensure DOM is updated
        }

        // Make sure videos are loaded for this channel
        const channelButton = document.querySelector(
          `.video-tab-button[data-channel-key="${channelKey}"]`
        );
        if (channelButton && !channelButton.dataset.loaded) {
          console.log(`Loading videos for channel: ${channelKey}`);
          this.fetchChannelVideos(channelKey);
          channelButton.dataset.loaded = "true";
        }
      }
    },

    // Pause all currently playing videos
    pauseAllVideos: function () {
      console.log("Pausing all videos");
      // Iterate through all YouTube players and pause them
      Object.keys(youtubePlayers).forEach((channelKey) => {
        const player = youtubePlayers[channelKey];
        if (player && typeof player.pauseVideo === "function") {
          try {
            console.log(`Pausing video for channel: ${channelKey}`);
            player.pauseVideo();
          } catch (e) {
            console.error(`Error pausing video for channel ${channelKey}:`, e);
          }
        }
      });
    },

    // Fetch videos from a YouTube channel
    fetchChannelVideos: function (channelKey, pageToken = null) {
      console.log(
        `Fetching videos for channel: ${channelKey}, pageToken: ${pageToken}`
      );

      const channel = window.YOUTUBE_CHANNELS[channelKey];
      if (!channel) {
        console.error(`Channel ${channelKey} not found in YOUTUBE_CHANNELS`);
        return;
      }

      // Check if this is one of the specified channels that needs pagination
      const isSpecifiedChannel = [
        "finnishcrashcourse",
        "finnishtogo",
        "suomenkurssiyt",
        "yleareena",
        "yleareena2",
        "yleareena3",
        "yleareena4",
        "yleareena5",
        "pipsapossu",
        "katchatsfinnish",
        "kaapowildbrain",
        "kaapowildbrain2",
        "kaapowildbrain3",
        "kaapowildbrain4",
        "ylepikkukakkonen",
        "ylepikkukakkonen2",
        "ylepikkukakkonen3",
        "ylepikkukakkonen4",
        "ylepikkukakkonen5",
        "ylepikkukakkonen6",
      ].includes(channelKey);

      console.log(
        `Channel ${channelKey} is specified channel for pagination: ${isSpecifiedChannel}`
      );

      // Show loading indicator
      const loadingIndicator = document.getElementById(
        `loading-indicator-${channelKey}`
      );
      if (loadingIndicator) {
        loadingIndicator.style.display = "block";
      }

      // Determine if we should use a playlist or search by channel ID
      let apiUrl;
      let type;

      if (channel.playlist && channel.playlist.trim() !== "") {
        // Use playlist API
        apiUrl = `https://www.googleapis.com/youtube/v3/playlistItems?part=snippet&maxResults=${MAX_RESULTS}&playlistId=${
          channel.playlist
        }&key=${this.getApiKey()}`;
        type = "playlist";
        console.log(`Using playlist API for channel: ${channelKey}`);
      } else {
        // Use search API with channel ID
        // Use relevance order for Yle Areena to get more videos
        const orderParam = channelKey === "yleareena" ? "relevance" : "date";
        apiUrl = `https://www.googleapis.com/youtube/v3/search?part=snippet&maxResults=${MAX_RESULTS}&channelId=${
          channel.id
        }&order=${orderParam}&type=video&key=${this.getApiKey()}`;
        type = "search";
        console.log(`Using search API for channel: ${channelKey}`);
      }

      // Add page token if provided
      if (pageToken) {
        apiUrl += `&pageToken=${pageToken}`;
        console.log(`Added page token: ${pageToken}`);
      }

      // Fetch videos from the API
      this.fetchWithKeyRotation(apiUrl)
        .then((data) => {
          // Hide loading indicator
          if (loadingIndicator) {
            loadingIndicator.style.display = "none";
          }

          // Process the videos
          this.processVideos(channelKey, type, data);

          // Add pagination
          this.addPaginationBar(channelKey, type, data);

          // Create player layout if it doesn't exist
          this.createPlayerLayout(channelKey);
        })
        .catch((error) => {
          console.error(
            `Error fetching videos for channel ${channelKey}:`,
            error
          );

          // Hide loading indicator
          if (loadingIndicator) {
            loadingIndicator.style.display = "none";
          }

          // Create fallback pagination with error message
          this.createFallbackPagination(
            channelKey,
            `Failed to load videos. ${error.message}`
          );
        });
    },

    // Process videos from the API response
    processVideos: function (channelKey, type, data) {
      console.log(
        `Processing videos for channel: ${channelKey}, type: ${type}`
      );

      const channel = window.YOUTUBE_CHANNELS[channelKey];
      if (!channel) {
        console.error(`Channel ${channelKey} not found in YOUTUBE_CHANNELS`);
        return;
      }

      // Check if this is one of the specified channels that needs pagination
      const isSpecifiedChannel = [
        "finnishcrashcourse",
        "finnishtogo",
        "suomenkurssiyt",
        "yleareena",
        "yleareena2",
        "yleareena3",
        "yleareena4",
        "yleareena5",
        "pipsapossu",
        "katchatsfinnish",
        "kaapowildbrain",
        "kaapowildbrain2",
        "kaapowildbrain3",
        "kaapowildbrain4",
        "ylepikkukakkonen",
        "ylepikkukakkonen2",
        "ylepikkukakkonen3",
        "ylepikkukakkonen4",
        "ylepikkukakkonen5",
        "ylepikkukakkonen6",
      ].includes(channelKey);

      console.log(
        `Channel ${channelKey} is specified channel for pagination: ${isSpecifiedChannel}`
      );

      // Get the videos container
      const videosContainer = document.getElementById(
        `youtube-videos-container-${channelKey}`
      );
      if (!videosContainer) {
        console.error(`Videos container for channel ${channelKey} not found`);
        return;
      }

      // Clear the container
      videosContainer.innerHTML = "";

      // Create a video list container
      const videoListContainer = document.createElement("div");
      videoListContainer.className = "youtube-video-list-container";
      videoListContainer.style.width = "100%";
      videoListContainer.style.maxHeight = "calc(100vh - 250px)";
      videoListContainer.style.overflowY = "hidden"; // No scrollbar
      videoListContainer.style.overflowX = "hidden";
      videoListContainer.style.paddingRight = "0"; // No need for scrollbar padding

      // Create a video list
      const videoList = document.createElement("div");
      videoList.className = "youtube-video-list";
      videoList.id = `youtube-video-list-${channelKey}`;
      videoList.style.width = "100%";
      videoList.style.display = "flex";
      videoList.style.flexDirection = "column";
      videoList.style.gap = "5px"; // Reduced from 10px to 5px
      videoList.style.paddingRight = "2px"; // Add slight padding

      // Process each video
      const items = data.items || [];
      items.forEach((item) => {
        // Get video data based on the API response type
        let videoId, title, thumbnail, publishedAt, description;

        // Helper function to get the best available thumbnail
        const getBestThumbnail = (thumbnails) => {
          if (!thumbnails)
            return "https://via.placeholder.com/120x90?text=No+Thumbnail";

          // Try to get thumbnails in order of preference
          if (thumbnails.medium && thumbnails.medium.url)
            return thumbnails.medium.url;
          if (thumbnails.default && thumbnails.default.url)
            return thumbnails.default.url;
          if (thumbnails.high && thumbnails.high.url)
            return thumbnails.high.url;
          if (thumbnails.standard && thumbnails.standard.url)
            return thumbnails.standard.url;
          if (thumbnails.maxres && thumbnails.maxres.url)
            return thumbnails.maxres.url;

          // If we can't find any thumbnail, use a placeholder
          return "https://via.placeholder.com/120x90?text=No+Thumbnail";
        };

        if (type === "playlist") {
          // Playlist items have a different structure
          videoId = item.snippet.resourceId.videoId;
          title = item.snippet.title;
          thumbnail = getBestThumbnail(item.snippet.thumbnails);
          publishedAt = item.snippet.publishedAt;
          description = item.snippet.description;
        } else {
          // Search results
          videoId = item.id.videoId;
          title = item.snippet.title;
          thumbnail = getBestThumbnail(item.snippet.thumbnails);
          publishedAt = item.snippet.publishedAt;
          description = item.snippet.description;
        }

        // Create a video item
        const videoItem = document.createElement("div");
        videoItem.className = "youtube-video-item";
        videoItem.dataset.videoId = videoId;
        videoItem.style.display = "flex";
        videoItem.style.padding = "4px"; // Further reduced padding
        videoItem.style.gap = "5px"; // Further reduced gap
        videoItem.style.borderBottom = "1px solid rgba(0,0,0,0.1)";
        videoItem.style.cursor = "pointer";
        videoItem.style.transition = "background-color 0.2s ease";
        videoItem.style.borderRadius = "4px"; // Add rounded corners

        // Create thumbnail
        const thumbnailElement = document.createElement("div");
        thumbnailElement.className = "youtube-video-thumbnail";
        thumbnailElement.style.width = "100px"; // Reduced to fit narrower list
        thumbnailElement.style.height = "56px"; // Maintained 16:9 ratio
        thumbnailElement.style.flexShrink = "0";
        thumbnailElement.style.borderRadius = "4px";
        thumbnailElement.style.overflow = "hidden";
        thumbnailElement.innerHTML = `<img src="${thumbnail}" alt="${title}" style="width:100%;height:100%;object-fit:cover;">`;

        // Create video info
        const videoInfo = document.createElement("div");
        videoInfo.className = "youtube-video-info";
        videoInfo.style.flex = "1";
        videoInfo.style.display = "flex";
        videoInfo.style.flexDirection = "column";
        videoInfo.style.justifyContent = "space-between";

        // Format the date
        const date = new Date(publishedAt);
        const formattedDate = `${date.toLocaleDateString()} ${date.toLocaleTimeString()}`;

        // Add video title and date
        const titleElement = document.createElement("h3");
        titleElement.className = "youtube-video-title";
        titleElement.textContent = title;
        titleElement.style.fontSize = "12px"; // Reduced from 14px to 12px
        titleElement.style.fontWeight = "500";
        titleElement.style.margin = "0 0 3px 0"; // Reduced margin
        titleElement.style.lineHeight = "1.2";
        titleElement.style.display = "-webkit-box";
        titleElement.style.webkitLineClamp = "2";
        titleElement.style.webkitBoxOrient = "vertical";
        titleElement.style.overflow = "hidden";

        const dateElement = document.createElement("p");
        dateElement.className = "youtube-video-date";
        dateElement.textContent = formattedDate;
        dateElement.style.fontSize = "10px"; // Reduced from 12px to 10px
        dateElement.style.color = "#606060";
        dateElement.style.margin = "0";

        videoInfo.appendChild(titleElement);
        videoInfo.appendChild(dateElement);

        // Add hover effect
        videoItem.addEventListener("mouseenter", () => {
          videoItem.style.backgroundColor = "rgba(0,0,0,0.05)";
        });

        videoItem.addEventListener("mouseleave", () => {
          if (!videoItem.classList.contains("active")) {
            videoItem.style.backgroundColor = "";
          }
        });

        // Add click event to play the video
        videoItem.addEventListener("click", () => {
          this.playVideo(videoId, channelKey);

          // Update active video in list
          this.updateActiveVideoInList(videoId, channelKey);
        });

        // Add elements to the video item
        videoItem.appendChild(thumbnailElement);
        videoItem.appendChild(videoInfo);

        // Add the video item to the list
        videoList.appendChild(videoItem);
      });

      // Add the video list to the container
      videoListContainer.appendChild(videoList);

      // Add the video list container to the videos container
      videosContainer.appendChild(videoListContainer);

      // If this is the first load, play the first video
      if (items.length > 0 && !currentVideoIds[channelKey]) {
        let firstVideoId;

        if (type === "playlist") {
          firstVideoId = items[0].snippet.resourceId.videoId;
        } else {
          firstVideoId = items[0].id.videoId;
        }

        // Set a timeout to allow the player to initialize
        setTimeout(() => {
          this.playVideo(firstVideoId, channelKey);
        }, 500);
      }
    },

    // Create a player layout for a channel
    createPlayerLayout: function (channelKey) {
      const channel = window.YOUTUBE_CHANNELS[channelKey];
      if (!channel) {
        console.error(`Channel ${channelKey} not found in YOUTUBE_CHANNELS`);
        return;
      }

      // Check if the player layout already exists
      let playerLayout = document.getElementById(
        `youtube-player-layout-${channelKey}`
      );
      if (playerLayout) {
        return; // Layout already exists
      }

      // Find the tab content
      const tabContent = document.getElementById(channel.tabId);
      if (!tabContent) {
        console.error(`Tab content for channel ${channelKey} not found`);
        return;
      }

      // Ensure tab content has proper styles
      tabContent.style.display = tabContent.classList.contains("active")
        ? "block"
        : "none";
      tabContent.style.width = "100%";
      tabContent.style.position = "relative";
      tabContent.style.overflow = "visible";

      // Create player layout
      playerLayout = document.createElement("div");
      playerLayout.className = "youtube-player-layout";
      playerLayout.id = `youtube-player-layout-${channelKey}`;

      // Set explicit styles to fix layout issues
      playerLayout.style.display = "flex";
      playerLayout.style.flexWrap = "nowrap";
      playerLayout.style.gap = "20px";
      playerLayout.style.alignItems = "flex-start";
      playerLayout.style.justifyContent = "space-between";
      playerLayout.style.width = "100%";
      playerLayout.style.maxHeight = "calc(100vh - 200px)";
      playerLayout.style.overflow = "hidden";
      playerLayout.style.position = "relative";
      playerLayout.style.zIndex = "10";
      playerLayout.style.marginTop = "20px";
      playerLayout.style.marginBottom = "20px";

      // Create player container
      const playerContainer = document.createElement("div");
      playerContainer.className = "youtube-player-container";
      playerContainer.id = `youtube-player-container-${channelKey}`;
      playerContainer.dataset.channelKey = channelKey;

      // Set explicit styles for player container
      playerContainer.style.flex = "4";
      playerContainer.style.maxWidth = "80%"; // Set to 80%
      playerContainer.style.minWidth = "75%"; // Minimum width
      playerContainer.style.height = "auto";
      playerContainer.style.position = "relative";
      playerContainer.style.zIndex = "5";
      playerContainer.style.backgroundColor = "transparent";

      // Create player element
      const playerElement = document.createElement("div");
      playerElement.id = `youtube-player-${channelKey}`;
      playerElement.className = "youtube-player-iframe";

      // Set explicit styles for player element
      playerElement.style.width = "100%";
      playerElement.style.aspectRatio = "16/9";

      // Add player element to container
      playerContainer.appendChild(playerElement);

      // Add container to layout
      playerLayout.appendChild(playerContainer);

      // Create list column
      const listColumn = document.createElement("div");
      listColumn.className = "youtube-list-column";
      listColumn.id = `youtube-list-column-${channelKey}`;

      // Set explicit styles for list column
      listColumn.style.flex = "1";
      listColumn.style.maxWidth = "20%"; // Set to 20%
      listColumn.style.minWidth = "200px"; // Minimum width
      listColumn.style.maxHeight = "calc(100vh - 200px)";
      listColumn.style.overflowY = "hidden"; // Remove scrollbar as requested
      listColumn.style.overflowX = "hidden";
      listColumn.style.position = "relative";
      listColumn.style.zIndex = "5";
      listColumn.style.backgroundColor = "transparent";
      listColumn.style.marginTop = "0";
      listColumn.style.paddingTop = "0";

      // Move the videos container to the list column
      const videosContainer = document.getElementById(
        `youtube-videos-container-${channelKey}`
      );
      if (videosContainer) {
        // Set explicit styles for videos container
        videosContainer.style.width = "100%";
        videosContainer.style.maxHeight = "100%";
        videosContainer.style.overflow = "visible";
        videosContainer.style.overflowY = "hidden"; // No scrollbar
        videosContainer.style.paddingRight = "0"; // No need for scrollbar padding

        listColumn.appendChild(videosContainer);
      }

      // Add list column to layout
      playerLayout.appendChild(listColumn);

      // Add layout to tab content (at the beginning)
      if (tabContent.firstChild) {
        tabContent.insertBefore(playerLayout, tabContent.firstChild);
      } else {
        tabContent.appendChild(playerLayout);
      }

      // Force layout recalculation
      setTimeout(() => {
        if (playerLayout) {
          playerLayout.style.display = "flex";
        }
        if (listColumn) {
          listColumn.style.display = "block";
        }
      }, 100);
    },

    // Play a video
    playVideo: function (videoId, channelKey) {
      if (!videoId || !channelKey) {
        console.error("Invalid videoId or channelKey:", videoId, channelKey);
        return;
      }

      console.log(`Playing video ${videoId} for channel ${channelKey}`);

      // Set the current video ID for this channel
      currentVideoIds[channelKey] = videoId;

      try {
        // Initialize the YouTube player
        this.initializeYouTubePlayer(videoId, channelKey);

        // Update active video in list
        this.updateActiveVideoInList(videoId, channelKey);
      } catch (error) {
        console.error(
          `Error playing video ${videoId} for channel ${channelKey}:`,
          error
        );
      }
    },

    // Update the active video in the list
    updateActiveVideoInList: function (videoId, channelKey) {
      // Remove active class from all video items
      const allVideoItems = document.querySelectorAll(".youtube-video-item");
      allVideoItems.forEach((item) => {
        item.classList.remove("active");
        item.style.backgroundColor = "";
        item.style.borderLeft = "none";
        item.style.paddingLeft = "8px";
      });

      // Add active class to the current video item
      const currentVideoItem = document.querySelector(
        `.youtube-video-item[data-video-id="${videoId}"]`
      );
      if (currentVideoItem) {
        currentVideoItem.classList.add("active");
        currentVideoItem.style.backgroundColor = "rgba(0,0,0,0.1)";

        // Add a left border to indicate active item
        currentVideoItem.style.borderLeft = "4px solid #007bff";
        currentVideoItem.style.paddingLeft = "4px";

        // Scroll the video into view with a small delay to ensure layout is complete
        setTimeout(() => {
          // Find the parent scrollable container
          const listColumn = document.getElementById(
            `youtube-list-column-${channelKey}`
          );
          if (listColumn) {
            // Calculate position to scroll to
            const itemTop = currentVideoItem.offsetTop;
            const containerScrollTop = listColumn.scrollTop;
            const containerHeight = listColumn.clientHeight;
            const itemHeight = currentVideoItem.offsetHeight;

            // If item is not visible, scroll to it
            if (
              itemTop < containerScrollTop ||
              itemTop + itemHeight > containerScrollTop + containerHeight
            ) {
              currentVideoItem.scrollIntoView({
                behavior: "smooth",
                block: "nearest",
              });
            }
          } else {
            // Fallback to default scrollIntoView
            currentVideoItem.scrollIntoView({
              behavior: "smooth",
              block: "nearest",
            });
          }
        }, 100);
      }
    },

    // Load the YouTube IFrame API
    loadYouTubeApi: function () {
      // Check if the API is already being loaded or is loaded
      if (
        document.querySelector(
          'script[src="https://www.youtube.com/iframe_api"]'
        ) ||
        typeof YT !== "undefined"
      ) {
        // If YT is defined but not fully initialized, wait for it
        if (typeof YT !== "undefined" && !YT.Player) {
          // Check again after a delay
          setTimeout(() => {
            if (typeof YT !== "undefined" && YT.Player) {
              youtubeApiLoaded = true;

              // Initialize players for channels with video IDs
              Object.keys(currentVideoIds).forEach((channelKey) => {
                if (currentVideoIds[channelKey]) {
                  this.initializeYouTubePlayer(
                    currentVideoIds[channelKey],
                    channelKey
                  );
                }
              });
            }
          }, 1000);
        }
        return;
      }

      // Load the API
      const tag = document.createElement("script");
      tag.src = "https://www.youtube.com/iframe_api";
      const firstScriptTag = document.getElementsByTagName("script")[0];
      firstScriptTag.parentNode.insertBefore(tag, firstScriptTag);

      // Define the callback function
      window.onYouTubeIframeAPIReady = () => {
        youtubeApiLoaded = true;
        console.log("YouTube API is ready");

        // Dispatch event to notify that YouTube API is ready
        const apiReadyEvent = new CustomEvent("YouTubeAPIReady");
        document.dispatchEvent(apiReadyEvent);
        console.log("YouTubeAPIReady event dispatched");

        // Initialize players for channels with video IDs
        Object.keys(currentVideoIds).forEach((channelKey) => {
          if (currentVideoIds[channelKey]) {
            this.initializeYouTubePlayer(
              currentVideoIds[channelKey],
              channelKey
            );
          }
        });
      };
    },

    // Initialize the YouTube player with a video ID
    initializeYouTubePlayer: function (videoId, channelKey) {
      console.log(
        "Initializing YouTube player with video ID:",
        videoId,
        "for channel:",
        channelKey
      );

      // Always set the current video ID for this channel
      currentVideoIds[channelKey] = videoId;

      // If the YouTube API isn't loaded yet, wait
      if (!youtubeApiLoaded) {
        // Load the YouTube API
        this.loadYouTubeApi();
        return;
      }

      // If the player already exists for this channel, just load the new video
      const player = youtubePlayers[channelKey];
      if (player) {
        // Pause all other videos first
        this.pauseAllVideos();

        // Check if the player is fully initialized and has the loadVideoById method
        if (
          player.loadVideoById &&
          typeof player.loadVideoById === "function"
        ) {
          // Then load and play the new video
          player.loadVideoById(videoId);
          this.updateActiveVideoInList(videoId, channelKey);
        } else {
          console.log(
            `Player for ${channelKey} not fully initialized, waiting...`
          );
          // Try again after a short delay
          setTimeout(() => {
            this.initializeYouTubePlayer(videoId, channelKey);
          }, 1000);
        }
        return;
      }

      // Find the player element
      const playerElementId = `youtube-player-${channelKey}`;
      const playerElement = document.getElementById(playerElementId);
      if (!playerElement) {
        console.error(`Player element with ID ${playerElementId} not found`);
        return;
      }

      // Make sure YT is defined and available
      if (typeof YT === "undefined" || !YT.Player) {
        console.error(
          "YouTube API (YT) is not available, waiting for it to load"
        );
        // Try again after a delay
        setTimeout(() => {
          this.initializeYouTubePlayer(videoId, channelKey);
        }, 2000);
        return;
      }

      // Create a new YouTube player
      try {
        const newPlayer = new YT.Player(playerElementId, {
          height: "100%",
          width: "100%",
          videoId: videoId,
          playerVars: {
            "playsinline": 1,
            "rel": 0,
            "modestbranding": 1,
            "origin": window.location.origin,
            "host": window.location.origin,
            "controls": 1,
            "showinfo": 1,
            "fs": 1,
            "iv_load_policy": 3,
            "enablejsapi": 1,
            "widgetid": 1,
          },
          events: {
            "onReady": (event) => this.onPlayerReady(event, channelKey),
            "onStateChange": (event) =>
              this.onPlayerStateChange(event, channelKey),
          },
        });

        // Store the player
        youtubePlayers[channelKey] = newPlayer;

        console.log(
          "YouTube player created successfully for channel:",
          channelKey
        );
      } catch (error) {
        console.error("Error creating YouTube player:", error);
        // Try again after a delay
        setTimeout(() => {
          this.initializeYouTubePlayer(videoId, channelKey);
        }, 2000);
      }
    },

    // When the player is ready
    onPlayerReady: function (event, channelKey) {
      console.log(`Player for channel ${channelKey} is ready`);

      // Make sure the player is fully initialized
      if (!youtubePlayers[channelKey]) {
        youtubePlayers[channelKey] = event.target;
      }

      // Pause all other videos first
      this.pauseAllVideos();

      // Then play this video if it's in the active tab
      const tabId = window.YOUTUBE_CHANNELS[channelKey].tabId;
      const tabContent = document.getElementById(tabId);

      if (tabContent && tabContent.classList.contains("active")) {
        // Only play if this is the active tab
        try {
          event.target.playVideo();
        } catch (e) {
          console.error(`Error playing video for channel ${channelKey}:`, e);
        }
      }

      // Hide any loading indicators
      const loadingIndicator = document.getElementById(
        `loading-indicator-${channelKey}`
      );
      if (loadingIndicator) {
        loadingIndicator.style.display = "none";
      }

      // Dispatch an event to notify that the player is ready
      const playerReadyEvent = new CustomEvent("YouTubePlayerReady", {
        detail: { channelKey: channelKey },
      });
      document.dispatchEvent(playerReadyEvent);
    },

    // Handle player state changes
    onPlayerStateChange: function (event, channelKey) {
      // When a video starts playing (state = 1)
      if (event.data === YT.PlayerState.PLAYING) {
        console.log(`Video in channel ${channelKey} started playing`);

        // Pause all other videos
        Object.keys(youtubePlayers).forEach((key) => {
          if (key !== channelKey) {
            const otherPlayer = youtubePlayers[key];
            if (otherPlayer && typeof otherPlayer.pauseVideo === "function") {
              try {
                console.log(`Pausing video for channel: ${key}`);
                otherPlayer.pauseVideo();
              } catch (e) {
                console.error(`Error pausing video for channel ${key}:`, e);
              }
            }
          }
        });
      }
    },

    // Add pagination bar
    addPaginationBar: function (channelKey, type, data) {
      console.log(
        `Adding pagination bar for channel: ${channelKey}, type: ${type}`
      );

      // Initialize pagination state if not exists
      if (!paginationState[channelKey]) {
        paginationState[channelKey] = {
          currentPage: 1,
          totalItems: 0,
          pageTokens: { 1: null }, // First page has no token
          type: type,
        };
      }

      // Update pagination state
      const state = paginationState[channelKey];
      state.type = type;

      // Store the next page token
      if (data.nextPageToken) {
        state.pageTokens[state.currentPage + 1] = data.nextPageToken;
      }

      // Update total items if available
      if (data.pageInfo && data.pageInfo.totalResults) {
        state.totalItems = data.pageInfo.totalResults;
      }

      // Calculate total pages (approximate)
      const totalPages = Math.ceil(state.totalItems / MAX_RESULTS) || 10; // Default to 10 if unknown

      // Remove existing pagination bars if present
      const existingPagination = document.getElementById(
        `pagination-${channelKey}`
      );
      if (existingPagination) {
        existingPagination.remove();
      }

      // Create pagination container
      const paginationContainer = document.createElement("div");
      paginationContainer.id = `pagination-${channelKey}`;
      paginationContainer.className = "pagination-container";

      // Add page info
      const pageInfo = document.createElement("div");
      pageInfo.className = "page-info";
      pageInfo.textContent = `Page ${state.currentPage} of ${
        totalPages > 100 ? "100+" : totalPages
      }`;
      paginationContainer.appendChild(pageInfo);

      // Create pagination controls
      const paginationControls = document.createElement("div");
      paginationControls.className = "pagination-controls";

      // Create pagination buttons
      const createPaginationButton = (
        icon,
        title,
        isDisabled,
        clickHandler
      ) => {
        const button = document.createElement("button");
        button.className = "pagination-button";
        button.innerHTML = `<i class="fas ${icon}"></i>`;
        button.title = title;
        button.disabled = isDisabled;
        button.addEventListener("click", clickHandler);
        return button;
      };

      // First page button
      const firstPageBtn = createPaginationButton(
        "fa-angle-double-left",
        "First Page",
        state.currentPage === 1,
        () => this.navigateToPage(channelKey, 1)
      );
      paginationControls.appendChild(firstPageBtn);

      // Previous page button
      const prevPageBtn = createPaginationButton(
        "fa-angle-left",
        "Previous Page",
        state.currentPage === 1,
        () => this.navigateToPage(channelKey, state.currentPage - 1)
      );
      paginationControls.appendChild(prevPageBtn);

      // Next page button
      const nextPageBtn = createPaginationButton(
        "fa-angle-right",
        "Next Page",
        !data.nextPageToken,
        () => this.navigateToPage(channelKey, state.currentPage + 1)
      );
      paginationControls.appendChild(nextPageBtn);

      // Last page button
      const lastPageBtn = createPaginationButton(
        "fa-angle-double-right",
        "Last Page",
        !data.nextPageToken,
        () => {
          // Calculate the last page number
          const totalPages = Math.ceil(state.totalItems / MAX_RESULTS) || 10;
          // Limit to 100 pages maximum (YouTube API limitation)
          const lastPage = Math.min(totalPages, 100);
          this.navigateToPage(channelKey, lastPage);
        }
      );
      paginationControls.appendChild(lastPageBtn);

      // Add pagination controls to container
      paginationContainer.appendChild(paginationControls);

      // Find the list column
      const listColumn = document.getElementById(
        `youtube-list-column-${channelKey}`
      );

      // Check if this is one of the specified channels that needs pagination
      const isSpecifiedChannel = [
        "finnishcrashcourse",
        "finnishtogo",
        "suomenkurssiyt",
        "yleareena",
        "yleareena2",
        "yleareena3",
        "yleareena4",
        "yleareena5",
        "pipsapossu",
        "katchatsfinnish",
      ].includes(channelKey);

      console.log(
        `Channel ${channelKey} is specified channel: ${isSpecifiedChannel}`
      );

      if (listColumn) {
        // Insert at the top of the list column
        const videosContainer = document.getElementById(
          `youtube-videos-container-${channelKey}`
        );
        if (videosContainer) {
          listColumn.insertBefore(paginationContainer, videosContainer);
          console.log(
            `Pagination added to list column before videos container for ${channelKey}`
          );
        } else {
          listColumn.appendChild(paginationContainer);
          console.log(`Pagination added to list column for ${channelKey}`);
        }

        // For specified channels, ensure the pagination is visible
        if (isSpecifiedChannel) {
          paginationContainer.style.display = "flex";
          paginationContainer.style.visibility = "visible";
          paginationContainer.style.opacity = "1";
          console.log(
            `Forced pagination visibility for specified channel: ${channelKey}`
          );
        }
      } else {
        // Fallback: add to the tab content
        const tabContent = document.getElementById(
          window.YOUTUBE_CHANNELS[channelKey].tabId
        );
        if (tabContent) {
          if (tabContent.firstChild) {
            tabContent.insertBefore(paginationContainer, tabContent.firstChild);
            console.log(
              `Pagination added to tab content before first child for ${channelKey}`
            );
          } else {
            tabContent.appendChild(paginationContainer);
            console.log(`Pagination added to tab content for ${channelKey}`);
          }

          // For specified channels, ensure the pagination is visible
          if (isSpecifiedChannel) {
            paginationContainer.style.display = "flex";
            paginationContainer.style.visibility = "visible";
            paginationContainer.style.opacity = "1";
            console.log(
              `Forced pagination visibility for specified channel: ${channelKey}`
            );
          }
        }
      }
    },

    // Navigate to a specific page
    navigateToPage: function (channelKey, pageNumber) {
      // Get the pagination state for this channel
      const state = paginationState[channelKey];
      if (!state) {
        console.error(`Pagination state for channel ${channelKey} not found`);
        return;
      }

      // Check if the page number is valid
      if (pageNumber < 1) {
        console.error(`Invalid page number: ${pageNumber}`);
        return;
      }

      // Set pending page update
      pendingPageUpdate = {
        channelKey: channelKey,
        pageNumber: pageNumber,
      };

      // If we're going to the first page, we don't need a token
      if (pageNumber === 1) {
        this.fetchChannelVideos(channelKey, null);
        return;
      }

      // If we already have the token for this page, use it
      if (state.pageTokens[pageNumber]) {
        this.fetchChannelVideos(channelKey, state.pageTokens[pageNumber]);
        return;
      }

      // If we're going forward and have the token for the next page, use it
      if (
        pageNumber === state.currentPage + 1 &&
        state.pageTokens[pageNumber]
      ) {
        this.fetchChannelVideos(channelKey, state.pageTokens[pageNumber]);
        return;
      }

      // Otherwise, we need to navigate page by page to get the token
      // Start from the closest page we have a token for
      let closestPage = 1;
      let closestToken = null;

      // Find the closest page with a token
      for (let page = 1; page < pageNumber; page++) {
        if (state.pageTokens[page] !== undefined) {
          closestPage = page;
          closestToken = state.pageTokens[page];
        }
      }

      // Navigate to the closest page first
      this.fetchChannelVideos(channelKey, closestToken);

      // Then navigate to the target page
      // This will be handled by the pagination state update in addPaginationBar
    },

    // Ensure pagination is visible for specified channels
    ensurePaginationForSpecifiedChannels: function () {
      console.log("Ensuring pagination for specified channels");

      // List of specified channels
      const specifiedChannels = [
        "finnishcrashcourse",
        "finnishtogo",
        "suomenkurssiyt",
        "yleareena",
        "yleareena2",
        "yleareena3",
        "yleareena4",
        "yleareena5",
        "pipsapossu",
        "katchatsfinnish",
        "kaapowildbrain",
        "kaapowildbrain2",
        "kaapowildbrain3",
        "kaapowildbrain4",
        "ylepikkukakkonen",
        "ylepikkukakkonen2",
        "ylepikkukakkonen3",
        "ylepikkukakkonen4",
        "ylepikkukakkonen5",
        "ylepikkukakkonen6",
      ];

      // For each specified channel
      specifiedChannels.forEach((channelKey) => {
        console.log(`Ensuring pagination for channel: ${channelKey}`);

        // Get the pagination container
        const paginationContainer = document.getElementById(
          `pagination-${channelKey}`
        );

        if (paginationContainer) {
          // Make sure it's visible
          paginationContainer.style.display = "flex";
          paginationContainer.style.visibility = "visible";
          paginationContainer.style.opacity = "1";
          paginationContainer.style.zIndex = "1000";
          console.log(
            `Found and made visible pagination for channel: ${channelKey}`
          );
        } else {
          console.log(
            `Pagination container not found for channel: ${channelKey}, will create one`
          );

          // If pagination container doesn't exist, fetch videos for this channel
          // This will create the pagination
          if (window.YOUTUBE_CHANNELS[channelKey]) {
            console.log(
              `Fetching videos to create pagination for channel: ${channelKey}`
            );
            this.fetchChannelVideos(channelKey);
          }
        }

        // Also check the tab content
        const channel = window.YOUTUBE_CHANNELS[channelKey];
        if (channel && channel.tabId) {
          const tabContent = document.getElementById(channel.tabId);
          if (tabContent) {
            // Find all pagination containers in this tab
            const paginationContainers = tabContent.querySelectorAll(
              ".pagination-container"
            );
            paginationContainers.forEach((container) => {
              container.style.display = "flex";
              container.style.visibility = "visible";
              container.style.opacity = "1";
              container.style.zIndex = "1000";
            });
            console.log(
              `Made visible all pagination containers in tab: ${channel.tabId}`
            );
          }
        }
      });
    },

    // Create a fallback pagination when API requests fail
    createFallbackPagination: function (channelKey, errorMessage) {
      console.log(`Creating fallback pagination for channel: ${channelKey}`);

      // Get the channel
      const channel = window.YOUTUBE_CHANNELS[channelKey];
      if (!channel) {
        console.error(`Channel ${channelKey} not found in YOUTUBE_CHANNELS`);
        return;
      }

      // Create a simple pagination container
      const paginationContainer = document.createElement("div");
      paginationContainer.id = `pagination-${channelKey}`;
      paginationContainer.className = "pagination-container";

      // Add error message
      const errorDiv = document.createElement("div");
      errorDiv.className = "pagination-error";
      errorDiv.textContent = errorMessage;

      // Add a retry button
      const retryButton = document.createElement("button");
      retryButton.textContent = "Try Again";
      retryButton.className = "pagination-retry-button";

      // Add click event to retry
      retryButton.addEventListener("click", () => {
        // Rotate to the next API key
        this.rotateApiKey();
        // Try to fetch videos again
        this.fetchChannelVideos(channelKey);
      });

      paginationContainer.appendChild(errorDiv);
      paginationContainer.appendChild(retryButton);

      // Check if this is one of the specified channels that needs pagination
      const isSpecifiedChannel = [
        "finnishcrashcourse",
        "finnishtogo",
        "suomenkurssiyt",
        "yleareena",
        "yleareena2",
        "yleareena3",
        "yleareena4",
        "yleareena5",
        "pipsapossu",
        "katchatsfinnish",
        "kaapowildbrain",
        "kaapowildbrain2",
        "kaapowildbrain3",
        "kaapowildbrain4",
        "ylepikkukakkonen",
        "ylepikkukakkonen2",
        "ylepikkukakkonen3",
        "ylepikkukakkonen4",
        "ylepikkukakkonen5",
        "ylepikkukakkonen6",
      ].includes(channelKey);

      // Try to find the list column first
      const listColumn = document.getElementById(
        `youtube-list-column-${channelKey}`
      );
      if (listColumn) {
        // Add to list column
        listColumn.insertBefore(paginationContainer, listColumn.firstChild);
        console.log(
          `Fallback pagination added to list column for ${channelKey}`
        );
      } else {
        // Try to find the tab content
        const tabContent = document.getElementById(channel.tabId);
        if (tabContent) {
          // Add to tab content
          if (tabContent.firstChild) {
            tabContent.insertBefore(paginationContainer, tabContent.firstChild);
            console.log(
              `Fallback pagination added to tab content for ${channelKey}`
            );
          } else {
            tabContent.appendChild(paginationContainer);
            console.log(
              `Fallback pagination added to tab content for ${channelKey}`
            );
          }
        }
      }

      // For specified channels, ensure the pagination is visible
      if (isSpecifiedChannel) {
        paginationContainer.style.display = "flex";
        paginationContainer.style.visibility = "visible";
        paginationContainer.style.opacity = "1";
        console.log(
          `Forced fallback pagination visibility for specified channel: ${channelKey}`
        );
      }
    },
  };
})();

// Initialize when the DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Make sure YOUTUBE_CHANNELS is defined
  if (typeof window.YOUTUBE_CHANNELS === "undefined") {
    console.error(
      "YOUTUBE_CHANNELS is not defined. Make sure to include youtube-channels.js before youtube-core.js"
    );
    return;
  }

  // Initialize the YouTube Core
  YouTubeCore.init();
});

