# Chapter 42: Tietot<PERSON>va verkossa / Web Security

## Objectives / Tavoitteet
- Learn vocabulary related to web security in Finnish
- Understand how to discuss common security threats and vulnerabilities
- Be able to explain basic security practices and protections
- Master basic conversations about securing web applications

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. T<PERSON>oturva - Security
2. Haavoittuvuus - Vulnerability
3. Hyökkäys - Attack
4. Suojaus - Protection
5. Salaus - Encryption
6. Todentaminen - Authentication
7. Valtuutus - Authorization
8. Palomuuri - Firewall
9. Tietomurto - Data breach
10. Haittaohjelma - Malware
11. Tietojenkalastelu - Phishing
12. Salasana - Password
13. Kaks<PERSON>ih<PERSON>en tunnistautuminen - Two-factor authentication
14. Haavoittuvuusskannaus - Vulnerability scanning
15. Tietoturvapäivitys - Security update

## Grammar Points / Kielioppi
1. **Technical Verbs for Security Operations**:
   - Action verbs for security measures
   - Example: <PERSON><PERSON> arkaluontoiset tiedot. (I encrypt sensitive data.)

2. **Conditional Forms for Security Scenarios**:
   - Expressing security conditions
   - Example: <PERSON><PERSON>, tie<PERSON><PERSON><PERSON>. (If the password would leak, a data breach would be possible.)

3. **Inessive Case (-ssa/-ssä) for Security Contexts**:
   - In security environments
   - Example: Tietoturvassa on monia tasoja. (There are many levels in security.)

4. **Elative Case (-sta/-stä) for Security Threats**:
   - From threat sources
   - Example: Suojaudumme hyökkäyksiltä palomuurilla. (We protect ourselves from attacks with a firewall.)

5. **Translative Case (-ksi) for Security Transformations**:
   - Converting to secure states
   - Example: Muunnan salasanan salatuksi tiivisteeksi. (I convert the password into an encrypted hash.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Web security workshop / Verkkotietoturvan työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa verkkotietoturvan työpajaan! Tänään opimme, miten voimme suojata verkkosovelluksiamme yleisimmiltä uhilta.<br>
<em>(ter-ve-tu-lo-a verk-ko-tie-to-tur-van työ-pa-jaan! tä-nään o-pim-me, mi-ten voim-me suo-ja-ta verk-ko-so-vel-luk-si-am-me y-lei-sim-mil-tä u-hil-ta.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen kehittänyt verkkosovelluksia, mutta en ole varma, ovatko ne riittävän turvallisia.<br>
<em>(kii-tos! o-len ke-hit-tä-nyt verk-ko-so-vel-luk-si-a, mut-ta en o-le var-ma, o-vat-ko ne riit-tä-vän tur-val-li-si-a.)</em></p>

<p><strong>Ohjaaja</strong>: Se on yleinen huolenaihe. Aloitetaan käymällä läpi yleisimmät verkkosovelluksiin kohdistuvat uhat.<br>
<em>(se on y-lei-nen huo-len-ai-he. a-loi-te-taan käy-mäl-lä lä-pi y-lei-sim-mät verk-ko-so-vel-luk-siin koh-dis-tu-vat u-hat.)</em></p>

<p><strong>Osallistuja</strong>: Mitä ne ovat?<br>
<em>(mi-tä ne o-vat?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin suojautua SQL-injektiolta?<br>
<em>(mi-ten voin suo-jau-tu-a SQL-in-jek-ti-ol-ta?)</em></p>

<p><strong>Osallistuja</strong>: Entä XSS-hyökkäykset?<br>
<em>(en-tä XSS-hyök-kä-yk-set?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin parantaa todentamista ja salasanojen turvallisuutta?<br>
<em>(mi-ten voin pa-ran-taa to-den-ta-mis-ta ja sa-la-sa-no-jen tur-val-li-suut-ta?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin suojata arkaluontoista tietoa?<br>
<em>(mi-ten voin suo-ja-ta ar-ka-luon-tois-ta tie-to-a?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin suojautua CSRF-hyökkäyksiltä?<br>
<em>(mi-ten voin suo-jau-tu-a CSRF-hyök-kä-yk-sil-tä?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin pitää sovellukseni komponentit turvallisina?<br>
<em>(mi-ten voin pi-tää so-vel-luk-se-ni kom-po-nen-tit tur-val-li-si-na?)</em></p>

<p><strong>Osallistuja</strong>: Onko muita yleisiä tietoturvakäytäntöjä, joita minun pitäisi noudattaa?<br>
<em>(on-ko mui-ta y-lei-si-ä tie-to-tur-va-käy-tän-tö-jä, joi-ta mi-nun pi-täi-si nou-dat-taa?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla parantaa sovellukseni tietoturvaa näiden ohjeiden avulla?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la pa-ran-taa so-vel-luk-se-ni tie-to-tur-vaa näi-den oh-jei-den a-vul-la?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita tekemällä tietoturva-arviointi sovelluksellesi. Käy läpi kaikki käsittelemämme uhat ja tarkista, miten sovelluksesi suojautuu niiltä. Priorisoi korjaukset kriittisyyden mukaan - aloita arkaluontoisen tiedon suojaamisesta ja todentamisjärjestelmästä. Muista, että tietoturva on jatkuva prosessi, ei kertaluontoinen tehtävä. Säännöllinen testaus ja päivitykset ovat avainasemassa.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta te-ke-mäl-lä tie-to-tur-va-ar-vi-oin-ti so-vel-luk-sel-le-si. käy lä-pi kaik-ki kä-sit-te-le-mäm-me u-hat ja tar-kis-ta, mi-ten so-vel-luk-se-si suo-jau-tuu niil-tä. pri-o-ri-soi kor-ja-uk-set kriit-ti-syy-den mu-kaan - a-loi-ta ar-ka-luon-toi-sen tie-don suo-jaa-mi-ses-ta ja to-den-ta-mis-jär-jes-tel-mäs-tä. muis-ta, et-tä tie-to-tur-va on jat-ku-va pro-ses-si, ei ker-ta-luon-toi-nen teh-tä-vä. sään-nöl-li-nen tes-ta-us ja päi-vi-tyk-set o-vat a-vain-a-se-mas-sa.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin suojata sovellukseni yleisimmiltä tietoturvauhilta.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin suo-ja-ta so-vel-luk-se-ni y-lei-sim-mil-tä tie-to-tur-va-u-hil-ta.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong focus on cybersecurity, with many companies specializing in security solutions
- Finnish developers typically value security and privacy as fundamental aspects of software development
- Finland's strict data protection laws align with EU regulations (GDPR) and influence how web applications handle personal data
- Finnish educational institutions often include security as a core component of IT and programming curricula
- Finland hosts cybersecurity events and competitions to promote awareness and skills in the field


