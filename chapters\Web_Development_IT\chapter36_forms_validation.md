# Chapter 36: Lomakkeet ja validointi / Forms & Validation

## Objectives / Tavoitteet
- Learn vocabulary related to web forms and validation in Finnish
- Understand how to discuss form elements and data validation
- Be able to explain form submission and error handling
- Master basic conversations about creating user-friendly forms

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Lomake - Form
2. Kenttä - Field
3. Syöte - Input
4. Validointi - Validation
5. Virhe - Error
6. <PERSON><PERSON><PERSON> - Required
7. Lähetys - Submission
8. Tarkistus - Verification
9. Arvo - Value
10. Valintaruutu - Checkbox
11. Pudotusvalikko - Dropdown menu
12. Tekstikenttä - Text field
13. Salasanakenttä - Password field
14. Virheilmoitus - Error message
15. Vahvistus - Confirmation

## Grammar Points / Kielioppi
1. **Imperative Forms for Form Instructions**:
   - Commands for users
   - Example: Täyt<PERSON> kaikki pakolliset kentät. (Fill in all required fields.)

2. **Conditional Forms for Validation Logic**:
   - Expressing validation conditions
   - Example: <PERSON><PERSON> on tyhjä, näytettäisiin virheilmoitus. (If the field is empty, an error message would be shown.)

3. **Inessive Case (-ssa/-ssä) for Form Contexts**:
   - In form elements
   - Example: Lomakkeessa on useita kenttiä. (There are several fields in the form.)

4. **Translative Case (-ksi) for Form Transformations**:
   - Changing form states
   - Example: Lomake muuttuu aktiiviseksi, kun käyttäjä klikkaa sitä. (The form becomes active when the user clicks on it.)

5. **Elative Case (-sta/-stä) for Form Data Sources**:
   - From form elements
   - Example: Tiedot kerätään lomakkeesta. (The information is collected from the form.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Web form design workshop / Verkkolomakkeiden suunnittelutyöpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa lomakkeiden ja validoinnin työpajaan! Tänään opimme, miten luodaan käyttäjäystävällisiä ja turvallisia verkkolomakkeita.<br>
<em>(ter-ve-tu-lo-a lo-mak-kei-den ja va-li-doin-nin työ-pa-jaan! tä-nään o-pim-me, mi-ten luo-daan käyt-tä-jä-ys-tä-väl-li-si-ä ja tur-val-li-si-a verk-ko-lo-mak-kei-ta.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen tehnyt yksinkertaisia lomakkeita, mutta haluaisin oppia enemmän validoinnista.<br>
<em>(kii-tos! o-len teh-nyt yk-sin-ker-tai-si-a lo-mak-kei-ta, mut-ta ha-lu-ai-sin op-pi-a e-nem-män va-li-doin-nis-ta.)</em></p>

<p><strong>Osallistuja</strong>: Mitä erilaisia syötetyyppejä on olemassa?<br>
<em>(mi-tä e-ri-lai-si-a syö-te-tyyp-pe-jä on o-le-mas-sa?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin tehdä kentästä pakollisen?<br>
<em>(mi-ten voin teh-dä ken-täs-tä pa-kol-li-sen?)</em></p>

<p><strong>Osallistuja</strong>: Entä JavaScript-validointi? Miten se toimii?<br>
<em>(en-tä ja-va-script-va-li-doin-ti? mi-ten se toi-mii?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin näyttää virheilmoitukset käyttäjälle?<br>
<em>(mi-ten voin näyt-tää vir-he-il-moi-tuk-set käyt-tä-jäl-le?)</em></p>

<p><strong>Osallistuja</strong>: Entä lomakkeen lähetys? Miten käsittelen lomakkeen tiedot?<br>
<em>(en-tä lo-mak-keen lä-he-tys? mi-ten kä-sit-te-len lo-mak-keen tie-dot?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin parantaa lomakkeen käyttäjäkokemusta?<br>
<em>(mi-ten voin pa-ran-taa lo-mak-keen käyt-tä-jä-ko-ke-mus-ta?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä validoidun lomakkeen?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä va-li-doi-dun lo-mak-keen?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita yksinkertaisella lomakkeella, jossa on muutama kenttä, ja lisää sitten validointi JavaScriptillä. Muista testata lomaketta erilaisilla syötteillä varmistaaksesi, että validointi toimii oikein.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta yk-sin-ker-tai-sel-la lo-mak-keel-la, jos-sa on muu-ta-ma kent-tä, ja li-sää sit-ten va-li-doin-ti ja-va-scrip-til-lä. muis-ta tes-ta-ta lo-ma-ket-ta e-ri-lai-sil-la syöt-teil-lä var-mis-taak-se-si, et-tä va-li-doin-ti toi-mii oi-kein.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin luoda käyttäjäystävällisiä ja turvallisia lomakkeita.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin luo-da käyt-tä-jä-ys-tä-väl-li-si-ä ja tur-val-li-si-a lo-mak-kei-ta.)</em></p>
</div>

### Cultural Notes:
- Finnish websites often feature clean, straightforward forms that reflect the national value of clarity and efficiency
- In Finland, data privacy is taken very seriously, influencing how forms are designed and how data is handled
- Finnish web developers typically prioritize accessibility in form design to ensure usability for all users
- Many Finnish government and municipal services have moved online, making well-designed forms an important part of civic infrastructure
- Finnish digital services often use strong validation to ensure data quality while maintaining a user-friendly experience


