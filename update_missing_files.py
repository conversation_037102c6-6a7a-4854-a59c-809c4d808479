#!/usr/bin/env python3
"""
Script to update missing Finnish grammar files with correct dropdown structure
"""

import os
import re

# Use the same dropdown templates from previous script
LEVEL2_VIDEOS_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt"><PERSON>ome<PERSON></a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>"""

LEVEL2_CATEGORIES_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../index.html#daily-life">Daily Life</a>
                        <a href="../../index.html#web-development">Web Development</a>
                        <a href="../../index.html#cleaner">Cleaner</a>
                        <a href="../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>"""

LEVEL2_ENTERTAINMENT_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../games.html">Games</a>
                    </div>
                </li>"""

LEVEL3_VIDEOS_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>"""

LEVEL3_CATEGORIES_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>"""

LEVEL3_ENTERTAINMENT_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>"""

# Submenu JavaScript functionality
SUBMENU_JAVASCRIPT = """
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });

                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });"""

# List of missing files to update
MISSING_FILES = [
    # Level 2 files (vocabulary index)
    ("finnish_grammar/vocabulary/index.html", 2),
    # Level 3 files (verb forms)
    ("finnish_grammar/verbs/infinitives/index.html", 3),
    ("finnish_grammar/verbs/participles/index.html", 3),
    ("finnish_grammar/verbs/passive/index.html", 3),
    # Level 3 files (vocabulary building)
    ("finnish_grammar/vocabulary/building/common_words.html", 3),
    ("finnish_grammar/vocabulary/building/loanwords.html", 3),
    ("finnish_grammar/vocabulary/building/slang.html", 3),
    ("finnish_grammar/vocabulary/building/word_formation.html", 3),
    # Level 3 files (vocabulary common words)
    ("finnish_grammar/vocabulary/common_words/colors.html", 3),
    ("finnish_grammar/vocabulary/common_words/family.html", 3),
    ("finnish_grammar/vocabulary/common_words/food.html", 3),
    ("finnish_grammar/vocabulary/common_words/greetings.html", 3),
    ("finnish_grammar/vocabulary/common_words/numbers.html", 3),
    ("finnish_grammar/vocabulary/common_words/time.html", 3),
    # Level 3 files (vocabulary thematic)
    ("finnish_grammar/vocabulary/thematic/daily_life.html", 3),
    ("finnish_grammar/vocabulary/thematic/health.html", 3),
    ("finnish_grammar/vocabulary/thematic/nature.html", 3),
    ("finnish_grammar/vocabulary/thematic/shopping.html", 3),
    ("finnish_grammar/vocabulary/thematic/travel.html", 3),
    ("finnish_grammar/vocabulary/thematic/work.html", 3),
]


def get_dropdown_templates(level):
    """Get the correct dropdown templates for the given level"""
    if level == 2:
        return (
            LEVEL2_VIDEOS_DROPDOWN,
            LEVEL2_CATEGORIES_DROPDOWN,
            LEVEL2_ENTERTAINMENT_DROPDOWN,
        )
    elif level == 3:
        return (
            LEVEL3_VIDEOS_DROPDOWN,
            LEVEL3_CATEGORIES_DROPDOWN,
            LEVEL3_ENTERTAINMENT_DROPDOWN,
        )
    else:
        return None, None, None


def update_file(file_path, level):
    """Update a single file with correct dropdown structure"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content
        videos_dropdown, categories_dropdown, entertainment_dropdown = (
            get_dropdown_templates(level)
        )

        if not videos_dropdown:
            print(f"✗ Invalid level {level} for {file_path}")
            return False

        # Update Videos dropdown - handle various existing patterns
        videos_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Videos</a>.*?</li>'
        content = re.sub(videos_pattern, videos_dropdown, content, flags=re.DOTALL)

        # Update Categories dropdown
        categories_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>.*?</li>'
        content = re.sub(
            categories_pattern, categories_dropdown, content, flags=re.DOTALL
        )

        # Update Entertainment dropdown
        entertainment_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>.*?</li>'
        content = re.sub(
            entertainment_pattern, entertainment_dropdown, content, flags=re.DOTALL
        )

        # Add submenu JavaScript if not already present
        if "dropdown-submenu" not in content or "submenu-header" not in content:
            # Find the end of the existing JavaScript and add submenu functionality
            js_end_pattern = r"(\s+}\s*}\);\s*</script>)"
            if re.search(js_end_pattern, content):
                content = re.sub(js_end_pattern, SUBMENU_JAVASCRIPT + r"\1", content)

        # Only write if content changed
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✓ Updated: {file_path} (level {level})")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False

    except Exception as e:
        print(f"✗ Error updating {file_path}: {e}")
        return False


def main():
    """Main function to update all missing files"""
    print("Starting update of missing Finnish grammar files...")

    updated_count = 0
    error_count = 0

    for file_path, level in MISSING_FILES:
        if os.path.exists(file_path):
            if update_file(file_path, level):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
            error_count += 1

    print(f"\nUpdate completed!")
    print(f"Files updated: {updated_count}")
    print(f"Errors: {error_count}")

    if error_count == 0:
        print("All missing Finnish grammar files have been successfully updated!")


if __name__ == "__main__":
    main()
