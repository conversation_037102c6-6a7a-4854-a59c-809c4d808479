// Get URL parameters
function getUrlParams() {
  const params = {};
  const queryString = window.location.search.substring(1);
  const pairs = queryString.split("&");

  for (let i = 0; i < pairs.length; i++) {
    const pair = pairs[i].split("=");
    params[decodeURIComponent(pair[0])] = decodeURIComponent(pair[1] || "");
  }

  return params;
}

// Function to convert markdown to HTML with enhanced formatting
function convertMarkdownToHtml(markdown) {
  // This is a simple markdown to HTML converter
  // For a real implementation, you might want to use a library like marked.js

  let html = markdown;

  // Process Finnish-English translation pairs (format: "Finnish term - English term")
  // This handles both numbered and unnumbered vocabulary items
  html = html.replace(
    /^(?:(\d+)\.\s+)?([^-\n]+) - ([^\n]+)$/gm,
    function (match, number, finnish, english) {
      // Handle potential undefined values
      if (!finnish) {
        console.warn(
          "Found translation pair with undefined Finnish term:",
          match
        );
        finnish = "Term";
      }
      if (!english) {
        console.warn(
          "Found translation pair with undefined English term:",
          match
        );
        english = "Translation";
      }

      const numberPrefix = number
        ? `<span class="item-number">${number}.</span> `
        : "";

      // New format with Finnish and English on the same line
      // Using a more compact format without unnecessary whitespace
      return `<div class="translation-pair" title="${finnish.trim()}: ${english.trim()}"><span class="finnish-term">${numberPrefix}${finnish.trim()}</span><span class="english-term">${english.trim()}</span></div>`;
    }
  );

  // Convert headers with IDs for TOC
  html = html.replace(/^# (.*$)/gm, function (match, title) {
    // Check if title is undefined and use a fallback
    if (!title) {
      console.warn("Found h1 header with undefined title:", match);
      title = "chapter";
    }
    const id = title.toLowerCase().replace(/[^\w]+/g, "-");
    return `<h1 id="${id}">${title}</h1>`;
  });

  // Special handling for Vocabulary and Grammar sections
  html = html.replace(
    /^## (Vocabulary \/ Sanasto)$/gm,
    function (match, title) {
      if (!title) {
        console.warn("Found vocabulary header with undefined title:", match);
        title = "Vocabulary / Sanasto";
      }
      const id = title.toLowerCase().replace(/[^\w]+/g, "-");
      return `<div class="compact-section">
      <h2 id="${id}"><i class="fas fa-book"></i> ${title}</h2>
      <div class="translation-grid">`;
    }
  );

  html = html.replace(
    /^## (Grammar Points \/ Kielioppi)$/gm,
    function (match, title) {
      if (!title) {
        console.warn("Found grammar header with undefined title:", match);
        title = "Grammar Points / Kielioppi";
      }
      const id = title.toLowerCase().replace(/[^\w]+/g, "-");

      // Close the vocabulary section and immediately open the grammar section
      // without any space for paragraphs to be inserted between them
      return `</div></div><div class="compact-section">
      <h2 id="${id}"><i class="fas fa-graduation-cap"></i> ${title}</h2>
      <div class="grammar-markdown-list">`;
    }
  );

  // Handle other h2 headers normally
  html = html.replace(
    /^## (?!(Vocabulary \/ Sanasto|Grammar Points \/ Kielioppi))(.*)$/gm,
    function (match, _, title) {
      // Check if title is undefined and use a fallback
      if (!title) {
        console.warn("Found h2 header with undefined title:", match);
        title = match.replace(/^## /, ""); // Extract title directly from the match
        if (!title.trim()) {
          title = "section";
        }
      }
      const id = title.toLowerCase().replace(/[^\w]+/g, "-");

      // Check if we need to close compact sections
      if (
        html.includes('<div class="compact-section">') &&
        !html.includes("</div></div><h2")
      ) {
        // Close any open compact sections before starting a new section
        return `</div></div><h2 id="${id}">${title}</h2>`;
      } else {
        // No need to close sections
        return `<h2 id="${id}">${title}</h2>`;
      }
    }
  );

  // Convert h3 headers without audio buttons (all buttons moved to conversation divs)
  html = html.replace(/^### (.*$)/gm, function (match, title) {
    if (!title) {
      console.warn("Found h3 header with undefined title:", match);
      title = "section";
    }
    const id = title.toLowerCase().replace(/[^\w]+/g, "-");

    // Generate a unique ID for the container
    const containerId = "h3-" + Math.random().toString(36).substring(2, 11);

    // Return the HTML without audio button
    return `<div class="h3-container" id="${containerId}">
      <h3 id="${id}">${title}</h3>
    </div>`;
  });

  // Convert lists
  html = html.replace(/^\- (.*$)/gm, "<li>$1</li>");
  html = html.replace(/^\d+\. (.*$)/gm, "<li>$1</li>");

  // Wrap lists
  html = html.replace(/<li>.*(?:\n<li>.*)*(?=\n\n|\n$|$)/g, function (match) {
    if (match.indexOf("1.") !== -1 || match.indexOf("2.") !== -1) {
      return "<ol>" + match + "</ol>";
    } else {
      return "<ul>" + match + "</ul>";
    }
  });

  // Process grammar points with numbered items
  // This regex matches numbered items under Grammar Points section
  html = html.replace(
    /(\d+)\.\s+\*\*(.*?)\*\*:([\s\S]*?)(?=\n\d+\.\s+\*\*|\n## |\n# |\n<div class="conversation">|$)/g,
    function (match, number, title, content) {
      if (!number) number = "1";
      if (!title) {
        console.warn("Found grammar point with undefined title:", match);
        title = "Grammar Point";
      }
      if (!content) content = "";

      // Process examples within the grammar point
      const processedContent = content.replace(
        /Example: (.*?)(?=\n|$)/g,
        '<p class="grammar-example"><strong>Example:</strong> $1</p>'
      );

      // Process bullet points (lines starting with - )
      const processedBullets = processedContent.replace(
        /^\s*-\s+(.*?)$/gm,
        "<li>$1</li>"
      );

      // Wrap bullet points in ul if they exist
      const finalContent = processedBullets.includes("<li>")
        ? `<ul>${processedBullets}</ul>`
        : processedBullets;

      // Format as markdown-like list item
      return `<div class="grammar-list-item">
        <p class="grammar-point-title"><strong>${number}. ${title}</strong>:</p>
        ${finalContent.trim()}
      </div>`;
    }
  );

  // Convert bold text (as keywords)
  html = html.replace(/\*\*(.*?)\*\*/g, '<span class="keyword">$1</span>');

  // Convert italic text
  html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");

  // Add ID and Play button to conversation divs
  html = html.replace(/<div class="conversation">/g, function () {
    const conversationId =
      "conversation-" + Math.random().toString(36).substring(2, 11);

    return `<div class="conversation" id="${conversationId}">
      <div class="audio-player-container">
        <button class="audio-play-button" data-conversation-id="${conversationId}" data-language="fi-FI">
          <i class="fas fa-play"></i> Play
        </button>
      </div>`;
  });

  // Convert paragraphs, but skip lines that are inside vocabulary or grammar sections
  let inVocabularySection = false;
  let inGrammarSection = false;

  // First mark the vocabulary and grammar sections
  html = html.replace(
    /<div class="compact-section">\s*<h2 id="vocabulary-sanasto">/g,
    function () {
      inVocabularySection = true;
      inGrammarSection = false; // Ensure grammar section is marked as inactive
      return '<div class="compact-section"><h2 id="vocabulary-sanasto">';
    }
  );

  html = html.replace(
    /<div class="compact-section">\s*<h2 id="grammar-points-kielioppi">/g,
    function () {
      inVocabularySection = false; // Ensure vocabulary section is marked as inactive
      inGrammarSection = true;
      return '<div class="compact-section"><h2 id="grammar-points-kielioppi">';
    }
  );

  // Handle section closing more precisely
  html = html.replace(
    /<\/div><\/div>(?!\s*<div class="compact-section">)/g,
    function () {
      // Reset section flags when we exit a section and don't immediately enter another
      inVocabularySection = false;
      inGrammarSection = false;
      return "</div></div>";
    }
  );

  // Then convert paragraphs, but skip lines in vocabulary or grammar sections
  html = html.replace(/^(?!<[a-z])(.*$)/gm, function (match) {
    if (match.trim() === "") return "";

    // Skip paragraph wrapping for translation pairs and grammar items
    if (
      match.includes('<div class="translation-pair">') ||
      match.includes('<div class="grammar-item">') ||
      match.includes('<div class="translation-grid">') ||
      match.includes('<div class="compact-grid">')
    ) {
      return match;
    }

    // Skip paragraph wrapping inside vocabulary or grammar sections
    if (inVocabularySection || inGrammarSection) {
      return match;
    }

    return "<p>" + match + "</p>";
  });

  // Clean up empty paragraphs
  html = html.replace(/<p><\/p>/g, "");

  // Clean up paragraphs between sections
  html = html.replace(
    /<\/div><\/div>\s*<p>\s*<\/p>\s*<div class="compact-section">/g,
    '</div></div><div class="compact-section">'
  );
  html = html.replace(/<\/div><\/div>\s*<p>\s*<\/p>/g, "</div></div>");
  html = html.replace(
    /<p>\s*<\/p>\s*<div class="compact-section">/g,
    '<div class="compact-section">'
  );

  // Remove any paragraphs that might appear right after translation-grid
  html = html.replace(/<\/div>\s*<p>\s*<\/p>/g, "</div>");

  // Specifically target empty paragraphs after translation-grid
  html = html.replace(
    /<div class="translation-grid">[\s\S]*?<\/div>\s*<p>\s*<\/p>/g,
    function (match) {
      return match.replace(/<p>\s*<\/p>$/, "");
    }
  );

  // Close any open compact sections at the end of the document
  // Count the number of opening and closing div tags to ensure proper nesting
  const openSectionCount = (html.match(/<div class="compact-section">/g) || [])
    .length;
  const closeSectionCount = (
    html.match(/<\/div><\/div>(?!.*<div class="compact-section">)/g) || []
  ).length;

  // If we have more opening tags than closing tags, add the necessary closing tags
  if (openSectionCount > closeSectionCount) {
    console.log(
      `Fixing unclosed compact sections: ${openSectionCount} opened, ${closeSectionCount} closed`
    );
    html += "</div></div>";
  }

  return html;
}

// Function to fetch chapter content
async function fetchChapterContent(category, chapterNumber) {
  try {
    // Show loader
    document.getElementById("chapter-loader").style.display = "block";

    // Map category parameter to directory name (same mapping as in the original server.js)
    const categoryDirs = {
      "daily-life": "Daily_Life",
      "web-development": "Web_Development_IT",
      "cleaner": "Cleaner",
      "kitchen-assistant": "Kitchen_Assistant",
      "warehouse": "Warehouse",
    };

    const categoryDir = categoryDirs[category] || category;

    // Format chapter number with leading zeros if needed
    const paddedNumber =
      category === "daily-life"
        ? chapterNumber.toString().padStart(2, "0")
        : chapterNumber;

    // Determine chapter file pattern based on category
    const chapterFilePattern = `chapter${paddedNumber}_`;

    // Direct path to the markdown file
    // We'll try multiple possible filenames since we don't have the server to find the right one
    const possiblePaths = [];

    // First, try the exact file patterns we found in the directory structure
    // These are the most likely to succeed based on the actual file naming convention
    if (category === "daily-life") {
      // For Daily_Life, we have files like chapter01_morning_greetings.md
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_morning_greetings.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_family_friends.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_home_living.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_daily_shopping.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_transport_vehicles.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_health_wellness.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_weather_seasons.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_leisure_hobbies.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_nutrition_meals.md`
      );
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_special_diets.md`
      );
    }

    // Then add the original patterns as fallbacks
    possiblePaths.push(
      `chapters/${categoryDir}/${chapterFilePattern}content.md`
    );
    possiblePaths.push(`chapters/${categoryDir}/chapter${paddedNumber}.md`);
    possiblePaths.push(`chapters/${categoryDir}/chapter${chapterNumber}.md`);

    // Add a wildcard search for files that start with the chapter number
    // This will help find files like chapter01_morning_greetings.md
    // First, try category-specific meta tag
    const categoryMetaName = `chapter-files-${category}`;
    let files = document.querySelectorAll(`meta[name="${categoryMetaName}"]`);

    // If category-specific meta tag doesn't exist, fall back to the generic one
    if (!files || files.length === 0) {
      console.log(
        `No category-specific meta tag found for ${category}, falling back to generic meta tag`
      );
      files = document.querySelectorAll('meta[name="chapter-files"]');
    } else {
      console.log(`Found category-specific meta tag for ${category}`);
    }

    if (files && files.length > 0) {
      const fileList = files[0].getAttribute("content");
      if (fileList) {
        const fileArray = fileList.split(",");
        const chapterPrefix = `chapter${paddedNumber}_`;

        console.log(
          `Looking for files starting with ${chapterPrefix} in meta tag`
        );
        let foundMatchingFiles = false;

        fileArray.forEach((file) => {
          if (file.trim().startsWith(chapterPrefix)) {
            foundMatchingFiles = true;
            possiblePaths.push(`chapters/${categoryDir}/${file.trim()}`);
            console.log(
              `Added possible path: chapters/${categoryDir}/${file.trim()}`
            );
          }
        });

        if (!foundMatchingFiles) {
          console.log(
            `No matching files found in meta tag for prefix ${chapterPrefix}`
          );
        }
      } else {
        console.log(`Meta tag found but content attribute is empty or missing`);
      }
    } else {
      console.log(`No meta tag found for chapter files`);
    }

    // Add a fallback for direct pattern matching based on directory listing
    const directPattern = `chapters/${categoryDir}/chapter${paddedNumber}_*.md`;
    possiblePaths.push(directPattern);

    // Use XMLHttpRequest which has better compatibility across browsers
    const markdown = await new Promise((resolve, reject) => {
      // Try to load from the first possible path
      tryLoadFile(possiblePaths, 0, resolve, reject);
    });

    // Convert markdown to HTML
    const html = convertMarkdownToHtml(markdown);

    // Hide loader
    document.getElementById("chapter-loader").style.display = "none";

    return html;
  } catch (error) {
    console.error("Error fetching chapter content:", error);
    // Hide loader
    document.getElementById("chapter-loader").style.display = "none";
    return `<div class="error">Error loading chapter content: ${error.message}</div>`;
  }
}

// Helper function to try loading from multiple possible paths
function tryLoadFile(paths, index, resolve, reject) {
  if (index >= paths.length) {
    // We've tried all paths and none worked
    console.error(
      `Could not find chapter file at any of the expected locations. Tried paths:`,
      paths
    );
    reject(
      new Error(
        `Could not find chapter file at any of the expected locations. Check console for details.`
      )
    );
    return;
  }

  // Get the current path to try
  const path = paths[index];
  console.log(`Trying to load from: ${path}`);

  // Check if this is a wildcard pattern (ends with *.md)
  if (path.endsWith("*.md")) {
    // This is a wildcard pattern, we need to try a different approach
    // Extract the directory and prefix
    const lastSlashIndex = path.lastIndexOf("/");
    const directory = path.substring(0, lastSlashIndex);
    const filePrefix = path.substring(lastSlashIndex + 1).replace("*.md", "");

    // Try to find files in the directory that match the pattern
    // Since we can't directly list files in the browser, we'll make educated guesses
    // based on common naming patterns and the actual files we found in the directory
    const commonSuffixes = [
      // Daily Life chapter suffixes (from actual files)
      "morning_greetings",
      "family_friends",
      "home_living",
      "daily_shopping",
      "transport_vehicles",
      "health_wellness",
      "weather_seasons",
      "leisure_hobbies",
      "nutrition_meals",
      "special_diets",
      "music_movies",
      "fashion_clothing",
      "friendly_small_talk",
      "holidays_traditions",
      "education_study",
      "workplace_career",
      "money_banking",
      "phones_communication",
      "basic_tech_use",
      "netiquette_social_media",
      "pets_animals",
      "environment_sustainability",
      "recycling_waste",
      "home_safety",
      "first_aid_basics",
      "local_culture",
      "offices_services",
      "online_ordering",
      "repairs_maintenance",
      "daily_conversations_review",

      // Generic suffixes as fallbacks
      "content",
      "introduction",
      "basics",
      "fundamentals",
      "overview",
    ];

    // Create an array of potential file paths
    const potentialFiles = commonSuffixes.map(
      (suffix) => `${directory}/${filePrefix}${suffix}.md`
    );

    // Try each potential file
    tryPotentialFiles(potentialFiles, 0, resolve, function () {
      // If none of the potential files worked, move to the next path
      tryLoadFile(paths, index + 1, resolve, reject);
    });
    return;
  }

  // Use XMLHttpRequest for better compatibility
  const xhr = new XMLHttpRequest();
  xhr.open("GET", path, true);

  xhr.onload = function () {
    if (xhr.status === 200) {
      // Success! We found the file
      resolve(xhr.responseText);
    } else {
      // This path didn't work, try the next one
      tryLoadFile(paths, index + 1, resolve, reject);
    }
  };

  xhr.onerror = function () {
    // This path didn't work, try the next one
    tryLoadFile(paths, index + 1, resolve, reject);
  };

  xhr.send();
}

// Helper function to try loading from potential files
function tryPotentialFiles(files, index, resolve, onAllFailed) {
  if (index >= files.length) {
    // We've tried all files and none worked
    console.error(
      `Could not find chapter file at any of the expected files. Tried files:`,
      files
    );
    onAllFailed();
    return;
  }

  // Get the current file to try
  const file = files[index];
  console.log(`Trying potential file: ${file}`);

  // Use XMLHttpRequest for better compatibility
  const xhr = new XMLHttpRequest();
  xhr.open("GET", file, true);

  xhr.onload = function () {
    if (xhr.status === 200) {
      // Success! We found the file
      resolve(xhr.responseText);
    } else {
      // This file didn't work, try the next one
      tryPotentialFiles(files, index + 1, resolve, onAllFailed);
    }
  };

  xhr.onerror = function () {
    // This file didn't work, try the next one
    tryPotentialFiles(files, index + 1, resolve, onAllFailed);
  };

  xhr.send();
}

// Function to get category information
function getCategoryInfo(category) {
  // Map category to readable name and icon
  const categoryInfo = {
    "daily-life": {
      name: "Daily Life",
      icon: "fa-calendar-day",
      color: "var(--category-daily)",
    },
    "web-development": {
      name: "Web Development",
      icon: "fa-code",
      color: "var(--category-web)",
    },
    "cleaner": {
      name: "Cleaner",
      icon: "fa-broom",
      color: "var(--category-cleaner)",
    },
    "kitchen-assistant": {
      name: "Kitchen Assistant",
      icon: "fa-utensils",
      color: "var(--category-kitchen)",
    },
    "warehouse": {
      name: "Warehouse",
      icon: "fa-warehouse",
      color: "var(--category-warehouse)",
    },
  };

  return (
    categoryInfo[category] || {
      name: category,
      icon: "fa-book",
      color: "var(--primary-color)",
    }
  );
}

// Function to update reading progress
function updateReadingProgress() {
  const progressBar = document.getElementById("reading-progress-bar");
  if (!progressBar) return;

  const windowHeight = window.innerHeight;
  const documentHeight = document.documentElement.scrollHeight - windowHeight;
  const scrollTop = window.scrollY;
  const progress = (scrollTop / documentHeight) * 100;

  progressBar.style.width = progress + "%";
}

// Function to toggle dark mode
function toggleDarkMode() {
  document.body.classList.toggle("dark-mode");

  const darkModeBtn = document.getElementById("compact-toggle-dark");

  // Store preference in localStorage
  if (document.body.classList.contains("dark-mode")) {
    localStorage.setItem("darkMode", "enabled");
    // Update button appearance and text
    if (darkModeBtn) {
      darkModeBtn.classList.add("active-tool");
      // Change icon only
      darkModeBtn.innerHTML = '<i class="fas fa-sun"></i>';
    }
  } else {
    localStorage.setItem("darkMode", "disabled");
    // Update button appearance and text
    if (darkModeBtn) {
      darkModeBtn.classList.remove("active-tool");
      // Change icon only
      darkModeBtn.innerHTML = '<i class="fas fa-moon"></i>';
    }
  }
}

// Function to toggle keyword highlighting
function toggleKeywordHighlight() {
  const keywords = document.querySelectorAll(".keyword");
  const highlightBtn = document.getElementById("compact-toggle-highlight");

  // Check if highlighting is currently enabled
  const isHighlighted = localStorage.getItem("highlightMode") === "enabled";

  if (isHighlighted) {
    // Disable highlighting
    keywords.forEach((keyword) => {
      keyword.style.backgroundColor = "transparent";
      keyword.style.color = "";
      keyword.style.padding = "";
      keyword.style.borderRadius = "";
      keyword.style.fontWeight = "";
    });

    // Change button appearance to indicate it's off
    if (highlightBtn) {
      highlightBtn.classList.remove("active-tool");
      highlightBtn.style = "";
    }

    // Store preference in localStorage
    localStorage.setItem("highlightMode", "disabled");
  } else {
    // Enable highlighting
    keywords.forEach((keyword) => {
      keyword.style.backgroundColor = "var(--keyword-highlight-color)";
      keyword.style.color = "black";
      keyword.style.padding = "0 5px";
      keyword.style.borderRadius = "3px";
      keyword.style.fontWeight = "bold";
    });

    // Change button appearance to indicate it's on
    if (highlightBtn) {
      highlightBtn.classList.add("active-tool");

      // Get the highlight color from CSS variables
      const highlightColor = getComputedStyle(document.documentElement)
        .getPropertyValue("--keyword-highlight-color")
        .trim();

      // Apply styles directly to ensure they take effect
      highlightBtn.style.backgroundColor = highlightColor;
      highlightBtn.style.color = "black";
      highlightBtn.style.fontWeight = "bold";
      highlightBtn.style.borderColor = highlightColor;
    }

    // Store preference in localStorage
    localStorage.setItem("highlightMode", "enabled");
  }

  // Force a repaint to ensure styles are applied
  if (highlightBtn) {
    highlightBtn.offsetHeight;
  }
}

// Function to set up audio players for conversations
// This function is now a stub - the actual implementation is in speech.js
function setupAudioPlayers() {
  console.log("Audio player setup is now handled by speech.js");
  // The actual implementation has been moved to speech.js
  return;
}

// Initialize the page when DOM is loaded
document.addEventListener("DOMContentLoaded", async function () {
  // Get URL parameters
  const params = getUrlParams();
  const category = params.category;
  const chapterNumber = params.chapter;

  // If we have a category and chapter number, fetch the content
  if (category && chapterNumber) {
    // Get category info
    const categoryInfo = getCategoryInfo(category);

    // Update the page title
    document.title = `Chapter ${chapterNumber} - ${categoryInfo.name} - Opiskelen Suomea`;

    // Update the category icon and name
    const categoryIcon = document.getElementById("category-icon");
    const categoryName = document.getElementById("category-name");
    const chapterTitle = document.getElementById("chapter-title");

    if (categoryIcon) {
      categoryIcon.className = `fas ${categoryInfo.icon}`;
      categoryIcon.style.color = categoryInfo.color;
    }

    if (categoryName) {
      categoryName.textContent = categoryInfo.name;
      categoryName.style.color = categoryInfo.color;
    }

    if (chapterTitle) {
      chapterTitle.textContent = `Chapter ${chapterNumber}`;
    }

    // Fetch and display the chapter content
    const chapterContent = document.getElementById("chapter-content");
    if (chapterContent) {
      const html = await fetchChapterContent(category, chapterNumber);
      chapterContent.innerHTML = html;

      // After content is loaded, check if we should highlight keywords
      if (localStorage.getItem("highlightMode") === "enabled") {
        const keywords = document.querySelectorAll(".keyword");
        keywords.forEach((keyword) => {
          keyword.style.backgroundColor = "var(--keyword-highlight-color)";
          keyword.style.color = "black";
          keyword.style.padding = "0 5px";
          keyword.style.borderRadius = "3px";
          keyword.style.fontWeight = "bold";
        });

        // Update button appearance
        const highlightBtn = document.getElementById(
          "compact-toggle-highlight"
        );
        if (highlightBtn) {
          highlightBtn.classList.add("active-tool");

          // Get the highlight color from CSS variables
          const highlightColor = getComputedStyle(document.documentElement)
            .getPropertyValue("--keyword-highlight-color")
            .trim();

          // Apply styles directly to ensure they take effect
          highlightBtn.style.backgroundColor = highlightColor;
          highlightBtn.style.color = "black";
          highlightBtn.style.fontWeight = "bold";
          highlightBtn.style.borderColor = highlightColor;
        }
      }

      // Initialize audio buttons after content is loaded
      if (typeof window.initializeAudioButtons === "function") {
        setTimeout(window.initializeAudioButtons, 500);
      }
    }
  } else {
    // If we don't have a category and chapter number, show an error
    const chapterContent = document.getElementById("chapter-content");
    if (chapterContent) {
      chapterContent.innerHTML = `<div class="error">Error: Missing category or chapter number in URL.</div>`;
    }
  }

  // Set up compact nav buttons
  const compactHighlightBtn = document.getElementById(
    "compact-toggle-highlight"
  );
  if (compactHighlightBtn) {
    compactHighlightBtn.addEventListener("click", toggleKeywordHighlight);
  }

  const compactDarkBtn = document.getElementById("compact-toggle-dark");
  if (compactDarkBtn) {
    compactDarkBtn.addEventListener("click", toggleDarkMode);
  }

  // Set up mobile menu toggle
  const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  const navLinks = document.getElementById("nav-links");

  if (mobileMenuToggle && navLinks) {
    mobileMenuToggle.addEventListener("click", function () {
      // Make sure navLinks is still valid before accessing classList
      const currentNavLinks = document.getElementById("nav-links");
      if (currentNavLinks) {
        currentNavLinks.classList.toggle("show");

        // Change icon based on menu state
        const icon = this.querySelector("i");
        if (icon && currentNavLinks.classList.contains("show")) {
          icon.classList.remove("fa-bars");
          icon.classList.add("fa-times");
        } else if (icon) {
          icon.classList.remove("fa-times");
          icon.classList.add("fa-bars");
        }
      }
    });

    // Handle dropdown clicks on mobile
    const dropdowns = document.querySelectorAll(".dropdown");
    dropdowns.forEach((dropdown) => {
      const dropbtn = dropdown.querySelector(".dropbtn");
      if (dropbtn) {
        dropbtn.addEventListener("click", function (e) {
          // Only handle click for mobile view
          if (window.innerWidth <= 768) {
            e.preventDefault();
            dropdown.classList.toggle("active");
            // Removed icon toggle functionality as requested
          }
        });
      }

      // Add click event to dropdown links to close the mobile menu
      const dropdownLinks = dropdown.querySelectorAll(".dropdown-content a");
      dropdownLinks.forEach((link) => {
        link.addEventListener("click", function () {
          if (window.innerWidth <= 768) {
            // Close the mobile menu
            const navLinks = document.getElementById("nav-links");
            if (navLinks && navLinks.classList.contains("show")) {
              navLinks.classList.remove("show");

              // Change hamburger icon back to bars
              const mobileMenuToggle =
                document.getElementById("mobile-menu-toggle");
              if (mobileMenuToggle) {
                const icon = mobileMenuToggle.querySelector("i");
                if (icon) {
                  icon.classList.remove("fa-times");
                  icon.classList.add("fa-bars");
                }
              }
            }
          }
        });
      });
    });

    // Add click event to all nav links (except dropdown buttons) to close the mobile menu
    const navLinks = document.querySelectorAll(
      ".nav-links > li > a:not(.dropbtn)"
    );
    navLinks.forEach((link) => {
      link.addEventListener("click", function () {
        if (window.innerWidth <= 768) {
          // Close the mobile menu
          const navLinksContainer = document.getElementById("nav-links");
          if (
            navLinksContainer &&
            navLinksContainer.classList.contains("show")
          ) {
            navLinksContainer.classList.remove("show");

            // Change hamburger icon back to bars
            const mobileMenuToggle =
              document.getElementById("mobile-menu-toggle");
            if (mobileMenuToggle) {
              const icon = mobileMenuToggle.querySelector("i");
              if (icon) {
                icon.classList.remove("fa-times");
                icon.classList.add("fa-bars");
              }
            }
          }
        }
      });
    });

    // Add click event to buttons in nav to close the mobile menu
    const navButtons = document.querySelectorAll(".nav-links > li > button");
    navButtons.forEach((button) => {
      button.addEventListener("click", function () {
        if (window.innerWidth <= 768) {
          // Close the mobile menu
          const navLinksContainer = document.getElementById("nav-links");
          if (
            navLinksContainer &&
            navLinksContainer.classList.contains("show")
          ) {
            navLinksContainer.classList.remove("show");

            // Change hamburger icon back to bars
            const mobileMenuToggle =
              document.getElementById("mobile-menu-toggle");
            if (mobileMenuToggle) {
              const icon = mobileMenuToggle.querySelector("i");
              if (icon) {
                icon.classList.remove("fa-times");
                icon.classList.add("fa-bars");
              }
            }
          }
        }
      });
    });
  }

  // Set up scroll event for reading progress
  window.addEventListener("scroll", updateReadingProgress);

  // Check for dark mode preference
  const darkModeBtn = document.getElementById("compact-toggle-dark");
  if (localStorage.getItem("darkMode") === "enabled") {
    document.body.classList.add("dark-mode");

    // Add active class to dark mode button and update text
    if (darkModeBtn) {
      darkModeBtn.classList.add("active-tool");
      darkModeBtn.innerHTML = '<i class="fas fa-sun"></i>';
    }
  } else {
    // Ensure dark mode button has the correct text in light mode
    if (darkModeBtn) {
      darkModeBtn.innerHTML = '<i class="fas fa-moon"></i>';
    }
  }

  // Initial reading progress update
  updateReadingProgress();
});

