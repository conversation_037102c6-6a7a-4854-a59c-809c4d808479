# Simple script to update remaining files with nested dropdown structure
Write-Host "Updating remaining files with nested dropdown structure..." -ForegroundColor Green

# List of files to update (excluding already updated ones)
$filesToUpdate = @(
    "games.html",
    "finnish_grammar\index.html"
)

# Get all HTML files in finnish_grammar subdirectories
$grammarFiles = Get-ChildItem -Path "finnish_grammar" -Filter "*.html" -Recurse | Where-Object { 
    $_.Name -ne "index.html" 
} | ForEach-Object { $_.FullName.Replace((Get-Location).Path + "\", "") }

$filesToUpdate += $grammarFiles

# Get all game files
$gameFiles = Get-ChildItem -Path "games" -Filter "*.html" | ForEach-Object { $_.FullName.Replace((Get-Location).Path + "\", "") }
$filesToUpdate += $gameFiles

$updatedCount = 0
$errorCount = 0

foreach ($file in $filesToUpdate) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Yellow
        
        try {
            $content = Get-Content -Path $file -Raw -Encoding UTF8
            $originalContent = $content
            
            # Calculate relative path prefix
            $folderDepth = ($file.Split("\").Length - 1)
            $prefix = "../" * $folderDepth
            
            # Check if file has video dropdown that needs updating
            if ($content -match 'id="channels-dropdown"' -and $content -notmatch 'dropdown-submenu') {
                Write-Host "  Updating video dropdown structure..." -ForegroundColor Cyan
                
                # Define the new nested structure
                $nestedDropdown = @"
                        <!-- Individual Channels -->
                        <a href="${prefix}video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="${prefix}video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="${prefix}video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="${prefix}video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="${prefix}video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="${prefix}video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="${prefix}video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="${prefix}video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="${prefix}video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="${prefix}video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
"@
                
                # Replace the dropdown content
                $pattern = '(<div class="dropdown-content" id="channels-dropdown">\s*)(.*?)(\s*</div>)'
                $replacement = "`$1`n$nestedDropdown`n                    `$3"
                $content = $content -replace $pattern, $replacement, 'Singleline'
                
                # Add nested dropdown JavaScript if not present
                if ($content -notmatch 'dropdown-submenu.*submenu-header') {
                    $jsCode = @"

    <!-- Nested dropdown functionality -->
    <script>
        // Handle nested dropdown menus (submenu functionality)
        const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
        dropdownSubmenus.forEach(submenu => {
            const submenuHeader = submenu.querySelector('.submenu-header');
            if (submenuHeader) {
                submenuHeader.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active submenus
                    dropdownSubmenus.forEach(otherSubmenu => {
                        if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                            otherSubmenu.classList.remove('active');
                        }
                    });
                    
                    // Toggle current submenu
                    submenu.classList.toggle('active');
                });
            }
        });

        // Close submenus when clicking outside
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.dropdown-submenu')) {
                dropdownSubmenus.forEach(submenu => {
                    submenu.classList.remove('active');
                });
            }
        });
    </script>
"@
                    
                    # Add before closing body tag
                    $content = $content -replace '(</body>)', "$jsCode`n`$1"
                }
                
                # Save the file
                if ($content -ne $originalContent) {
                    Set-Content -Path $file -Value $content -Encoding UTF8
                    Write-Host "  Successfully updated!" -ForegroundColor Green
                    $updatedCount++
                } else {
                    Write-Host "  No changes needed" -ForegroundColor Gray
                }
            } else {
                Write-Host "  No video dropdown found or already updated" -ForegroundColor Gray
            }
        }
        catch {
            Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    } else {
        Write-Host "File not found: $file" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host "`n=== UPDATE SUMMARY ===" -ForegroundColor Magenta
Write-Host "Files updated: $updatedCount" -ForegroundColor Green
Write-Host "Errors: $errorCount" -ForegroundColor Red
Write-Host "Update completed!" -ForegroundColor Green
