/* Enhanced Audio List Styles */

/* Global button transition */
:root {
    --button-transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    --play-all-color: #27ae60;
    --shuffle-color: #8e44ad;
    --repeat-one-color: #f39c12; /* Orange for Repeat One */
    --stop-color: #e74c3c;
    --next-prev-color: #3498db;
    --play-all-hover: #2ecc71;
    --shuffle-hover: #9b59b6;
    --repeat-one-hover: #f1c40f; /* Brighter orange for hover */
    --stop-hover: #e74c3c;
    --next-prev-hover: #2980b9;
}

button {
    transition: var(--button-transition);
}

/* Audio Tab Container */
.audio-tab-container {
    margin-top: 30px;
    width: 100%;
    max-width: 1200px;
    margin-left: auto;
    margin-right: auto;
}

/* Audio Tab Buttons */
.audio-tab-buttons {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
}

.audio-tab-button {
    padding: 12px 24px;
    margin: 0 8px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 600;
    font-size: 16px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.audio-tab-button:hover {
    background-color: #1a5fb4; /* Darker blue instead of white */
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: white;
    border-bottom: 2px solid #2980b9;
}

.audio-tab-button.active {
    background-color: var(--accent-color);
    color: var(--primary-color);
    font-weight: 700;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Audio Controls */
.audio-controls {
    display: flex;
    justify-content: center;
    margin-bottom: 30px;
    gap: 15px;
}

.audio-control-button {
    padding: 12px 20px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
    font-size: 15px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
}

/* Add a subtle shine effect on hover */
.audio-control-button::after {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: rgba(255, 255, 255, 0.1);
    transform: rotate(30deg);
    transition: transform 0.5s ease;
    pointer-events: none;
    opacity: 0;
}

.audio-control-button:hover::after {
    opacity: 1;
    transform: rotate(30deg) translate(10%, 10%);
}

.audio-control-button:hover {
    background-color: var(--secondary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    color: white;
    border-bottom: 2px solid #2980b9;
}

/* Specific hover styles for Play All, Shuffle, and Stop buttons */
#play-mode-button:hover {
    background-color: var(--play-all-hover);
    color: white;
}

#play-mode-button.mode-all:hover {
    background-color: var(--play-all-hover);
    color: white;
}

#play-mode-button.mode-shuffle:hover {
    background-color: var(--shuffle-hover);
    color: white;
}

#stop-button:hover {
    background-color: var(--stop-hover);
    color: white;
}

.audio-control-button.active {
    background-color: #4CAF50;
    color: white;
    font-weight: 600;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Specific active styles for each button */
#play-mode-button.active {
    background-color: #4CAF50; /* Green */
}

#play-mode-button.mode-all.active {
    background-color: #4CAF50; /* Green */
}

#play-mode-button.mode-shuffle.active {
    background-color: #9b59b6; /* Purple */
}

/* Specific hover styles for active buttons */
#play-mode-button.active:hover {
    background-color: #388E3C; /* Darker green */
}

#play-mode-button.mode-all.active:hover {
    background-color: #388E3C; /* Darker green */
}

#play-mode-button.mode-shuffle.active:hover {
    background-color: #8e44ad; /* Darker purple */
}

/* Special styles for each button type */
#play-mode-button {
    background-color: var(--repeat-one-color);
}

#play-mode-button.mode-all {
    background-color: var(--play-all-color);
}

#play-mode-button.mode-shuffle {
    background-color: var(--shuffle-color);
}

#stop-button {
    background-color: var(--stop-color);
}

/* Subtitle button styles */
#subtitle-button {
    background-color: #f44336; /* Default red (subtitles off) */
    transition: all 0.3s ease;
}

#subtitle-button.active {
    background-color: #4CAF50; /* Green when active (subtitles on) */
}

#subtitle-button:hover {
    transform: translateY(-2px) scale(1.05);
}

/* Special styles for Next and Previous buttons */
#next-button, #previous-button {
    background-color: var(--next-prev-color);
    transition: all 0.3s ease;
}

#next-button:hover, #previous-button:hover {
    background-color: var(--next-prev-hover);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.2);
}

/* Add a subtle animation for button clicks */
.audio-control-button:active {
    transform: scale(0.95);
    box-shadow: 0 2px 3px rgba(0, 0, 0, 0.1);
}

/* Audio List Container */
.audio-list-container {
    max-height: 700px;
    overflow-y: auto;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
    background-color: var(--bg-color-light);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

/* Audio List */
.audio-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

/* Audio Item */
.audio-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-radius: 8px;
    background-color: var(--bg-color);
    transition: all 0.3s ease;
    border-left: 4px solid transparent;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.audio-item:hover {
    background-color: var(--hover-color);
    transform: translateX(5px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.audio-item.playing {
    background-color: rgba(66, 133, 244, 0.15); /* Light blue background */
    border-left: 4px solid var(--primary-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    transform: translateX(8px); /* Slight shift to the right */
    position: relative;
    z-index: 2; /* Ensure it appears above other items */
}

/* Add a subtle pulsing animation to the playing item */
@keyframes pulse-border {
    0% { border-left-color: var(--primary-color); }
    50% { border-left-color: #4CAF50; }
    100% { border-left-color: var(--primary-color); }
}

.audio-item.playing {
    animation: pulse-border 2s infinite;
}

/* Audio Title */
.audio-title {
    flex-grow: 1;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    padding-right: 15px;
    line-height: 1.4;
}

.audio-title strong {
    font-weight: 700;
    color: var(--primary-color);
    margin-right: 5px;
}

.audio-category {
    font-size: 13px;
    opacity: 0.7;
    font-style: italic;
    margin-left: 5px;
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    background-color: rgba(0, 0, 0, 0.05);
}

/* Audio Play Button */
.audio-play-single {
    background-color: #f5f5f5;
    border: none;
    color: var(--text-color);
    cursor: pointer;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    font-size: 16px;
}

.audio-play-single:hover {
    background-color: var(--primary-color);
    color: white;
    transform: scale(1.1);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border: 2px solid #2980b9;
}

.audio-item.playing .audio-play-single {
    background-color: #4CAF50;
    color: white;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transform: scale(1.1);
}

/* Add a subtle glow effect to the play button when playing */
@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
    50% { box-shadow: 0 0 15px rgba(76, 175, 80, 0.8); }
    100% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.5); }
}

.audio-item.playing .audio-play-single {
    animation: glow 2s infinite;
}

/* Category Indicators */
.audio-item[data-category="daily-life"] {
    border-left-color: var(--category-daily);
}

.audio-item[data-category="web-development"] {
    border-left-color: var(--category-web);
}

.audio-item[data-category="cleaner"] {
    border-left-color: var(--category-cleaner);
}

.audio-item[data-category="kitchen-assistant"] {
    border-left-color: var(--category-kitchen);
}

.audio-item[data-category="warehouse"] {
    border-left-color: var(--category-warehouse);
}

/* Dark Mode Adjustments */
.dark-mode .audio-list-container {
    background-color: #1e1e1e;
    border-color: #333;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.dark-mode .audio-item {
    background-color: #2a2a2a;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.dark-mode .audio-item:hover {
    background-color: #333;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.dark-mode .audio-item.playing {
    background-color: rgba(66, 133, 244, 0.25); /* Slightly brighter blue for dark mode */
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    border-left: 4px solid var(--primary-color);
}

.dark-mode .audio-title {
    color: #e0e0e0;
}

.dark-mode .audio-title strong {
    color: #4285F4;
}

.dark-mode .audio-category {
    background-color: rgba(255, 255, 255, 0.1);
}

.dark-mode .audio-play-single {
    background-color: #444;
    color: #e0e0e0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.dark-mode .audio-play-single:hover {
    background-color: var(--primary-color);
    color: white;
}

.dark-mode .audio-item.playing .audio-play-single {
    background-color: #4CAF50;
    color: white;
    transform: scale(1.1);
}

/* Adjust the glow animation for dark mode */
@keyframes dark-glow {
    0% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.6); }
    50% { box-shadow: 0 0 15px rgba(76, 175, 80, 0.9); }
    100% { box-shadow: 0 0 5px rgba(76, 175, 80, 0.6); }
}

.dark-mode .audio-item.playing .audio-play-single {
    animation: dark-glow 2s infinite;
}

.dark-mode .audio-tab-button:hover {
    background-color: #2980b9; /* Brighter blue for dark mode hover */
    color: white;
}

.dark-mode .audio-tab-button.active {
    background-color: #333;
    color: white;
}

.dark-mode .audio-control-button {
    background-color: #2c3e50;
}

/* Dark mode specific button colors */
.dark-mode #play-mode-button {
    background-color: var(--play-all-color);
}

.dark-mode #play-mode-button.mode-all {
    background-color: var(--play-all-color);
}

.dark-mode #play-mode-button.mode-shuffle {
    background-color: var(--shuffle-color);
}

.dark-mode #stop-button {
    background-color: var(--stop-color);
}

.dark-mode .audio-control-button.active {
    background-color: #4CAF50;
    color: white;
}

.dark-mode #play-mode-button:hover {
    background-color: var(--play-all-hover);
}

.dark-mode #play-mode-button.mode-all:hover {
    background-color: var(--play-all-hover);
}

.dark-mode #play-mode-button.mode-shuffle:hover {
    background-color: var(--shuffle-hover);
}

.dark-mode #stop-button:hover {
    background-color: var(--stop-hover);
}

.dark-mode #next-button, .dark-mode #previous-button {
    background-color: var(--next-prev-color);
}

.dark-mode #next-button:hover, .dark-mode #previous-button:hover {
    background-color: var(--next-prev-hover);
}

/* Subtitle Container */
.subtitle-container {
    position: fixed;
    bottom: 50px;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 20px;
    text-align: center;
    font-size: 20px;
    font-weight: 500;
    line-height: 1.4;
    z-index: 1000;
    display: none;
    max-width: 80%;
    margin: 0 auto;
    border-radius: 8px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .audio-controls {
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }
    
    .audio-control-button {
        flex: 0 0 auto;
        min-width: 45px;
        width: 45px;
        height: 45px;
        padding: 0;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
    }
    
    /* Hide text in buttons, show only icons */
    .audio-control-button span.button-text {
        display: none;
    }
    
    /* Make icons slightly larger */
    .audio-control-button i {
        font-size: 18px;
        margin-right: 0;
    }
    
    .audio-title {
        font-size: 14px;
    }
    
    .audio-play-single {
        width: 36px;
        height: 36px;
    }
    
    .subtitle-container {
        max-width: 95%;
        font-size: 16px;
        padding: 12px 15px;
    }
}

/* Even smaller screens */
@media (max-width: 480px) {
    .audio-control-button {
        min-width: 40px;
        width: 40px;
        height: 40px;
    }
    
    .audio-control-button i {
        font-size: 16px;
    }
    
    .audio-tab-button {
        padding: 8px 12px;
        font-size: 14px;
    }
    
    .audio-controls {
        gap: 8px;
    }
}