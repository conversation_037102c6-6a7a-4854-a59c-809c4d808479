# PowerShell script to update all level 3 Finnish grammar files with correct dropdown structure
# This script updates navigation menus to match the partitive.html template

Write-Host "Starting update of level 3 Finnish grammar files..." -ForegroundColor Green

# Define all level 3 files to update
$level3Files = @(
    # Cases/Grammatical (skip partitive.html as it's already correct)
    "finnish_grammar/cases/grammatical/accusative.html",
    "finnish_grammar/cases/grammatical/genitive.html",
    # nominative.html already updated manually
    
    # Cases/Locative
    "finnish_grammar/cases/locative/ablative.html",
    "finnish_grammar/cases/locative/adessive.html", 
    "finnish_grammar/cases/locative/allative.html",
    "finnish_grammar/cases/locative/elative.html",
    "finnish_grammar/cases/locative/illative.html",
    "finnish_grammar/cases/locative/inessive.html",
    
    # Cases/Other
    "finnish_grammar/cases/other/abessive.html",
    "finnish_grammar/cases/other/comitative.html",
    "finnish_grammar/cases/other/essive.html",
    "finnish_grammar/cases/other/instructive.html",
    "finnish_grammar/cases/other/translative.html",
    
    # Nouns/Types
    "finnish_grammar/nouns/types/type1.html",
    "finnish_grammar/nouns/types/type2.html",
    "finnish_grammar/nouns/types/type3.html",
    "finnish_grammar/nouns/types/type4.html",
    "finnish_grammar/nouns/types/type5.html",
    
    # Verbs/Types
    "finnish_grammar/verbs/types/type1.html",
    "finnish_grammar/verbs/types/type2.html",
    "finnish_grammar/verbs/types/type3.html",
    "finnish_grammar/verbs/types/type4.html",
    "finnish_grammar/verbs/types/type5.html",
    "finnish_grammar/verbs/types/type6.html"
)

# Correct Videos dropdown structure
$correctVideosDropdown = @'
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
'@

# Correct Categories dropdown
$correctCategoriesDropdown = @'
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
'@

# Correct Entertainment dropdown
$correctEntertainmentDropdown = @'
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
'@

# Submenu JavaScript functionality
$submenuJavaScript = @'

    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });

                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });
'@

$updatedCount = 0
$errorCount = 0

foreach ($file in $level3Files) {
    if (Test-Path $file) {
        Write-Host "Processing: $file" -ForegroundColor Yellow
        
        try {
            $content = Get-Content -Path $file -Raw -Encoding UTF8
            $originalContent = $content
            
            # Update Videos dropdown - handle various existing patterns
            $content = $content -replace '(?s)<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Videos</a>.*?</li>', $correctVideosDropdown
            
            # Update Categories dropdown
            $content = $content -replace '(?s)<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>.*?</li>', $correctCategoriesDropdown
            
            # Update Entertainment dropdown
            $content = $content -replace '(?s)<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>.*?</li>', $correctEntertainmentDropdown
            
            # Add submenu JavaScript if not already present
            if ($content -notmatch "dropdown-submenu") {
                $content = $content -replace '(\s+}\s*}\);\s*</script>)', "$submenuJavaScript`$1"
            }
            
            # Only write if content changed
            if ($content -ne $originalContent) {
                Set-Content -Path $file -Value $content -Encoding UTF8
                Write-Host "  ✓ Updated successfully" -ForegroundColor Green
                $updatedCount++
            } else {
                Write-Host "  - No changes needed" -ForegroundColor Gray
            }
            
        } catch {
            Write-Host "  ✗ Error updating file: $_" -ForegroundColor Red
            $errorCount++
        }
    } else {
        Write-Host "  ✗ File not found: $file" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host "`nUpdate completed!" -ForegroundColor Green
Write-Host "Files updated: $updatedCount" -ForegroundColor Green
Write-Host "Errors: $errorCount" -ForegroundColor $(if ($errorCount -gt 0) { "Red" } else { "Green" })

if ($errorCount -eq 0) {
    Write-Host "All level 3 files have been successfully updated with the correct dropdown structure!" -ForegroundColor Green
}
