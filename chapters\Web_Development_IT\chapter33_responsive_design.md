# Chapter 33: Responsiivinen suunnittelu / Responsive Design

## Objectives / Tavoitteet
- Learn vocabulary related to responsive web design in Finnish
- Understand how to discuss mobile-friendly layouts and adaptive content
- Be able to explain responsive design principles and techniques
- Master basic conversations about creating websites that work on all devices

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Responsiivisuus - Responsiveness
2. Mukautuva - Adaptive
3. Mobiiliystävällinen - Mobile-friendly
4. Näyttökoko - Screen size
5. Mediakyselyt - Media queries
6. <PERSON>ustava ruudukko - Flexible grid
7. <PERSON><PERSON><PERSON><PERSON><PERSON> yksikkö - Relative unit
8. Pikselitiheys - Pixel density
9. Näkymäportti - Viewport
10. Kuvasuhde - Aspect ratio
11. Kosketusystävällinen - Touch-friendly
12. <PERSON><PERSON><PERSON> ensin - Mobile first
13. Työpöytäversio - Desktop version
14. Päätelaite - Device
15. Skaalautuvuus - Scalability

## Grammar Points / Kielioppi
1. **Comparative and Superlative Forms for Design**:
   - Comparing layouts and approaches
   - Example: <PERSON><PERSON>ili<PERSON><PERSON> on yksinkertaisempi kuin työpöytäversio. (The mobile version is simpler than the desktop version.)

2. **Conditional Forms for Design Decisions**:
   - Expressing design choices
   - Example: Jos näyttö on kapea, valikko piilotettaisiin. (If the screen is narrow, the menu would be hidden.)

3. **Inessive Case (-ssa/-ssä) for Design Contexts**:
   - In different environments
   - Example: Mobiililaitteissa käytetään kosketusystävällisiä elementtejä. (On mobile devices, touch-friendly elements are used.)

4. **Translative Case (-ksi) for Design Transformations**:
   - Changing into something
   - Example: Sivusto muuttuu responsiiviseksi mediakyselyiden avulla. (The site becomes responsive with the help of media queries.)

5. **Adessive Case (-lla/-llä) for Design Methods**:
   - By means of
   - Example: Responsiivisuus saavutetaan joustavalla ruudukolla. (Responsiveness is achieved with a flexible grid.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Responsive design workshop / Responsiivisen suunnittelun työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa responsiivisen suunnittelun työpajaan! Tänään opimme, miten tehdä verkkosivuja, jotka toimivat hyvin kaikilla laitteilla.<br>
<em>(ter-ve-tu-lo-a res-pon-sii-vi-sen suun-nit-te-lun työ-pa-jaan! tä-nään o-pim-me, mi-ten teh-dä verk-ko-si-vu-ja, jot-ka toi-mi-vat hy-vin kai-kil-la lait-teil-la.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen huomannut, että monet sivut näyttävät erilaisilta puhelimella ja tietokoneella.<br>
<em>(kii-tos! o-len huo-man-nut, et-tä mo-net si-vut näyt-tä-vät e-ri-lai-sil-ta pu-he-li-mel-la ja tie-to-ko-neel-la.)</em></p>

<p><strong>Ohjaaja</strong>: Juuri niin! Responsiivinen suunnittelu tarkoittaa sitä, että verkkosivu mukautuu automaattisesti eri näyttökokoihin ja laitteisiin. Tämä on nykyään välttämätöntä, koska ihmiset käyttävät verkkoa monilla eri laitteilla.<br>
<em>(juu-ri niin! res-pon-sii-vi-nen suun-nit-te-lu tar-koit-taa si-tä, et-tä verk-ko-si-vu mu-kau-tuu au-to-maat-ti-ses-ti e-ri näyt-tö-ko-koi-hin ja lait-tei-siin. tä-mä on ny-ky-ään vält-tä-mä-tön-tä, kos-ka ih-mi-set käyt-tä-vät verk-ko-a mo-nil-la e-ri lait-teil-la.)</em></p>

<p><strong>Osallistuja</strong>: Miten responsiivinen suunnittelu toteutetaan käytännössä?<br>
<em>(mi-ten res-pon-sii-vi-nen suun-nit-te-lu to-teu-te-taan käy-tän-nös-sä?)</em></p>

<p><strong>Ohjaaja</strong>: Responsiivisen suunnittelun kolme pääperiaatetta ovat joustavat ruudukot, joustavat kuvat ja mediakyselyt. Aloitetaan joustavista ruudukoista.<br>
<em>(res-pon-sii-vi-sen suun-nit-te-lun kol-me pää-pe-ri-aa-tet-ta o-vat jous-ta-vat ruu-du-kot, jous-ta-vat ku-vat ja me-di-a-ky-se-lyt. a-loi-te-taan jous-ta-vis-ta ruu-du-kois-ta.)</em></p>

<p><strong>Osallistuja</strong>: Mitä joustavat ruudukot tarkoittavat?<br>
<em>(mi-tä jous-ta-vat ruu-du-kot tar-koit-ta-vat?)</em></p>

<p><strong>Ohjaaja</strong>: Joustavat ruudukot käyttävät suhteellisia yksiköitä, kuten prosentteja, pikseleiden sijaan. Esimerkiksi, jos määrittelet elementin leveydeksi 50%, se vie aina puolet käytettävissä olevasta tilasta riippumatta näytön koosta.<br>
<em>(jous-ta-vat ruu-du-kot käyt-tä-vät suh-teel-li-si-a yk-si-köi-tä, ku-ten pro-sent-te-ja, pik-se-lei-den si-jaan. e-si-mer-kik-si, jos mää-rit-te-let e-le-men-tin le-vey-dek-si 50%, se vie ai-na puo-let käy-tet-tä-vis-sä o-le-vas-ta ti-las-ta riip-pu-mat-ta näy-tön koos-ta.)</em></p>

<p><strong>Osallistuja</strong>: Entä joustavat kuvat?<br>
<em>(en-tä jous-ta-vat ku-vat?)</em></p>

<p><strong>Osallistuja</strong>: Ymmärrän. Entä mediakyselyt?<br>
<em>(ym-mär-rän. en-tä me-di-a-ky-se-lyt?)</em></p>

<p><strong>Osallistuja</strong>: Mitä "mobiili ensin" -lähestymistapa tarkoittaa?<br>
<em>(mi-tä "mo-bii-li en-sin" -lä-hes-ty-mis-ta-pa tar-koit-taa?)</em></p>

<p><strong>Ohjaaja</strong>: "Mobiili ensin" tarkoittaa, että suunnittelet ensin mobiiliversion sivustosta ja sitten laajennat sitä suuremmille näytöille. Tämä on hyvä käytäntö, koska se pakottaa keskittymään olennaiseen sisältöön ja toiminnallisuuteen. CSS:ssä tämä tarkoittaa, että kirjoitat ensin perustyylit mobiilille ja sitten lisäät mediakyselyitä suuremmille näytöille.<br>
<em>("mo-bii-li en-sin" tar-koit-taa, et-tä suun-nit-te-let en-sin mo-bii-li-ver-si-on si-vus-tos-ta ja sit-ten laa-jen-nat si-tä suu-rem-mil-le näy-töil-le. tä-mä on hy-vä käy-tän-tö, kos-ka se pa-kot-taa kes-kit-ty-mään o-len-nai-seen si-säl-töön ja toi-min-nal-li-suu-teen. CSS:s-sä tä-mä tar-koit-taa, et-tä kir-joi-tat en-sin pe-rus-tyy-lit mo-bii-lil-le ja sit-ten li-sä-ät me-di-a-ky-se-ly-i-tä suu-rem-mil-le näy-töil-le.)</em></p>

<p><strong>Osallistuja</strong>: Entä viewport-meta-tagi? Olen kuullut, että se on tärkeä mobiililaitteille.<br>
<em>(en-tä view-port-me-ta-ta-gi? o-len kuul-lut, et-tä se on tär-ke-ä mo-bii-li-lait-teil-le.)</em></p>

<p><strong>Osallistuja</strong>: Miten voin testata sivuni responsiivisuutta?<br>
<em>(mi-ten voin tes-ta-ta si-vu-ni res-pon-sii-vi-suut-ta?)</em></p>

<p><strong>Ohjaaja</strong>: Useimmissa selaimissa on kehittäjätyökalut, joilla voit simuloida eri näyttökokoja. Esimerkiksi Chromessa voit painaa F12 ja sitten valita mobiili-ikonin. Voit myös muuttaa selainikkunan kokoa manuaalisesti. On myös hyvä testata oikeilla laitteilla, jos mahdollista.<br>
<em>(u-seim-mis-sa se-lai-mis-sa on ke-hit-tä-jä-työ-ka-lut, joil-la voit si-mu-loi-da e-ri näyt-tö-ko-ko-ja. e-si-mer-kik-si chro-mes-sa voit pai-naa F12 ja sit-ten va-li-ta mo-bii-li-i-ko-nin. voit my-ös muut-taa se-lain-ik-ku-nan ko-ko-a ma-nu-aa-li-ses-ti. on my-ös hy-vä tes-ta-ta oi-keil-la lait-teil-la, jos mah-dol-lis-ta.)</em></p>

<p><strong>Osallistuja</strong>: Entä kosketusystävällisyys? Miten varmistan, että sivuni toimii hyvin kosketusnäytöillä?<br>
<em>(en-tä kos-ke-tus-ys-tä-väl-li-syys? mi-ten var-mis-tan, et-tä si-vu-ni toi-mii hy-vin kos-ke-tus-näy-töil-lä?)</em></p>

<p><strong>Ohjaaja</strong>: Kosketusystävällisyys on tärkeää! Varmista, että klikattavat elementit, kuten painikkeet ja linkit, ovat riittävän suuria (vähintään 44x44 pikseliä). Jätä myös riittävästi tilaa elementtien väliin, jotta käyttäjä ei vahingossa klikkaa väärää kohdetta. Vältä myös hover-efektejä, jotka eivät toimi kosketusnäytöillä. (kos-ke-tus-ys-tä-väl-li-syys on tär-ke-ää! var-mis-ta, et-tä kli-kat-ta-vat e-le-men-tit, ku-ten pai-nik-keet ja lin-kit, o-vat riit-tä-vän suu-ri-a (vä-hin-tään 44x44 pik-se-li-ä). jä-tä my-ös riit-tä-väs-ti ti-laa e-le-ment-ti-en vä-liin, jot-ta käyt-tä-jä ei va-hin-gos-sa klik-kaa vää-rää koh-det-ta. väl-tä my-ös ho-ver-e-fek-te-jä, jot-ka ei-vät toi-mi kos-ke-tus-näy-töil-lä.)</p>

<p><strong>Osallistuja</strong>: Entä kuvat? Pitäisikö käyttää eri kuvia eri näyttökokoihin?<br>
<em>(en-tä ku-vat? pi-täi-si-kö käyt-tää e-ri ku-vi-a e-ri näyt-tö-ko-koi-hin?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä tietoa! Voinko nyt kokeilla tehdä responsiivisen version aiemmasta projektistani?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä tie-to-a! voin-ko nyt ko-keil-la teh-dä res-pon-sii-vi-sen ver-si-on ai-em-mas-ta pro-jek-tis-ta-ni?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita lisäämällä viewport-meta-tagi, muuta kiinteät leveydet prosenteiksi ja lisää mediakyselyt eri näyttökokoihin. Muista testata eri laitteilla tai selainsimulaattorilla.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta li-sää-mäl-lä view-port-me-ta-ta-gi, muu-ta kiin-te-ät le-vey-det pro-sen-teik-si ja li-sää me-di-a-ky-se-lyt e-ri näyt-tö-ko-koi-hin. muis-ta tes-ta-ta e-ri lait-teil-la tai se-lain-si-mu-laat-to-ril-la.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin tehdä verkkosivuistani toimivia kaikilla laitteilla.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin teh-dä verk-ko-si-vuis-ta-ni toi-mi-vi-a kai-kil-la lait-teil-la.)</em></p>
</div>

### Cultural Notes:
- Finland has one of the highest mobile internet usage rates in the world, making responsive design particularly important
- Finnish design philosophy emphasizes usability and accessibility, which aligns well with responsive design principles
- Many Finnish companies prioritize mobile-first approaches due to the high smartphone penetration in the country
- Finnish web designers often create clean, minimalist layouts that adapt well to different screen sizes
- In Finland's tech education, responsive design is taught as a fundamental skill rather than an optional enhancement


