﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Consonant Gradation - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../grammar.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../index.html#daily-life">Daily Life</a>
                        <a href="../../index.html#web-development">Web Development</a>
                        <a href="../../index.html#cleaner">Cleaner</a>
                        <a href="../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../grammar.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="index.html">Nouns</a>
            <span class="separator">></span>
            <span>Consonant Gradation</span>
        </div>
        
        <section class="grammar-section">
            <h2>Consonant Gradation in Finnish</h2>
            <p>Consonant gradation is a key feature of Finnish grammar where certain consonants change in different forms of the same word. This phenomenon affects both nouns and verbs and is an essential aspect of Finnish inflection.</p>
        </section>

        <section class="grammar-category">
            <h3>WHAT IS CONSONANT GRADATION?</h3>
            
            <div class="grammar-content">
                <p>Consonant gradation is a systematic alternation between two forms of certain consonants or consonant combinations:</p>
                
                <ul>
                    <li>The <strong>strong grade</strong> appears in certain forms of the word</li>
                    <li>The <strong>weak grade</strong> appears in other forms of the same word</li>
                </ul>
                
                <p>The alternation typically occurs in the last syllable of the word stem, before the ending.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">kukka (flower, strong grade) → kukan (of the flower, weak grade)</span></p>
                    <p><span class="finnish">poika (boy, strong grade) → pojan (of the boy, weak grade)</span></p>
                </div>
                
                <p>The main rule is that the <strong>strong grade</strong> appears in open syllables (syllables ending in a vowel), while the <strong>weak grade</strong> appears in closed syllables (syllables ending in a consonant).</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION PATTERNS</h3>
            
            <div class="grammar-content">
                <p>Here are the main consonant gradation patterns in Finnish:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Strong Grade</th>
                        <th>Weak Grade</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>kk</td>
                        <td>k</td>
                        <td>kukka → kukan (flower)</td>
                    </tr>
                    <tr>
                        <td>pp</td>
                        <td>p</td>
                        <td>kauppa → kaupan (shop)</td>
                    </tr>
                    <tr>
                        <td>tt</td>
                        <td>t</td>
                        <td>matto → maton (carpet)</td>
                    </tr>
                    <tr>
                        <td>k</td>
                        <td>-</td>
                        <td>joki → joen (river)</td>
                    </tr>
                    <tr>
                        <td>p</td>
                        <td>v</td>
                        <td>leipä → leivän (bread)</td>
                    </tr>
                    <tr>
                        <td>t</td>
                        <td>d</td>
                        <td>katu → kadun (street)</td>
                    </tr>
                    <tr>
                        <td>nk</td>
                        <td>ng</td>
                        <td>kenkä → kengän (shoe)</td>
                    </tr>
                    <tr>
                        <td>mp</td>
                        <td>mm</td>
                        <td>lampi → lammen (pond)</td>
                    </tr>
                    <tr>
                        <td>lt</td>
                        <td>ll</td>
                        <td>silta → sillan (bridge)</td>
                    </tr>
                    <tr>
                        <td>nt</td>
                        <td>nn</td>
                        <td>ranta → rannan (shore)</td>
                    </tr>
                    <tr>
                        <td>rt</td>
                        <td>rr</td>
                        <td>kerta → kerran (time, occasion)</td>
                    </tr>
                </table>
                
                <p>Note that not all consonants undergo gradation. For example, m, n, l, r, s, and v do not change.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>WHEN DOES CONSONANT GRADATION OCCUR?</h3>
            
            <div class="grammar-content">
                <p>For nouns, consonant gradation typically occurs in the following situations:</p>
                
                <h4>1. Strong grade appears in:</h4>
                <ul>
                    <li>Nominative singular (basic form): <span class="finnish">kukka</span> (flower)</li>
                    <li>Partitive singular: <span class="finnish">kukkaa</span></li>
                    <li>Illative singular: <span class="finnish">kukkaan</span></li>
                </ul>
                
                <h4>2. Weak grade appears in:</h4>
                <ul>
                    <li>Genitive singular: <span class="finnish">kukan</span> (of the flower)</li>
                    <li>Inessive singular: <span class="finnish">kukassa</span> (in the flower)</li>
                    <li>Elative singular: <span class="finnish">kukasta</span> (from the flower)</li>
                    <li>Adessive singular: <span class="finnish">kukalla</span> (on the flower)</li>
                    <li>And most other cases</li>
                </ul>
                
                <p>The pattern can be different for different noun types, and there are exceptions to these general rules.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EXAMPLES BY NOUN TYPE</h3>
            
            <div class="grammar-content">
                <h4>Type 1 (-i) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">kivi (stone)</span></p>
                    <p>Strong grade: <span class="finnish">kivi, kiveä</span> (nominative, partitive)</p>
                    <p>Weak grade: <span class="finnish">kiven</span> (genitive)</p>
                </div>
                
                <h4>Type 2 (-e) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">perhe (family)</span></p>
                    <p>Strong grade: <span class="finnish">perhe, perhettä</span> (nominative, partitive)</p>
                    <p>Weak grade: <span class="finnish">perheen</span> (genitive)</p>
                </div>
                
                <h4>Type 4 (-a/-ä) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">poika (boy)</span></p>
                    <p>Strong grade: <span class="finnish">poika, poikaa</span> (nominative, partitive)</p>
                    <p>Weak grade: <span class="finnish">pojan</span> (genitive)</p>
                </div>
                
                <h4>Type 5 (-o/-ö/-u/-y) nouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">lintu (bird)</span></p>
                    <p>Strong grade: <span class="finnish">lintu, lintua</span> (nominative, partitive)</p>
                    <p>Weak grade: <span class="finnish">linnun</span> (genitive)</p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXCEPTIONS</h3>
            
            <div class="grammar-content">
                <p>Some words have irregular consonant gradation patterns:</p>
                
                <h4>1. Words with -e stem</h4>
                <div class="grammar-example">
                    <p><span class="finnish">käsi (hand) → käden</span> (s → d)</p>
                    <p><span class="finnish">vesi (water) → veden</span> (s → d)</p>
                </div>
                
                <h4>2. Words with consonant clusters</h4>
                <div class="grammar-example">
                    <p><span class="finnish">ilta (evening) → illan</span> (lt → ll)</p>
                    <p><span class="finnish">kenkä (shoe) → kengän</span> (nk → ng)</p>
                </div>
                
                <h4>3. Words with no gradation</h4>
                <p>Some words do not undergo consonant gradation at all:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kala (fish) → kalan</span> (no change)</p>
                    <p><span class="finnish">talo (house) → talon</span> (no change)</p>
                </div>
                
                <p>It's important to note that consonant gradation is not predictable from the basic form alone. You need to learn the pattern for each word.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION IN PLURAL FORMS</h3>
            
            <div class="grammar-content">
                <p>Consonant gradation also affects plural forms of nouns:</p>
                
                <div class="grammar-example">
                    <p>Nominative plural: <span class="finnish">pojat</span> (boys) - weak grade</p>
                    <p>Genitive plural: <span class="finnish">poikien</span> (of boys) - strong grade</p>
                    <p>Partitive plural: <span class="finnish">poikia</span> (boys) - strong grade</p>
                </div>
                
                <p>The pattern in plural forms often differs from the singular pattern, and it can vary depending on the noun type.</p>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















