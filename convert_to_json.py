import json
import csv

def determine_category(finnish_word, english_word):
    """Determine the grammatical category of a word based on patterns and meaning."""
    english_lower = english_word.lower()
    
    # Verbs
    verbs = ['do', 'go', 'come', 'see', 'make', 'use', 'can', 'say', 'call', 'find', 'talk', 'give', 'search', 'take', 'receive', 'laugh']
    if english_lower in verbs:
        return "Verbs"
    
    # Adjectives
    adjectives = ['bad', 'good', 'pretty', 'ugly', 'easy', 'difficult', 'near', 'far', 'small', 'weak', 'strong', 'cold', 'hot', 'funny', 'clean', 'dirty', 'low', 'high', 'sunny', 'rainy', 'happy', 'sad', 'angry', 'cloudy', 'cool']
    if english_lower in adjectives:
        return "Adjectives"
    
    # Adverbs
    adverbs = ['today', 'yesterday', 'tomorrow']
    if english_lower in adverbs:
        return "Adverbs"
    
    # Pronouns
    pronouns = ['i', 'you', 'he', 'she', 'it', 'we', 'they', 'my', 'your', 'his', 'her', 'its', 'our', 'their']
    if english_lower in pronouns:
        return "Pronouns"
    
    # Prepositions
    prepositions = ['in', 'on', 'at', 'to', 'from', 'with', 'without', 'for', 'by', 'near', 'under', 'over']
    if english_lower in prepositions:
        return "Prepositions"
    
    # Conjunctions
    conjunctions = ['and', 'or', 'but', 'because', 'if', 'when', 'while', 'though', 'although']
    if english_lower in conjunctions:
        return "Conjunctions"
    
    # Interjections
    interjections = ['hello', 'hi', 'excuse me', "i'm sorry", 'good night', 'nice to meet you', 'how are you', 'yes', 'no', 'thank you', 'goodbye']
    if english_lower in interjections:
        return "Interjection"
    
    # Phrases
    if ' ' in english_lower or '...' in english_lower:
        return "Phrase"
    
    # Default to Nouns for most other words
    return "Nouns"

def convert_to_json():
    """Convert the Finnish_2000_formatted.txt file to JSON format."""
    data = []
    
    with open('Finnish_2000_formatted.txt', 'r', encoding='utf-8') as file:
        # Skip header line
        next(file)
        
        for line in file:
            parts = line.strip().split('\t')
            if len(parts) >= 5:
                rank = int(parts[0])
                finnish = parts[1]
                finnish_sentence = parts[2]
                english = parts[3]
                english_sentence = parts[4]
                
                category = determine_category(finnish, english)
                
                data.append({
                    "rank": rank,
                    "finnish": finnish,
                    "english": english,
                    "finnishSentence": finnish_sentence,
                    "englishSentence": english_sentence,
                    "category": category
                })
    
    # Write to JSON file
    with open('finnish_vocabulary.json', 'w', encoding='utf-8') as json_file:
        json.dump(data, json_file, ensure_ascii=False, indent=2)
    
    print(f"Converted {len(data)} vocabulary entries to JSON format.")

if __name__ == "__main__":
    convert_to_json()