﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Comitative Case - Finnish Grammar - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Comitative Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Comitative Case (Komitatiivi)</h2>
            <p>The comitative case in Finnish is used to express accompaniment or possession, meaning "with" or "together with." It is formed with the ending -ine- plus a possessive suffix. It is one of the rarest cases in modern Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMATION OF THE COMITATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The comitative case is formed by adding -ine- to the genitive stem of the word (without the final -n), followed by a possessive suffix that agrees with the subject.</p>
                
                <p>The possessive suffixes are:</p>
                <ul>
                    <li>-ni (my)</li>
                    <li>-si (your, singular)</li>
                    <li>-nsa/-nsä (his/her/its)</li>
                    <li>-mme (our)</li>
                    <li>-nne (your, plural)</li>
                    <li>-nsa/-nsä (their)</li>
                </ul>
                
                <p>Examples of words in the comitative case:</p>
                <ul>
                    <li>perhe (family) → perheineni (with my family)</li>
                    <li>ystävä (friend) → ystävinesi (with your friend)</li>
                    <li>lapsi (child) → lapsineen (with his/her child)</li>
                    <li>auto (car) → autoinemme (with our car)</li>
                    <li>koira (dog) → koirinenne (with your dogs)</li>
                </ul>
                
                <p>The comitative case is always plural in form, even when referring to a single item. This means that the -ine- ending is added to the plural stem of the word:</p>
                <ul>
                    <li>perhe → perhe + ine + ni = perheineni (with my family)</li>
                    <li>ystävä → ystäv + ine + si = ystävinesi (with your friend)</li>
                    <li>lapsi → laps + ine + en = lapsineen (with his/her child)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies tuli vaimoneen.</span> <span class="english">The man came with his wife.</span></p>
                    <p><span class="finnish">Lähden perheineni lomalle.</span> <span class="english">I'm going on vacation with my family.</span></p>
                </div>
                
                <p>Note: The comitative case is not very common in everyday Finnish. In many contexts, the postposition "kanssa" + genitive is used instead.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE COMITATIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The comitative case is used in the following situations:</p>
                
                <h4>1. To express accompaniment</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Presidentti saapui puolisoineen juhlaan.</span> <span class="english">The president arrived at the celebration with his/her spouse.</span></p>
                    <p><span class="finnish">Matkustan ystävineni Suomeen.</span> <span class="english">I travel to Finland with my friends.</span></p>
                </div>
                
                <h4>2. To express possession or inclusion</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Talo kaikkine huoneineen myytiin.</span> <span class="english">The house with all its rooms was sold.</span></p>
                    <p><span class="finnish">Kirja liitteineen maksaa 20 euroa.</span> <span class="english">The book with its appendices costs 20 euros.</span></p>
                </div>
                
                <h4>3. In formal or literary contexts</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Kuningas kaikkine hoveineen saapui linnaan.</span> <span class="english">The king with all his court arrived at the castle.</span></p>
                    <p><span class="finnish">Laiva miehistöineen katosi.</span> <span class="english">The ship with its crew disappeared.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMITATIVE VS. "KANSSA" + GENITIVE</h3>
            
            <div class="grammar-content">
                <p>In modern Finnish, the comitative case is often replaced by the postposition "kanssa" followed by a noun in the genitive case:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Comitative</th>
                        <th>"kanssa" + Genitive</th>
                        <th>English</th>
                    </tr>
                    <tr>
                        <td>vaimoineen</td>
                        <td>vaimonsa kanssa</td>
                        <td>with his wife</td>
                    </tr>
                    <tr>
                        <td>perheineni</td>
                        <td>perheeni kanssa</td>
                        <td>with my family</td>
                    </tr>
                    <tr>
                        <td>ystävinesi</td>
                        <td>ystäviesi kanssa</td>
                        <td>with your friends</td>
                    </tr>
                    <tr>
                        <td>lapsineen</td>
                        <td>lapsensa kanssa</td>
                        <td>with his/her child</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies tuli vaimoineen.</span> <span class="english">The man came with his wife. (Comitative)</span></p>
                    <p><span class="finnish">Mies tuli vaimonsa kanssa.</span> <span class="english">The man came with his wife. ("kanssa" + Genitive)</span></p>
                </div>
                
                <p>The "kanssa" + genitive construction is more common in everyday speech, while the comitative is more formal or literary.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CHARACTERISTICS</h3>
            
            <div class="grammar-content">
                <p>The comitative case has several special characteristics that make it unique among Finnish cases:</p>
                
                <h4>1. Always requires a possessive suffix</h4>
                <p>Unlike most other cases, the comitative always requires a possessive suffix that agrees with the subject of the sentence.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä tulen perheineni.</span> <span class="english">I come with my family.</span></p>
                    <p><span class="finnish">Sinä tulet perheinesi.</span> <span class="english">You come with your family.</span></p>
                    <p><span class="finnish">Hän tulee perheineen.</span> <span class="english">He/she comes with his/her family.</span></p>
                </div>
                
                <h4>2. Always plural in form</h4>
                <p>The comitative case is always plural in form, even when referring to a single item.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Mies tuli vaimoineen (not vaimoinensa).</span> <span class="english">The man came with his wife.</span></p>
                    <p><span class="finnish">Lapsi tuli koirineen (not koiraneen).</span> <span class="english">The child came with his/her dog.</span></p>
                </div>
                
                <h4>3. Limited productivity</h4>
                <p>The comitative case is not very productive in modern Finnish, meaning that it's not freely used with all nouns. It appears mainly in formal or literary contexts.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Presidentti puolisoineen (formal)</span> <span class="english">The president with his/her spouse</span></p>
                    <p><span class="finnish">Presidentti ja hänen puolisonsa (everyday)</span> <span class="english">The president and his/her spouse</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>HISTORICAL NOTES</h3>
            
            <div class="grammar-content">
                <p>The comitative case is one of the least frequently used cases in modern Finnish. Its usage has declined over time, with the "kanssa" + genitive construction taking its place in many contexts.</p>
                
                <p>In older Finnish and in formal or literary contexts, the comitative case was more common. Today, it survives mainly in formal writing, such as official announcements, invitations, and literary texts.</p>
                
                <p>The comitative case is also found in some Finnish dialects and in related Finnic languages, such as Estonian, where it may have different forms or uses.</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Tervetuloa perheinenne juhlaamme.</span> <span class="english">Welcome to our celebration with your family. (Formal invitation)</span></p>
                    <p><span class="finnish">Tervetuloa perheenne kanssa juhlaamme.</span> <span class="english">Welcome to our celebration with your family. (More common)</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















