# PowerShell script to apply nested dropdown menu structure to ALL HTML files
# This script converts the flat video dropdown to a nested structure with grouped channels

Write-Host "Starting nested dropdown update for ALL HTML files..." -ForegroundColor Green

# Get all HTML files recursively, excluding specific files
$htmlFiles = Get-ChildItem -Path "." -Filter "*.html" -Recurse | Where-Object { 
    $_.FullName -notlike "*node_modules*" -and 
    $_.Name -ne "channel_id_tool.html" -and
    $_.Name -ne "dynamic_common_words.html" -and
    $_.Name -ne "mobile-menu-test.html" -and
    $_.Name -ne "index.html" -and # Skip index.html as it's already updated
    $_.Name -ne "audio.html"       # Skip audio.html as it's already updated
}

$updatedFiles = @()
$skippedFiles = @()

# Function to calculate relative path prefix
function Get-RelativePrefix($filePath) {
    $relativePath = $filePath.Replace((Get-Location).Path + "\", "").Replace("\", "/")
    $folderDepth = ($relativePath.Split("/").Length - 1)
    return "../" * $folderDepth
}

# Define the new nested dropdown structure template
function Get-NestedDropdownContent($prefix = "") {
    return @"
                        <!-- Individual Channels -->
                        <a href="${prefix}video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="${prefix}video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="${prefix}video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="${prefix}video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="${prefix}video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="${prefix}video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        
                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="${prefix}video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="${prefix}video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="${prefix}video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="${prefix}video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>
                        
                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="${prefix}video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>
                        
                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="${prefix}video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="${prefix}video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
"@
}

# JavaScript code for nested dropdown functionality
$nestedDropdownJS = @'
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();
                
                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });
                
                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });
'@

foreach ($file in $htmlFiles) {
    Write-Host "Processing: $($file.FullName)" -ForegroundColor Yellow
    
    try {
        $content = Get-Content -Path $file.FullName -Raw -Encoding UTF8
        $originalContent = $content
        
        # Calculate relative path prefix
        $prefix = Get-RelativePrefix $file.FullName
        
        # Check if file contains the video dropdown
        if ($content -match '<div class="dropdown-content"[^>]*(?:id="channels-dropdown"[^>]*)?>' -and $content -match 'video\.html\?channel=') {
            Write-Host "  Found video dropdown, updating to nested structure..." -ForegroundColor Cyan
            
            # Get the nested dropdown content with correct prefix
            $nestedContent = Get-NestedDropdownContent $prefix
            
            # Pattern to match the entire dropdown content
            # This pattern looks for the opening div tag and captures everything until the closing div
            $pattern = '(<div class="dropdown-content"[^>]*(?:id="channels-dropdown"[^>]*)?>\s*)(.*?)(\s*</div>)'
            
            if ($content -match $pattern) {
                # Replace the content between the div tags
                $replacement = "`$1`n$nestedContent`n                    `$3"
                $content = $content -replace $pattern, $replacement
                
                # Add nested dropdown JavaScript if not already present
                if ($content -notmatch 'dropdown-submenu' -or $content -notmatch 'submenu-header') {
                    # Find the last script tag and add the nested dropdown JS before it
                    if ($content -match '</script>\s*</body>') {
                        $jsToAdd = "`n$nestedDropdownJS`n"
                        $content = $content -replace '(</script>\s*</body>)', "$jsToAdd`$1"
                    }
                }
                
                # Write the updated content back to file
                if ($content -ne $originalContent) {
                    Set-Content -Path $file.FullName -Value $content -Encoding UTF8
                    $updatedFiles += $file.FullName
                    Write-Host "  Successfully updated!" -ForegroundColor Green
                }
                else {
                    Write-Host "  No changes needed" -ForegroundColor Gray
                    $skippedFiles += $file.FullName
                }
            }
            else {
                Write-Host "  Could not match dropdown pattern" -ForegroundColor Red
                $skippedFiles += $file.FullName
            }
        }
        else {
            Write-Host "  No video dropdown found" -ForegroundColor Gray
            $skippedFiles += $file.FullName
        }
    }
    catch {
        Write-Host "  Error processing file: $($_.Exception.Message)" -ForegroundColor Red
        $skippedFiles += $file.FullName
    }
}

# Summary
Write-Host "`n=== NESTED DROPDOWN UPDATE SUMMARY ===" -ForegroundColor Magenta
Write-Host "Updated files: $($updatedFiles.Count)" -ForegroundColor Green
Write-Host "Skipped files: $($skippedFiles.Count)" -ForegroundColor Yellow

if ($updatedFiles.Count -gt 0) {
    Write-Host "`nUpdated files:" -ForegroundColor Green
    $updatedFiles | ForEach-Object { Write-Host "  $_" -ForegroundColor White }
}

if ($skippedFiles.Count -gt 0) {
    Write-Host "`nSkipped files:" -ForegroundColor Yellow
    $skippedFiles | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
}

Write-Host "`nNested dropdown update completed!" -ForegroundColor Green
