﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter - <PERSON><PERSON><PERSON><PERSON></title>
    <!-- Meta tag for Daily Life chapter file patterns -->
    <meta name="chapter-files-daily-life" content="chapter01_morning_greetings.md,chapter02_family_friends.md,chapter03_home_living.md,chapter04_daily_shopping.md,chapter05_transport_vehicles.md,chapter06_health_wellness.md,chapter07_weather_seasons.md,chapter08_leisure_hobbies.md,chapter09_nutrition_meals.md,chapter10_special_diets.md,chapter11_music_movies.md,chapter12_fashion_clothing.md,chapter13_friendly_small_talk.md,chapter14_holidays_traditions.md,chapter15_education_study.md,chapter16_workplace_career.md,chapter17_money_banking.md,chapter18_phones_communication.md,chapter19_basic_tech_use.md,chapter20_netiquette_social_media.md,chapter21_pets_animals.md,chapter22_environment_sustainability.md,chapter23_recycling_waste.md,chapter24_home_safety.md,chapter25_first_aid_basics.md,chapter26_local_culture.md,chapter27_offices_services.md,chapter28_online_ordering.md,chapter29_repairs_maintenance.md,chapter30_daily_conversations_review.md">
    
    <!-- Meta tag for Web Development chapter file patterns -->
    <meta name="chapter-files-web-development" content="chapter31_html_basics.md,chapter32_css_styling.md,chapter33_responsive_design.md,chapter34_javascript_basics.md,chapter35_dom_manipulation.md,chapter36_forms_validation.md,chapter37_ajax_api_calls.md,chapter38_databases_sql.md,chapter39_php_basics.md,chapter40_nodejs_basics.md,chapter41_react_basics.md,chapter42_web_security.md,chapter43_testing_quality_assurance.md,chapter44_version_control.md,chapter45_performance_optimization.md,chapter46_ui_design.md,chapter47_user_experience.md,chapter48_website_deployment.md,chapter49_deployment_release.md,chapter50_emerging_web_trends.md">
    
    <!-- Meta tag for Cleaner chapter file patterns -->
    <meta name="chapter-files-cleaner" content="chapter51_cleaning_tools.md,chapter52_cleaning_products.md,chapter53_home_cleaning.md,chapter54_office_cleaning.md,chapter55_hotel_cleaning.md,chapter56_industrial_cleaning.md,chapter57_floor_maintenance.md,chapter58_window_cleaning.md,chapter59_bathroom_cleaning.md,chapter60_daily_reporting.md">
    
    <!-- Meta tag for Kitchen Assistant chapter file patterns -->
    <meta name="chapter-files-kitchen-assistant" content="chapter61_kitchen_basics.md,chapter62_cooking_methods.md,chapter63_kitchen_utensils_equipment.md,chapter64_food_handling_storage.md,chapter65_kitchen_cleanliness_hygiene.md,chapter66_special_diets_allergies.md,chapter67_customer_service_kitchen.md,chapter68_work_safety_kitchen.md,chapter69_orders_inventory_management.md,chapter70_teamwork_communication_kitchen.md">
    
    <!-- Meta tag for Warehouse chapter file patterns -->
    <meta name="chapter-files-warehouse" content="chapter71_receiving_inspection.md,chapter72_storage_techniques.md,chapter73_order_picking.md,chapter74_packing_shipping.md,chapter75_warehouse_safety.md,chapter76_forklift_equipment.md,chapter77_inventory_management.md,chapter78_waste_management.md,chapter79_quality_inspections.md,chapter80_daily_reporting.md">
    
    <!-- Meta tag for all chapter file patterns (for backward compatibility) -->
    <meta name="chapter-files" content="chapter01_morning_greetings.md,chapter02_family_friends.md,chapter03_home_living.md,chapter04_daily_shopping.md,chapter05_transport_vehicles.md,chapter06_health_wellness.md,chapter07_weather_seasons.md,chapter08_leisure_hobbies.md,chapter09_nutrition_meals.md,chapter10_special_diets.md,chapter11_music_movies.md,chapter12_fashion_clothing.md,chapter13_friendly_small_talk.md,chapter14_holidays_traditions.md,chapter15_education_study.md,chapter16_workplace_career.md,chapter17_money_banking.md,chapter18_phones_communication.md,chapter19_basic_tech_use.md,chapter20_netiquette_social_media.md,chapter21_pets_animals.md,chapter22_environment_sustainability.md,chapter23_recycling_waste.md,chapter24_home_safety.md,chapter25_first_aid_basics.md,chapter26_local_culture.md,chapter27_offices_services.md,chapter28_online_ordering.md,chapter29_repairs_maintenance.md,chapter30_daily_conversations_review.md">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    
    <!-- Custom styles for enhanced mobile menu -->
    <style>
        /* Basic mobile menu styles */
        .nav-links {
            display: none;
        }
        
        .nav-links.show {
            display: block !important;
        }
        
        /* Force all submenu elements to be clickable */
        @media (max-width: 767px) {
            .mobile-menu-toggle {
                display: block;
                cursor: pointer;
            }
            
            .nav-links {
                position: absolute;
                top: 60px;
                left: 0;
                right: 0;
                background-color: white;
                flex-direction: column;
                width: 100%;
                box-shadow: 0 8px 16px rgba(0,0,0,0.1);
                z-index: 1000;
            }
            
            .nav-links.show {
                display: block !important;
            }
            
            .nav-links li {
                width: 100%;
                text-align: left;
                padding: 10px 20px;
                border-bottom: 1px solid #eee;
            }
            
            .dropdown-content {
                position: static;
                display: none;
                width: 100%;
                box-shadow: none;
                padding-left: 20px;
            }
            
            /* Ensure dropdown content is visible when dropdown has active class */
            .dropdown.active .dropdown-content {
                display: block !important;
            }
            
            /* Ensure submenu content is visible when submenu has active class */
            .dropdown-submenu.active .dropdown-content {
                display: block !important;
            }
            
            .dropdown-submenu {
                position: relative;
                width: 100%;
            }
            
            .dropdown-submenu .dropdown-content {
                display: none;
                position: static;
                width: 100%;
                padding-left: 20px;
            }
            
            .dropdown-submenu.active .dropdown-content {
                display: block !important;
            }
            
            /* Force all submenu elements to be clickable */
            .enhanced-mobile-menu .dropdown-submenu,
            .enhanced-mobile-menu .dropdown-submenu *,
            .enhanced-mobile-menu .submenu-header,
            .enhanced-mobile-menu .dropdown-content a {
                pointer-events: auto !important;
                cursor: pointer !important;
                user-select: none !important;
            }
            
            /* Prevent any hover effects from closing submenus */
            .enhanced-mobile-menu .dropdown-submenu:hover > .dropdown-content {
                display: none !important;
            }
            
            /* Only show submenu content when explicitly activated */
            .enhanced-mobile-menu .dropdown-submenu.active > .dropdown-content {
                display: block !important;
            }
            
            /* Debug styles */
            .debug-info {
                position: fixed;
                bottom: 0;
                left: 0;
                right: 0;
                background: rgba(0,0,0,0.8);
                color: #fff;
                padding: 10px;
                font-size: 12px;
                z-index: 9999;
                max-height: 30vh;
                overflow-y: auto;
            }
        }
        
        /* Dark mode adjustments */
        .dark-mode .nav-links {
            background-color: #121212;
        }
        
        .dark-mode .nav-links li {
            border-bottom: 1px solid #333;
        }
    </style>
    
    <!-- Debug script -->
    <script>
        // Create a debug log function that will display on the page
        window.debugLog = function(message) {
            // Create debug container if it doesn't exist
            let debugContainer = document.getElementById('debug-container');
            if (!debugContainer) {
                debugContainer = document.createElement('div');
                debugContainer.id = 'debug-container';
                debugContainer.className = 'debug-info';
                document.body.appendChild(debugContainer);
            }
            
            // Add the message
            const logItem = document.createElement('div');
            logItem.textContent = new Date().toLocaleTimeString() + ': ' + message;
            debugContainer.appendChild(logItem);
            
            // Scroll to bottom
            debugContainer.scrollTop = debugContainer.scrollHeight;
        };
        
        // Safe version that doesn't cause recursion
        window.safeLog = function(message) {
            // Use the original console.log
            console.info("DEBUG:", message);
            // Also add to our debug container
            window.debugLog(message);
        };
        
        // Enhanced debugging for menu state
        window.menuStateMonitor = function() {
            const navLinks = document.getElementById('nav-links');
            
            // Find dropdowns by their content
            function findDropdownByText(text) {
                const links = document.querySelectorAll('.dropbtn, .submenu-header');
                for (let link of links) {
                    if (link.textContent.includes(text)) {
                        return link.closest('.dropdown, .dropdown-submenu');
                    }
                }
                return null;
            }
            
            const videosDropdown = findDropdownByText('Videos');
            const yleSubmenu = findDropdownByText('Yle Areena');
            const kaapoSubmenu = findDropdownByText('Kaapo');
            const pikkuSubmenu = findDropdownByText('Pikku');
            const categoriesDropdown = findDropdownByText('Categories');
            const entertainmentDropdown = findDropdownByText('Entertainment');
            
            window.safeLog("MENU STATE: " + 
                          "Main menu: " + (navLinks && navLinks.classList.contains('show') ? 'show' : 'hidden') + 
                          " | Videos: " + (videosDropdown && videosDropdown.classList.contains('active') ? 'active' : 'inactive') +
                          " | Yle: " + (yleSubmenu && yleSubmenu.classList.contains('active') ? 'active' : 'inactive') +
                          " | Kaapo: " + (kaapoSubmenu && kaapoSubmenu.classList.contains('active') ? 'active' : 'inactive') +
                          " | Pikku: " + (pikkuSubmenu && pikkuSubmenu.classList.contains('active') ? 'active' : 'inactive') +
                          " | Categories: " + (categoriesDropdown && categoriesDropdown.classList.contains('active') ? 'active' : 'inactive') +
                          " | Entertainment: " + (entertainmentDropdown && entertainmentDropdown.classList.contains('active') ? 'active' : 'inactive'));
        };
        
        // Monitor for any click on the document
        document.addEventListener('DOMContentLoaded', function() {
            document.addEventListener('click', function(e) {
                window.safeLog("Document clicked: " + e.target.tagName + 
                              (e.target.className ? " class=" + e.target.className : "") + 
                              (e.target.id ? " id=" + e.target.id : ""));
                
                // Check menu state after a short delay
                setTimeout(window.menuStateMonitor, 100);
            });
            
            // Initial menu state
            window.menuStateMonitor();
        });
    </script>
</head>
<body class="chapter-body">
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="compact-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links" style="display: none;">
                <li><a href="index.html">Home</a></li>
                <li><a href="audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn" 
                       onclick="event.preventDefault(); 
                                event.stopPropagation();
                                // Only toggle the active class, let CSS handle display
                                this.closest('.dropdown').classList.toggle('active');
                                window.safeLog('Videos submenu toggled via active class');
                                return false;">Videos ▼</a>
                    <div class="dropdown-content" id="videos-submenu" style="display: none;">
                        <!-- Individual Channels -->
                        <a href="video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header" 
                               onclick="event.preventDefault();
                                        event.stopPropagation();
                                        // Only toggle the active class, let CSS handle display
                                        this.closest('.dropdown-submenu').classList.toggle('active');
                                        window.safeLog('Yle Areena submenu toggled via active class');
                                        return false;">Yle Areena ▼</a>
                            <div class="dropdown-content" id="yle-submenu" style="display: none;">
                                <a href="video.html?channel=yleareena" data-channel-key="yleareena"
                                   onclick="if(window.innerWidth <= 767) { 
                                             event.preventDefault(); 
                                             event.stopPropagation(); 
                                             window.safeLog('INLINE HANDLER: Yle Areena 1 link clicked'); 
                                             setTimeout(function() { 
                                                 window.location.href = 'video.html?channel=yleareena'; 
                                             }, 100); 
                                             return false; 
                                           }">Yle Areena 1</a>
                                <a href="video.html?channel=yleareena2" data-channel-key="yleareena2"
                                   onclick="if(window.innerWidth <= 767) { 
                                             event.preventDefault(); 
                                             event.stopPropagation(); 
                                             window.safeLog('INLINE HANDLER: Yle Areena 2 link clicked'); 
                                             setTimeout(function() { 
                                                 window.location.href = 'video.html?channel=yleareena2'; 
                                             }, 100); 
                                             return false; 
                                           }">Yle Areena 2</a>
                                <a href="video.html?channel=yleareena3" data-channel-key="yleareena3"
                                   onclick="if(window.innerWidth <= 767) { 
                                             event.preventDefault(); 
                                             event.stopPropagation(); 
                                             window.safeLog('INLINE HANDLER: Yle Areena 3 link clicked'); 
                                             setTimeout(function() { 
                                                 window.location.href = 'video.html?channel=yleareena3'; 
                                             }, 100); 
                                             return false; 
                                           }">Yle Areena 3</a>
                                <a href="video.html?channel=yleareena4" data-channel-key="yleareena4"
                                   onclick="if(window.innerWidth <= 767) { 
                                             event.preventDefault(); 
                                             event.stopPropagation(); 
                                             window.safeLog('INLINE HANDLER: Yle Areena 4 link clicked'); 
                                             setTimeout(function() { 
                                                 window.location.href = 'video.html?channel=yleareena4'; 
                                             }, 100); 
                                             return false; 
                                           }">Yle Areena 4</a>
                                <a href="video.html?channel=yleareena5" data-channel-key="yleareena5"
                                   onclick="if(window.innerWidth <= 767) { 
                                             event.preventDefault(); 
                                             event.stopPropagation(); 
                                             window.safeLog('INLINE HANDLER: Yle Areena 5 link clicked'); 
                                             setTimeout(function() { 
                                                 window.location.href = 'video.html?channel=yleareena5'; 
                                             }, 100); 
                                             return false; 
                                           }">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header"
                               onclick="event.preventDefault();
                                        event.stopPropagation();
                                        // Only toggle the active class, let CSS handle display
                                        this.closest('.dropdown-submenu').classList.toggle('active');
                                        window.safeLog('Kaapo submenu toggled via active class');
                                        return false;">Kaapo - WildBrain ▼</a>
                            <div class="dropdown-content" id="kaapo-submenu" style="display: none;">
                                <a href="video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header"
                               onclick="event.preventDefault();
                                        event.stopPropagation();
                                        // Only toggle the active class, let CSS handle display
                                        this.closest('.dropdown-submenu').classList.toggle('active');
                                        window.safeLog('Pikku Kakkonen submenu toggled via active class');
                                        return false;">Yle Pikku Kakkonen ▼</a>
                            <div class="dropdown-content" id="pikku-submenu" style="display: none;">
                                <a href="video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="finnish_grammar/index.html">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn"
                       onclick="event.preventDefault();
                                event.stopPropagation();
                                // Only toggle the active class, let CSS handle display
                                this.closest('.dropdown').classList.toggle('active');
                                window.safeLog('Categories submenu toggled via active class');
                                return false;">Categories ▼</a>
                    <div class="dropdown-content" id="categories-submenu" style="display: none;">
                        <a href="index.html#daily-life">Daily Life</a>
                        <a href="index.html#web-development">Web Development</a>
                        <a href="index.html#cleaner">Cleaner</a>
                        <a href="index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn"
                       onclick="event.preventDefault();
                                event.stopPropagation();
                                // Only toggle the active class, let CSS handle display
                                this.closest('.dropdown').classList.toggle('active');
                                window.safeLog('Entertainment submenu toggled via active class');
                                return false;">Entertainment ▼</a>
                    <div class="dropdown-content" id="entertainment-submenu" style="display: none;">
                        <a href="games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="compact-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="compact-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>



    <!-- Simple test buttons -->
    <div style="position: fixed; bottom: 10px; right: 10px; z-index: 10000; display: flex; gap: 10px; flex-wrap: wrap;">
        <button id="test-button" style="background: blue; color: white; padding: 10px; border: none; 
                                       border-radius: 5px; font-weight: bold; margin-bottom: 5px;"
                onclick="alert('JavaScript is working!'); return false;">
            Test JS
        </button>
        
        <button id="menu-toggle-button" style="background: green; color: white; padding: 10px; border: none; 
                                              border-radius: 5px; font-weight: bold; margin-bottom: 5px;"
                onclick="document.getElementById('nav-links').style.display = 
                         document.getElementById('nav-links').style.display === 'block' ? 'none' : 'block'; 
                         return false;">
            Toggle Menu
        </button>
        
        <button id="videos-toggle-button" style="background: purple; color: white; padding: 10px; border: none; 
                                               border-radius: 5px; font-weight: bold; margin-bottom: 5px;"
                onclick="var content = document.getElementById('channels-dropdown');
                         content.style.display = content.style.display === 'block' ? 'none' : 'block';
                         alert('Videos dropdown toggled to: ' + content.style.display);
                         return false;">
            Toggle Videos
        </button>
        
        <button id="debug-info-button" style="background: red; color: white; padding: 10px; border: none; 
                                            border-radius: 5px; font-weight: bold; margin-bottom: 5px;"
                onclick="alert('Videos dropdown display: ' + document.getElementById('channels-dropdown').style.display + 
                              '\nNav links display: ' + document.getElementById('nav-links').style.display);
                         return false;">
            Debug Info
        </button>
    </div>

    <div class="container chapter-layout-full">
        <main class="chapter-main-full">
            <div id="chapter-loader" class="loader"></div>

            <div id="chapter-container">
                <div class="chapter-progress-container">
                    <div class="chapter-progress-bar" id="reading-progress"></div>
                </div>

                <div class="chapter-header-navigation">
                    <button id="prev-chapter-top" class="nav-button" title="Previous Chapter"><i class="fas fa-chevron-left"></i><span class="button-text">&nbsp; Previous</span></button>
                    <div id="table-of-contents-dropdown">
                        <button class="toc-button"><i class="fas fa-list"></i><span class="button-text">&nbsp; Contents</span></button>
                        <div id="table-of-contents" class="toc-dropdown-content">
                            <!-- Table of contents will be generated dynamically -->
                            <div class="toc-placeholder">Loading contents...</div>
                        </div>
                    </div>
                    <button id="next-chapter-top" class="nav-button" title="Next Chapter"><span class="button-text">Next &nbsp;</span><i class="fas fa-chevron-right"></i></button>
                </div>

                <div id="chapter-content" class="chapter-content">
                    <!-- Chapter content will be loaded here -->
                </div>

                <div class="chapter-footer-navigation">
                    <button id="prev-chapter-bottom" class="nav-button" title="Previous Chapter"><i class="fas fa-chevron-left"></i><span class="button-text">&nbsp; Previous</span></button>
                    <a href="index.html" class="back-to-home"><i class="fas fa-home"></i><span class="button-text">&nbsp; Home</span></a>
                    <button id="next-chapter-bottom" class="nav-button" title="Next Chapter"><span class="button-text">Next &nbsp;</span><i class="fas fa-chevron-right"></i></button>
                </div>
            </div>
        </main>
    </div>

    <!-- Highlight mode notification -->
    <div id="highlight-notification" class="highlight-notification">
        <i class="fas fa-highlighter"></i> Highlight mode enabled. Select text to highlight it.
    </div>

    <footer>
        <div class="footer-content">
            <div class="footer-section">
                <h3>About Us</h3>
                <p>Opiskelen Suomea provides Finnish language learning resources specifically designed for professional environments and workplace integration in Finland.</p>
                <div class="footer-social">
                    <a href="#" class="social-icon"><i class="fab fa-facebook-f"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-twitter"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-linkedin-in"></i></a>
                    <a href="#" class="social-icon"><i class="fab fa-instagram"></i></a>
                </div>
            </div>

            <div class="footer-section">
                <h3>Categories</h3>
                <ul class="footer-links">
                    <li><a href="index.html#daily-life"><i class="fas fa-angle-right"></i> Daily Life</a></li>
                    <li><a href="index.html#web-development"><i class="fas fa-angle-right"></i> Web Development</a></li>
                    <li><a href="index.html#cleaner"><i class="fas fa-angle-right"></i> Cleaner</a></li>
                    <li><a href="index.html#kitchen-assistant"><i class="fas fa-angle-right"></i> Kitchen Assistant</a></li>
                    <li><a href="index.html#warehouse"><i class="fas fa-angle-right"></i> Warehouse</a></li>
                </ul>
            </div>

            <div class="footer-section">
                <h3>Quick Links</h3>
                <ul class="footer-links">
                    <li><a href="index.html"><i class="fas fa-angle-right"></i> Home</a></li>
                    <li><a href="audio.html"><i class="fas fa-angle-right"></i> Audio</a></li>
                    <li><a href="video.html"><i class="fas fa-angle-right"></i> Videos</a></li>
                    <li><a href="index.html#about"><i class="fas fa-angle-right"></i> About</a></li>
                    <li><a href="index.html#structure"><i class="fas fa-angle-right"></i> Learning Structure</a></li>
                </ul>
            </div>

            <div class="footer-section footer-contact">
                <h3>Contact Us</h3>
                <p><i class="fas fa-envelope"></i> <EMAIL></p>
                <p><i class="fas fa-phone"></i> +358 40 123 4567</p>
                <p><i class="fas fa-map-marker-alt"></i> Helsinki, Finland</p>
            </div>

            <div class="footer-bottom">
                <p class="copyright">&copy; 2024 Opiskelen Suomea. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="highlight.js"></script>
    <script>
        // Function to handle text selection - defined at the top to ensure it's available
        function handleTextSelection(event) {
            console.log("handleTextSelection called");
            
            if (!document.body.classList.contains('highlight-mode')) {
                console.log("Highlight mode is not enabled, returning");
                return;
            }

            // Check if we're in a chapter page by looking for the chapter-content element
            const chapterContent = document.getElementById("chapter-content");
            if (!chapterContent) {
                console.log("Not in a chapter page, returning");
                return;
            }

            // Only activate highlighting if the selection is within the chapter content
            const selection = window.getSelection();
            if (!selection || selection.rangeCount === 0) {
                console.log("No selection found, returning");
                return;
            }

            const range = selection.getRangeAt(0);
            const container = range.commonAncestorContainer;

            // Check if the selection is within the chapter content
            if (
                chapterContent.contains(container) ||
                chapterContent.contains(container.parentNode)
            ) {
                console.log("Selection is valid, proceeding with highlighting");
                
                // Use the highlightSelectedText function from highlight.js
                if (typeof highlightSelectedText === 'function') {
                    console.log("Using highlightSelectedText function from highlight.js");
                    highlightSelectedText();
                } else {
                    console.error('highlightSelectedText function not found, using fallback');
                    
                    // Fallback implementation if highlightSelectedText is not available
                    if (selection.toString().length > 0) {
                        // Create a span element to wrap the selected text
                        const range = selection.getRangeAt(0);
                        const selectedText = range.extractContents();
                        const span = document.createElement('span');
                        span.className = 'user-highlight';
                        span.appendChild(selectedText);
                        range.insertNode(span);

                        // Clear the selection
                        selection.removeAllRanges();
                        console.log("Highlighting successful using fallback method");
                    }
                }
            } else {
                console.log("Selection is not within chapter content");
            }
        }

        // Function to remove all highlights
        function removeAllHighlights() {
            const highlights = document.querySelectorAll('.user-highlight');
            highlights.forEach(highlight => {
                const parent = highlight.parentNode;
                while (highlight.firstChild) {
                    parent.insertBefore(highlight.firstChild, highlight);
                }
                parent.removeChild(highlight);
            });
        }
        
        // Function to toggle text highlighting - defined at the top to ensure it's available
        function toggleHighlight() {
            console.log("toggleHighlight called");
            
            // Get all highlight buttons
            const highlightButtons = document.querySelectorAll('[id$="-toggle-highlight"]');
            
            // Check current state based on the button's active class
            const highlightButton = document.getElementById('compact-toggle-highlight');
            const isCurrentlyHighlighted = highlightButton && highlightButton.classList.contains('active-tool');
            
            console.log("Current highlight state:", isCurrentlyHighlighted ? "enabled" : "disabled", 
                        "Button active:", highlightButton && highlightButton.classList.contains('active-tool'));
            
            if (isCurrentlyHighlighted) {
                // DISABLE HIGHLIGHTING
                console.log("Disabling highlight mode");
                
                // 1. Update DOM
                document.body.classList.remove('highlight-mode');
                document.body.classList.remove('highlight-enabled');
                document.body.style.cursor = ''; // Reset cursor
                
                // 2. Update all button appearances
                highlightButtons.forEach(button => {
                    button.classList.remove('active');
                    button.classList.remove('active-tool');
                    console.log("Removed active classes from button:", button.id);
                });
                
                // 3. Remove event listener for text selection
                document.removeEventListener('mouseup', handleTextSelection);
                
                // 4. Remove all highlights if the function exists
                if (typeof removeAllHighlights === 'function') {
                    removeAllHighlights();
                }
                
                // 5. Remove background color from Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = '';
                });
            } else {
                // ENABLE HIGHLIGHTING
                console.log("Enabling highlight mode");
                
                // 1. Update DOM
                document.body.classList.add('highlight-mode');
                document.body.style.cursor = 'text'; // Change cursor to indicate highlight mode
                
                // 2. Update all button appearances
                highlightButtons.forEach(button => {
                    const activeClass = button.classList.contains('compact-nav-button') ? 'active-tool' : 'active';
                    button.classList.add(activeClass);
                    console.log("Added", activeClass, "class to button:", button.id);
                });
                
                // 3. Show notification
                const notification = document.getElementById('highlight-notification');
                if (notification) {
                    console.log("Showing highlight notification");
                    notification.style.display = 'block';
                    setTimeout(() => {
                        notification.style.opacity = '1';
                    }, 10);
                    setTimeout(() => {
                        notification.style.opacity = '0';
                        setTimeout(() => {
                            notification.style.display = 'none';
                        }, 500);
                    }, 3000);
                }
                
                // 4. Add event listener for text selection
                document.addEventListener('mouseup', handleTextSelection);
                
                // 5. Add background color to Finnish words
                document.querySelectorAll('.finnish').forEach(function(element) {
                    element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                });
            }
        }

        // Direct implementation without any dependencies
        document.addEventListener('DOMContentLoaded', function() {
            console.log("DOM Content Loaded for chapter.html");

            // Apply dark mode from localStorage immediately
            if (localStorage.getItem('darkMode') === 'enabled') {
                document.body.classList.add('dark-mode');

                // Update all moon icons to sun icons
                document.querySelectorAll('.fa-moon').forEach(function(icon) {
                    icon.classList.remove('fa-moon');
                    icon.classList.add('fa-sun');
                });

                // Add active-tool class to dark mode toggle buttons
                const darkModeToggle = document.getElementById('compact-toggle-dark');
                const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');
                if (darkModeToggle) darkModeToggle.classList.add('active-tool');
                if (darkModeToggleMobile) darkModeToggleMobile.classList.add('active-tool');
            }

            // Highlight mode initialization is now handled in a single place at the end of the script

            // Function to handle dark mode toggle
            function handleDarkModeToggle(event) {
                // Prevent default behavior
                event.preventDefault();
                event.stopPropagation();

                console.log("Toggle dark mode clicked");

                // Get dark mode toggle buttons
                const darkModeToggle = document.getElementById('compact-toggle-dark');
                const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');

                // Toggle dark mode class
                if (document.body.classList.contains('dark-mode')) {
                    // Currently in dark mode, switch to light mode
                    document.body.classList.remove('dark-mode');
                    localStorage.setItem('darkMode', 'disabled');

                    // Update all icons
                    document.querySelectorAll('.fa-sun').forEach(function(icon) {
                        icon.classList.remove('fa-sun');
                        icon.classList.add('fa-moon');
                    });

                    // Remove active-tool class from dark mode toggle buttons
                    if (darkModeToggle) darkModeToggle.classList.remove('active-tool');
                    if (darkModeToggleMobile) darkModeToggleMobile.classList.remove('active-tool');

                    console.log("Switched to light mode");
                } else {
                    // Currently in light mode, switch to dark mode
                    document.body.classList.add('dark-mode');
                    localStorage.setItem('darkMode', 'enabled');

                    // Update all icons
                    document.querySelectorAll('.fa-moon').forEach(function(icon) {
                        icon.classList.remove('fa-moon');
                        icon.classList.add('fa-sun');
                    });

                    // Add active-tool class to dark mode toggle buttons
                    if (darkModeToggle) darkModeToggle.classList.add('active-tool');
                    if (darkModeToggleMobile) darkModeToggleMobile.classList.add('active-tool');

                    console.log("Switched to dark mode");
                }

                console.log("Dark mode is now:", document.body.classList.contains('dark-mode') ? 'enabled' : 'disabled');
                console.log("localStorage darkMode is now:", localStorage.getItem('darkMode'));

                return false;
            }

            // Add click event to desktop toggle button
            const darkModeToggle = document.getElementById('compact-toggle-dark');
            if (darkModeToggle) {
                darkModeToggle.addEventListener('click', handleDarkModeToggle, true);
            }

            // Add click event to mobile toggle button
            const darkModeToggleMobile = document.getElementById('compact-toggle-dark-mobile');
            if (darkModeToggleMobile) {
                darkModeToggleMobile.addEventListener('click', handleDarkModeToggle, true);
            }

            // Highlight toggle - use the toggleHighlight function for consistency
            const highlightToggle = document.getElementById('compact-toggle-highlight');
            if (highlightToggle) {
                highlightToggle.addEventListener('click', function() {
                    // Call the toggleHighlight function to ensure consistent behavior
                    toggleHighlight();
                });
            }

            // handleTextSelection function is now defined at the top of the script

            // removeAllHighlights function is now defined at the top of the script
        });
    </script>
    <script src="chapter-new.js"></script>
    <script src="chapter-navigation.js"></script>
    <script src="speech-new.js"></script>

    <!-- Nested dropdown functionality -->
    <script>
        // Handle nested dropdown menus (submenu functionality)
        const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
        const navLinks = document.getElementById('nav-links'); // Define navLinks here
        
        // Note: This code is now handled in the DOMContentLoaded event listener below
        // to ensure proper event handling for mobile menus
        /*
        dropdownSubmenus.forEach(submenu => {
            const submenuHeader = submenu.querySelector('.submenu-header');
            if (submenuHeader) {
                submenuHeader.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    // Only handle submenu on desktop or when mobile menu is open
                    if (window.innerWidth > 767 || (navLinks && navLinks.classList.contains('show'))) {
                        // Close other active submenus
                        dropdownSubmenus.forEach(otherSubmenu => {
                            if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                                otherSubmenu.classList.remove('active');
                            }
                        });

                        // Toggle current submenu
                        submenu.classList.toggle('active');
                    }
                });
            }
        
        // This code has been moved to the DOMContentLoaded event listener below
        */
    </script>
        // No longer using localStorage for highlight state
        document.addEventListener('DOMContentLoaded', function() {
            console.log("Highlight state will be determined by button state only");
            
            // Make sure highlight mode is disabled by default
            document.body.classList.remove('highlight-mode');
            document.body.classList.remove('highlight-enabled');
            
            // Update button appearance - ensure it's in the correct state
            const highlightButtons = document.querySelectorAll('[id$="-toggle-highlight"]');
            highlightButtons.forEach(button => {
                // Check if the button has the active class
                if (button.classList.contains('active-tool') || button.classList.contains('active')) {
                    // If button is active, enable highlight mode
                    document.body.classList.add('highlight-mode');
                    document.body.style.cursor = 'text';
                    document.addEventListener('mouseup', handleTextSelection);
                    
                    // Add background color to Finnish words if they exist
                    const finnishWords = document.querySelectorAll('.finnish');
                    if (finnishWords.length > 0) {
                        finnishWords.forEach(function(element) {
                            element.style.backgroundColor = 'var(--highlight-color, #ffff99)';
                        });
                    }
                    
                    console.log("Highlight mode enabled based on button state");
                } else {
                    // If button is not active, make sure highlight mode is disabled
                    button.classList.remove('active');
                    button.classList.remove('active-tool');
                }
            });
        });        // toggleHighlight function is now defined at the top of the script
        
        // This section has been consolidated with the earlier implementation to avoid duplication

// Mobile menu toggle functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    console.log("Mobile menu elements:", mobileMenuToggle, navLinks);
    
    // The Yle Areena submenu handling has been moved to the comprehensive menu system below
    /*
    // Add specific handler for Yle Areena submenu
    // Find all submenu headers first
    const submenuHeaders = document.querySelectorAll('.submenu-header');
    let yleAreenaSubmenu = null;
    
    // Find the one that contains "Yle Areena"
    submenuHeaders.forEach(header => {
        if (header.textContent.includes('Yle Areena')) {
            yleAreenaSubmenu = header.closest('.dropdown-submenu');
        }
    });
    
    if (yleAreenaSubmenu) {
        const submenuHeader = yleAreenaSubmenu.querySelector('.submenu-header');
        const submenuContent = yleAreenaSubmenu.querySelector('.dropdown-content');
        
        // Add click handler to Yle Areena header
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                console.log("Yle Areena header clicked");
                e.preventDefault();
                e.stopPropagation();
                
                // Toggle submenu
                yleAreenaSubmenu.classList.toggle('active');
            });
        }
        
        // Add click handler to all links inside Yle Areena submenu
        if (submenuContent) {
            const links = submenuContent.querySelectorAll('a');
            links.forEach(link => {
                link.addEventListener('click', function(e) {
                    // Don't stop propagation for the actual link click (to allow navigation)
                    // but prevent it from closing the mobile menu
                    e.stopPropagation();
                });
            });
        }
    }
    */
    
    // Add direct click handler to the button
    document.querySelector('.mobile-menu-toggle').addEventListener('click', function(e) {
        console.log("Direct mobile menu toggle clicked");
        e.preventDefault();
        e.stopPropagation();
        document.getElementById('nav-links').classList.toggle('show');
        this.classList.toggle('active');
    });
    
    // Mobile menu toggle is now handled in the comprehensive event handler below
    }
    
    // All dropdown and submenu handling has been moved to the comprehensive menu system below
    /*
    // Add event handlers to ALL dropdown links to prevent menu closing
    const allDropdownLinks = document.querySelectorAll('.dropdown-content a');
    allDropdownLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            console.log("Dropdown link clicked:", this.textContent);
            // Stop propagation but allow the link to work
            e.stopPropagation();
        });
    });
    
    // Add event handlers to ALL submenu headers
    const allSubmenuHeaders = document.querySelectorAll('.submenu-header');
    allSubmenuHeaders.forEach(header => {
        header.addEventListener('click', function(e) {
            console.log("Submenu header clicked:", this.textContent);
            e.preventDefault();
            e.stopPropagation();
            
            // Toggle the active class on the parent submenu
            const submenu = this.closest('.dropdown-submenu');
            if (submenu) {
                submenu.classList.toggle('active');
            }
        });
    });
    
    // Handle dropdown menus in mobile view
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only in mobile view
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                }
            });
        }
        
        // Prevent clicks on dropdown items from closing the menu
        const dropdownContent = dropdown.querySelector('.dropdown-content');
        if (dropdownContent) {
            dropdownContent.addEventListener('click', function(e) {
                // Only prevent propagation for links in mobile view
                if (window.innerWidth <= 767 && navLinks.classList.contains('show')) {
                    // Don't prevent default for links, just stop propagation
                    e.stopPropagation();
                }
            });
        }
    });
    */
    
    // Document click handling has been moved to the comprehensive menu system below
    /*
    // Close mobile menu when clicking outside
    document.addEventListener('click', function(e) {
        // Log what was clicked for debugging
        console.log("Clicked element:", e.target);
        
        // Special handling for Yle Areena submenu
        // If we're clicking on any element inside the Yle Areena submenu, don't close the menu
        if (yleAreenaSubmenu && yleAreenaSubmenu.contains(e.target)) {
            console.log("Clicked inside Yle Areena submenu");
            // Don't close the menu
            return;
        }
        
        if (navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu
            // Don't close if clicking on dropdown elements, submenu elements, or the toggle button
            if (!navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !e.target.closest('.dropdown') && 
                !e.target.closest('.dropdown-content') && 
                !e.target.closest('.dropdown-submenu')) {
                
                console.log("Closing mobile menu");
                navLinks.classList.remove('show');
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    
                    // Change icon back to bars
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
            } else {
                console.log("Click inside menu, not closing");
            }
        }
    });
    */
});

// COMPLETELY NEW IMPLEMENTATION FOR MOBILE MENU
document.addEventListener('DOMContentLoaded', function() {
    console.log("NEW Mobile Menu Implementation Loaded");
    
    // Debug helper function to log all events
    function logEvent(eventName, target, detail) {
        console.log(`%c${eventName}`, 'background: #222; color: #bada55', target, detail || '');
    }
    
    // Get references to key elements
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    // 1. CAPTURE PHASE EVENT HANDLER - This will run before any other click handlers
    // This is the key to preventing the menu from closing
    document.addEventListener('click', function(e) {
        logEvent('CAPTURE PHASE - Document Click', e.target);
        
        // If we're in mobile view and the menu is open
        if (window.innerWidth <= 767 && navLinks && navLinks.classList.contains('show')) {
            // Check if the click is on or inside a dropdown, submenu, or their children
            const isMenuRelated = e.target.closest('.dropdown') || 
                                 e.target.closest('.dropdown-content') || 
                                 e.target.closest('.dropdown-submenu') ||
                                 e.target.closest('.submenu-header');
            
            // If the click is menu-related, prevent it from closing the menu
            if (isMenuRelated) {
                logEvent('PREVENTING DEFAULT - Menu-related click', e.target);
                e.stopPropagation();
                
                // Make sure the mobile menu stays open
                if (navLinks) {
                    navLinks.classList.add('show');
                }
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.add('active');
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-times';
                    }
                }
            }
        }
    }, true); // true = capture phase, runs before bubbling phase
    
    // 2. Mobile menu toggle
    if (mobileMenuToggle && navLinks) {
        mobileMenuToggle.addEventListener('click', function(e) {
            logEvent('Mobile Menu Toggle Clicked', this);
            e.preventDefault();
            e.stopPropagation();
            
            // Toggle the menu visibility using only the show class
            navLinks.classList.toggle('show');
            window.safeLog('Main menu toggled via show class');
            
            this.classList.toggle('active');
            
            // Toggle icon
            const icon = this.querySelector('i');
            if (icon) {
                icon.className = navLinks.classList.contains('show') ? 'fas fa-times' : 'fas fa-bars';
            }
            
            // If closing the menu, also close all dropdowns and submenus
            if (!navLinks.classList.contains('show')) {
                document.querySelectorAll('.dropdown.active, .dropdown-submenu.active').forEach(item => {
                    item.classList.remove('active');
                });
            }
        });
    }
    
    // 3. Handle dropdown buttons
    const dropdowns = document.querySelectorAll('.dropdown');
    
    dropdowns.forEach(dropdown => {
        const dropbtn = dropdown.querySelector('.dropbtn');
        if (dropbtn) {
            dropbtn.addEventListener('click', function(e) {
                // Only handle in mobile view
                if (window.innerWidth <= 767) {
                    logEvent('Dropdown Button Clicked', this, this.textContent);
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Close other active dropdowns
                    dropdowns.forEach(otherDropdown => {
                        if (otherDropdown !== dropdown && otherDropdown.classList.contains('active')) {
                            otherDropdown.classList.remove('active');
                        }
                    });
                    
                    // Always close ALL submenus when clicking on any dropdown button
                    const allSubmenus = document.querySelectorAll('.dropdown-submenu');
                    allSubmenus.forEach(submenu => {
                        submenu.classList.remove('active');
                    });
                    
                    // Toggle current dropdown
                    dropdown.classList.toggle('active');
                    
                    // IMPORTANT: Make sure the mobile menu stays open
                    if (navLinks) {
                        navLinks.classList.add('show');
                    }
                    if (mobileMenuToggle) {
                        mobileMenuToggle.classList.add('active');
                        const icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-times';
                        }
                    }
                }
            });
        }
    });
    
    // 4. Handle submenu headers
    const submenuHeaders = document.querySelectorAll('.submenu-header');
    
    submenuHeaders.forEach(header => {
        header.addEventListener('click', function(e) {
            if (window.innerWidth <= 767) {
                logEvent('Submenu Header Clicked', this, this.textContent);
                e.preventDefault();
                e.stopPropagation();
                
                // Find the parent submenu container
                const submenuContainer = this.closest('.dropdown-submenu');
                
                // Check if this submenu is currently active
                const isCurrentlyActive = submenuContainer.classList.contains('active');
                
                // Close ALL submenus first
                document.querySelectorAll('.dropdown-submenu').forEach(submenu => {
                    submenu.classList.remove('active');
                });
                
                // Only open this submenu if it wasn't already active
                if (!isCurrentlyActive) {
                    submenuContainer.classList.add('active');
                }
                
                // Ensure the parent dropdown stays open
                const parentDropdown = this.closest('.dropdown');
                if (parentDropdown) {
                    parentDropdown.classList.add('active');
                }
                
                // IMPORTANT: Make sure the mobile menu stays open
                if (navLinks) {
                    navLinks.classList.add('show');
                }
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.add('active');
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-times';
                    }
                }
            }
        });
    });
    
    // 5. Handle all dropdown and submenu links
    const allLinks = document.querySelectorAll('.dropdown-content a:not(.submenu-header)');
    
    allLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            if (window.innerWidth <= 767) {
                logEvent('Dropdown/Submenu Link Clicked', this, this.textContent);
                
                // Check if this link is inside a submenu
                const isInSubmenu = this.closest('.dropdown-submenu');
                
                if (isInSubmenu) {
                    e.preventDefault();
                    e.stopPropagation();
                    
                    // Get the URL to navigate to
                    const url = this.getAttribute('href');
                    
                    // Keep the mobile menu open
                    if (navLinks) {
                        navLinks.classList.add('show');
                    }
                    if (mobileMenuToggle) {
                        mobileMenuToggle.classList.add('active');
                        const icon = mobileMenuToggle.querySelector('i');
                        if (icon) {
                            icon.className = 'fas fa-times';
                        }
                    }
                    
                    // Navigate to the link with a small delay
                    setTimeout(() => {
                        logEvent('Navigating to', url);
                        window.location.href = url;
                    }, 100);
                }
            }
        });
    });
    
    // 6. Close mobile menu when clicking outside - DISABLED FOR NOW
    /*
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 767 && navLinks && navLinks.classList.contains('show')) {
            // Check if click is outside the nav menu and not on the toggle button
            if (!navLinks.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
                logEvent('Closing Mobile Menu - Outside Click', e.target);
                navLinks.classList.remove('show');
                
                if (mobileMenuToggle) {
                    mobileMenuToggle.classList.remove('active');
                    const icon = mobileMenuToggle.querySelector('i');
                    if (icon) {
                        icon.className = 'fas fa-bars';
                    }
                }
                
                // Also close any active dropdowns and submenus
                document.querySelectorAll('.dropdown.active, .dropdown-submenu.active').forEach(item => {
                    item.classList.remove('active');
                });
            }
        }
    });
    */
    
    // New handler that doesn't close menus when clicking on submenu items
    document.addEventListener('click', function(e) {
        // Log the click for debugging
        logEvent('Document clicked', e.target);
        
        // Check if the click is inside a dropdown or submenu
        const isMenuClick = e.target.closest('.dropdown-content') || 
                           e.target.closest('.dropdown-submenu') ||
                           e.target.closest('.submenu-header');
        
        // If it's a menu click, don't close anything
        if (isMenuClick) {
            logEvent('Click inside menu - not closing', e.target);
            e.stopPropagation();
            return;
        }
        
        // Only close the menu if clicking outside the nav and not on the toggle button
        if (window.innerWidth <= 767 && navLinks && 
            !navLinks.contains(e.target) && !mobileMenuToggle.contains(e.target)) {
            logEvent('Closing Mobile Menu - Outside Click', e.target);
            
            // Don't close the menu for now - just log it
            // navLinks.classList.remove('show');
        }
    });
    
    // 7. Force all submenu headers and links to be clickable
    document.querySelectorAll('.submenu-header, .dropdown-submenu .dropdown-content a').forEach(el => {
        el.style.pointerEvents = 'auto';
        el.style.cursor = 'pointer';
    });
    
    // 8. Add a special class to indicate our new implementation is active
    document.body.classList.add('enhanced-mobile-menu');
    
    // Log that initialization is complete
    console.log('Mobile menu enhancement complete');
});
    });
});
});
</script>
</body>
</html>











