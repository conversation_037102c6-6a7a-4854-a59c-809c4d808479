# Chapter 31: HTML-perusteet / HTML Basics

## Objectives / Tavoitteet
- Learn vocabulary related to HTML and web page structure in Finnish
- Understand how to discuss basic HTML elements and their purposes
- Be able to explain the structure of a simple web page
- Master basic conversations about HTML coding

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Verkkosivu - Web page
2. Elementti - Element
3. Tagi - Tag
4. Otsikko - Heading
5. Kappale - Paragraph
6. Linkki - Link
7. Kuva - Image
8. Lista - List
9. Taulukko - Table
10. Lomake - Form
11. Attribuutti - Attribute
12. <PERSON>s<PERSON>kk<PERSON>inen - Nested
13. Dokumentti - Document
14. Rakenne - Structure
15. Merkintäkieli - Markup language

## Grammar Points / Kielioppi
1. **Technical Verbs in Finnish**:
   - Merkitä (to mark), määritellä (to define), koodata (to code)
   - Example: Merkitsen otsikon h1-tagilla. (I mark the heading with an h1 tag.)

2. **Inessive Case (-ssa/-ssä) for Coding Contexts**:
   - Location in code
   - Example: HTML-dokumentissa on head- ja body-osiot. (In an HTML document, there are head and body sections.)

3. **Translative Case (-ksi) for Transformations**:
   - Becoming something
   - Example: Teksti muuttuu linkiksi, kun lisäät a-tagin. (The text becomes a link when you add an a tag.)

4. **Genitive Case for Technical Relationships**:
   - Possession or connection
   - Example: Elementin attribuutti määrittelee sen ominaisuuksia. (An element's attribute defines its properties.)

5. **Partitive Case with Technical Quantities**:
   - Partial objects
   - Example: Sivulla on monta elementtiä. (The page has many elements.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: HTML coding lesson / HTML-koodauksen oppitunti

<div class="conversation">
<p><strong>Opettaja</strong>: Tervetuloa HTML-perusteiden kurssille! Tänään opimme, mikä HTML on ja miten sitä käytetään.<br>
<em>(ter-ve-tu-lo-a HTML-pe-rus-tei-den kurs-sil-le! tä-nään o-pim-me, mi-kä HTML on ja mi-ten si-tä käy-te-tään.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos! Olen innoissani oppimassa verkkosivujen tekemistä.<br>
<em>(kii-tos! o-len in-nois-sa-ni op-pi-mas-sa verk-ko-si-vu-jen te-ke-mis-tä.)</em></p>

<p><strong>Opettaja</strong>: Hienoa! Aloitetaan perusteista. HTML on lyhenne sanoista Hypertext Markup Language. Se on merkintäkieli, jota käytetään verkkosivujen rakenteen luomiseen.<br>
<em>(hie-no-a! a-loi-te-taan pe-rus-teis-ta. HTML on ly-hen-ne sa-nois-ta hy-per-text mar-kup lan-gua-ge. se on mer-kin-tä-kie-li, jo-ta käy-te-tään verk-ko-si-vu-jen ra-ken-teen luo-mi-seen.)</em></p>

<p><strong>Opiskelija</strong>: Ymmärrän. Miten HTML-koodi kirjoitetaan?<br>
<em>(ym-mär-rän. mi-ten HTML-koo-di kir-joi-te-taan?)</em></p>

<p><strong>Opettaja</strong>: HTML-koodi koostuu elementeistä, jotka merkitään tageilla. Tagi alkaa pienempi kuin -merkillä ja päättyy suurempi kuin -merkkiin. Esimerkiksi, kappale merkitään p-tagilla.<br>
<em>(HTML-koo-di koos-tuu e-le-men-teis-tä, jot-ka mer-ki-tään ta-geil-la. ta-gi al-kaa pie-nem-pi kuin -mer-kil-lä ja päät-tyy suu-rem-pi kuin -merk-kiin. e-si-mer-kik-si, kap-pa-le mer-ki-tään p-ta-gil-la.)</em></p>

<p><strong>Opiskelija</strong>: Eli <code>&lt;p&gt;</code> on kappale-elementti?<br>
<em>(e-li p on kap-pa-le-e-le-ment-ti?)</em></p>

<p><strong>Opettaja</strong>: Kyllä, juuri niin! Useimmat elementit tarvitsevat sekä aloitus- että lopetustagit. Lopetustagin alussa on kauttaviiva. Esimerkiksi <code>&lt;p&gt;Tämä on kappale&lt;/p&gt;</code>.<br>
<em>(kyl-lä, juu-ri niin! u-seim-mat e-le-men-tit tar-vit-se-vat se-kä a-loi-tus- et-tä lo-pe-tus-ta-git. lo-pe-tus-ta-gin a-lus-sa on kaut-ta-vii-va. e-si-mer-kik-si p-tä-mä on kap-pa-le-/p.)</em></p>

<p><strong>Opiskelija</strong>: Entä muut yleiset elementit?<br>
<em>(en-tä muut y-lei-set e-le-men-tit?)</em></p>

<p><strong>Opettaja</strong>: Hyviä esimerkkejä ovat otsikot, jotka merkitään h1, h2, h3 jne. tageilla. Linkit merkitään a-tagilla, ja kuvat img-tagilla.<br>
<em>(hy-vi-ä e-si-merk-ke-jä o-vat ot-si-kot, jot-ka mer-ki-tään h1, h2, h3 jne. ta-geil-la. lin-kit mer-ki-tään a-ta-gil-la, ja ku-vat img-ta-gil-la.)</em></p>

<p><strong>Opiskelija</strong>: Miten linkki luodaan?<br>
<em>(mi-ten link-ki luo-daan?)</em></p>

<p><strong>Opettaja</strong>: Linkki luodaan a-tagilla ja href-attribuutilla. Esimerkiksi: <code>&lt;a href="https://www.example.com"&gt;Linkki&lt;/a&gt;</code>. Href-attribuutti määrittelee, mihin linkki johtaa.<br>
<em>(link-ki luo-daan a-ta-gil-la ja href-at-tri-buu-til-la. e-si-mer-kik-si: a href="https://www.example.com"&gt;link-ki&lt;/a&gt;. href-at-tri-buut-ti mää-rit-te-lee, mi-hin link-ki joh-taa.)</em></p>

<p><strong>Opiskelija</strong>: Entä kuva?<br>
<em>(en-tä ku-va?)</em></p>

<p><strong>Opettaja</strong>: Kuva lisätään img-tagilla ja src-attribuutilla. Esimerkiksi: <code>&lt;img src="kuva.jpg" alt="Kuvaus kuvasta"&gt;</code>. Alt-attribuutti on tärkeä saavutettavuuden kannalta.<br>
<em>(ku-va li-sä-tään img-ta-gil-la ja src-at-tri-buu-til-la. e-si-mer-kik-si: img src="ku-va.jpg" alt="ku-va-us ku-vas-ta"&gt;. alt-at-tri-buut-ti on tär-ke-ä saa-vu-tet-ta-vuu-den kan-nal-ta.)</em></p>

<p><strong>Opiskelija</strong>: Miten kokonainen verkkosivu rakennetaan?<br>
<em>(mi-ten ko-ko-nai-nen verk-ko-si-vu ra-ken-ne-taan?)</em></p>

<p><strong>Opettaja</strong>: Kokonainen HTML-dokumentti alkaa <code>&lt;!DOCTYPE html&gt;</code> -määrittelyllä. Sitten tulee html-elementti, jonka sisällä on head- ja body-elementit. Head sisältää metatietoja, ja body sisältää kaiken, mitä käyttäjä näkee sivulla.<br>
<em>(ko-ko-nai-nen HTML-do-ku-ment-ti al-kaa &lt;!DOCTYPE html&gt; -mää-rit-te-lyl-lä. sit-ten tu-lee html-e-le-ment-ti, jon-ka si-säl-lä on head- ja bo-dy-e-le-men-tit. head si-säl-tää me-ta-tie-to-ja, ja bo-dy si-säl-tää kai-ken, mi-tä käyt-tä-jä nä-kee si-vul-la.)</em></p>

<p><strong>Opiskelija</strong>: Voisitko näyttää esimerkin yksinkertaisesta HTML-dokumentista?<br>
<em>(voi-sit-ko näyt-tää e-si-mer-kin yk-sin-ker-tai-ses-ta HTML-do-ku-men-tis-ta?)</em></p>

<p><strong>Opettaja</strong>: Totta kai! Tässä on esimerkki:</p>


<!DOCTYPE html>
<html>
<head>
  <title>Sivun otsikko</title>
</head>
<body>
  <h1>Pääotsikko</h1>
  <p>Tämä on kappale tekstiä.</p>
  <a href="https://www.example.com">Linkki</a>
  <img src="kuva.jpg" alt="Kuvaus kuvasta">
</body>
</html>

<p><em>(tot-ta kai! täs-sä on e-si-merk-ki: [HTML-koodi])</em></p>

<p><strong>Opiskelija</strong>: Kiitos! Tämä selventää paljon. Miten voin testata HTML-koodia?<br>
<em>(kii-tos! tä-mä sel-ven-tää pal-jon. mi-ten voin tes-ta-ta HTML-koo-di-a?)</em></p>

<p><strong>Opettaja</strong>: Voit tallentaa koodin .html-päätteiseen tiedostoon ja avata sen selaimessa. Voit myös käyttää verkossa olevia koodieditoreita, kuten CodePen tai JSFiddle.<br>
<em>(voit tal-len-taa koo-din .html-päät-tei-seen tie-dos-toon ja a-va-ta sen se-lai-mes-sa. voit my-ös käyt-tää ver-kos-sa o-le-vi-a koo-di-e-di-to-rei-ta, ku-ten code-pen tai js-fid-dle.)</em></p>

<p><strong>Opiskelija</strong>: Entä listat ja taulukot? Miten ne tehdään?<br>
<em>(en-tä lis-tat ja tau-lu-kot? mi-ten ne teh-dään?)</em></p>

<p><strong>Opettaja</strong>: Listat tehdään ul- tai ol-elementeillä, ja jokainen listan kohta on li-elementti. Taulukot tehdään table-elementillä, joka sisältää tr-elementtejä (rivejä) ja td-elementtejä (soluja).<br>
<em>(lis-tat teh-dään ul- tai ol-e-le-men-teil-lä, ja jo-kai-nen lis-tan koh-ta on li-e-le-ment-ti. tau-lu-kot teh-dään tab-le-e-le-men-til-lä, jo-ka si-säl-tää tr-e-le-ment-te-jä (ri-ve-jä) ja td-e-le-ment-te-jä (so-lu-ja).)</em></p>

<p><strong>Opiskelija</strong>: Tämä on todella mielenkiintoista! Voinko nyt kokeilla tehdä yksinkertaisen verkkosivun?<br>
<em>(tä-mä on to-del-la mie-len-kiin-tois-ta! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen verk-ko-si-vun?)</em></p>

<p><strong>Opettaja</strong>: Ehdottomasti! Aloita luomalla perusrakenne ja lisää sitten erilaisia elementtejä. Muista, että harjoitus tekee mestarin.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta luo-mal-la pe-rus-ra-ken-ne ja li-sää sit-ten e-ri-lai-si-a e-le-ment-te-jä. muis-ta, et-tä har-joi-tus te-kee mes-ta-rin.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos paljon tästä oppitunnista! Odotan innolla, että pääsen kokeilemaan HTML-koodausta.<br>
<em>(kii-tos pal-jon täs-tä op-pi-tun-nis-ta! o-do-tan in-nol-la, et-tä pää-sen ko-kei-le-maan HTML-koo-da-us-ta.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong tech education system, with programming often taught in schools
- Finnish tech terminology often uses both Finnish terms and international English terms
- Finland has a vibrant tech industry with many web development companies
- Clear, functional design is valued in Finnish web development, reflecting Finnish design principles
- Accessibility (saavutettavuus) is taken seriously in Finnish web development, with legal requirements for public websites


