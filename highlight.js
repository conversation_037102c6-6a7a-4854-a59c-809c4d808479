// Function to highlight selected text - with single line restriction
function highlightSelectedText() {
  try {
    // Check if highlight is disabled or highlight mode is not enabled
    if (document.body.classList.contains("highlight-disabled") || !document.body.classList.contains("highlight-mode")) {
      console.log("Highlighting is disabled or highlight mode is not enabled");
      return;
    }

    console.log("Attempting to highlight selected text");
    
    const selection = window.getSelection();

    if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
      console.log("No text selected");
      return;
    }

    const range = selection.getRangeAt(0);

    // Don't highlight if selection is empty or just whitespace
    if (range.toString().trim() === "") {
      console.log("Selection is empty or just whitespace");
      return;
    }

    // Get the selected text
    const selectedText = range.toString();
    console.log("Selected text:", selectedText);

    // Check if the selection contains a newline character
    if (selectedText.includes("\n")) {
      // Show a message to the user
      alert(
        "Please highlight only one line at a time to maintain proper formatting."
      );
      selection.removeAllRanges();
      return;
    }

    // Get the highlight color from CSS variables
    const highlightColor = getComputedStyle(document.documentElement)
      .getPropertyValue("--highlight-color")
      .trim();
    console.log("Highlight color:", highlightColor);

    // Create a new range for safety
    const safeRange = range.cloneRange();

    // Check if we're dealing with a simple text node selection
    if (
      safeRange.startContainer === safeRange.endContainer &&
      safeRange.startContainer.nodeType === Node.TEXT_NODE
    ) {
      try {
        console.log("Using simple text node selection method");
        // Simple text node selection - use surroundContents
        const highlightSpan = document.createElement("span");
        highlightSpan.className = "user-highlight";
        highlightSpan.style.backgroundColor = highlightColor;
        highlightSpan.style.color = "black";

        safeRange.surroundContents(highlightSpan);
        selection.removeAllRanges();
        console.log("Highlighting successful using surroundContents");
        return;
      } catch (e) {
        console.log(
          "Simple text highlighting failed, trying fallback method:",
          e
        );
        // Continue to fallback method
      }
    }

    // Fallback method - simple text replacement
    try {
      console.log("Using fallback text replacement method");
      // Create a simple highlighted span
      const highlightSpan = document.createElement("span");
      highlightSpan.className = "user-highlight";
      highlightSpan.style.backgroundColor = highlightColor;
      highlightSpan.style.color = "black";
      highlightSpan.textContent = selectedText;

      // Replace the selection with our highlighted span
      safeRange.deleteContents();
      safeRange.insertNode(highlightSpan);

      // Clear the selection
      selection.removeAllRanges();
      console.log("Highlighting successful using fallback method");
    } catch (fallbackError) {
      console.error("All highlighting methods failed:", fallbackError);
    }
  } catch (error) {
    console.error("Highlighting failed:", error);
  }
}

// Helper function to get all text nodes within an element
function getTextNodesIn(node) {
  const textNodes = [];

  function getTextNodes(node) {
    if (node.nodeType === Node.TEXT_NODE) {
      textNodes.push(node);
    } else {
      const children = node.childNodes;
      for (let i = 0; i < children.length; i++) {
        getTextNodes(children[i]);
      }
    }
  }

  getTextNodes(node);
  return textNodes;
}

// Function to detect if the device is a touch device
function isTouchDevice() {
  return (
    "ontouchstart" in window ||
    navigator.maxTouchPoints > 0 ||
    navigator.msMaxTouchPoints > 0
  );
}

// Add event listener to enable highlighting on mouseup (when text is selected)
document.addEventListener("DOMContentLoaded", function () {
  console.log("DOM Content Loaded for highlight.js");
  
  // Check if this is a touch device
  const isTouch = isTouchDevice();

  if (!isTouch) {
    // For non-touch devices, use mouseup event
    document.addEventListener("mouseup", function (event) {
      // Only proceed if highlight mode is enabled
      if (document.body.classList.contains('highlight-mode')) {
        console.log("Highlight mode is enabled");
        
        // Get the selection
        const selection = window.getSelection();
        if (selection && selection.rangeCount > 0) {
          const range = selection.getRangeAt(0);
          const container = range.commonAncestorContainer;

          // Check if we're in a chapter page
          const chapterContent = document.getElementById("chapter-content");
          
          // If we're in a chapter page, only highlight within chapter content
          if (chapterContent) {
            // Check if the selection is within the chapter content
            if (
              chapterContent.contains(container) ||
              chapterContent.contains(container.parentNode)
            ) {
              console.log("Selection is within chapter content, calling highlightSelectedText()");
              highlightSelectedText();
            } else {
              console.log("Selection is not within chapter content");
            }
          } else {
            // For other pages, allow highlighting anywhere in the main content
            // Find the main content container based on the page
            let mainContent = document.querySelector('.main-content') || // index.html
                             document.querySelector('.audio-container') || // audio.html
                             document.querySelector('.video-container') || // video.html
                             document.querySelector('.grammar-container'); // finnish_grammar/index.html
            
            if (mainContent) {
              if (
                mainContent.contains(container) ||
                mainContent.contains(container.parentNode)
              ) {
                console.log("Selection is within main content, calling highlightSelectedText()");
                highlightSelectedText();
              } else {
                console.log("Selection is not within main content");
              }
            } else {
              // If no specific container is found, allow highlighting anywhere in the body
              console.log("No specific content container found, allowing highlighting anywhere");
              highlightSelectedText();
            }
          }
        } else {
          console.log("No valid selection found");
        }
      } else {
        console.log("Highlight mode is not enabled");
      }
    });

    console.log("Text highlighting functionality initialized for desktop");
  } else {
    // For touch devices
    console.log("Text highlighting functionality initialized for touch devices");
    
    // Add touch-specific handling if needed
  }
});

