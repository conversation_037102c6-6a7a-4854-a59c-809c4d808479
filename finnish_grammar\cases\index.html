﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Finnish Cases - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .grammar-card {
            background-color: #f9f9f9;
            border-radius: 5px;
            padding: 20px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            cursor: pointer;
            position: relative;
        }
        
        .grammar-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .grammar-card h3 {
            color: #0066cc;
            margin-top: 0;
            margin-bottom: 15px;
            font-size: 1.2em;
        }
        
        .grammar-card p {
            margin-bottom: 0;
            color: #555;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-card {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-card p {
            color: #bbb;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        /* Card link behavior */
        .grammar-card {
            text-decoration: none;
            display: block;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                        <a href="../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                        <a href="../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                        <a href="../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                        <a href="../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                        <a href="../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                        <a href="../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                        <a href="../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                        <a href="../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                        <a href="../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                        <a href="../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                    </div>
                </li>
                <li><a href="../../grammar.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../index.html#daily-life">Daily Life</a>
                        <a href="../../index.html#web-development">Web Development</a>
                        <a href="../../index.html#cleaner">Cleaner</a>
                        <a href="../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../index.html#warehouse">Warehouse</a>
                    </div>
                                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../index.html">Grammar</a>
            <span class="separator">></span>
            <span>Cases</span>
        </div>
        
        <section class="grammar-section">
            <h2>Finnish Cases</h2>
            <p>Finnish has 15 grammatical cases that are used to express relationships between words. This section covers all Finnish cases, their formation, and usage.</p>
        </section>

        <section class="grammar-category">
            <h3>GRAMMATICAL CASES</h3>
            
            <div class="grammar-grid">
                <a href="grammatical/nominative.html" class="grammar-card">
                    <h3>Nominative Case</h3>
                    <p>The basic form of nouns, used for the subject (e.g., talo - house)</p>
                </a>
                
                <a href="grammatical/genitive.html" class="grammar-card">
                    <h3>Genitive Case</h3>
                    <p>Indicates possession, formed with -n (e.g., talon - of the house)</p>
                </a>
                
                <a href="grammatical/partitive.html" class="grammar-card">
                    <h3>Partitive Case</h3>
                    <p>Used for indefinite quantities, formed with -a/-ä, -ta/-tä (e.g., taloa - house - partitive)</p>
                </a>
                
                <a href="grammatical/accusative.html" class="grammar-card">
                    <h3>Accusative Case</h3>
                    <p>Used for the direct object, takes the form of genitive or nominative</p>
                </a>
            </div>
        </section>

        <section class="grammar-category">
            <h3>LOCATIVE CASES</h3>
            
            <div class="grammar-grid">
                <a href="locative/inessive.html" class="grammar-card">
                    <h3>Inessive Case</h3>
                    <p>Indicates location inside something, formed with -ssa/-ssä (e.g., talossa - in the house)</p>
                </a>
                
                <a href="locative/elative.html" class="grammar-card">
                    <h3>Elative Case</h3>
                    <p>Indicates movement out of something, formed with -sta/-stä (e.g., talosta - from the house)</p>
                </a>
                
                <a href="locative/illative.html" class="grammar-card">
                    <h3>Illative Case</h3>
                    <p>Indicates movement into something, formed with -Vn, -hVn, -seen (e.g., taloon - into the house)</p>
                </a>
                
                <a href="locative/adessive.html" class="grammar-card">
                    <h3>Adessive Case</h3>
                    <p>Indicates location on or at something, formed with -lla/-llä (e.g., pöydällä - on the table)</p>
                </a>
                
                <a href="locative/ablative.html" class="grammar-card">
                    <h3>Ablative Case</h3>
                    <p>Indicates movement from on or at something, formed with -lta/-ltä (e.g., pöydältä - from the table)</p>
                </a>
                
                <a href="locative/allative.html" class="grammar-card">
                    <h3>Allative Case</h3>
                    <p>Indicates movement onto or to something, formed with -lle (e.g., pöydälle - onto the table)</p>
                </a>
            </div>
        </section>

        <section class="grammar-category">
            <h3>OTHER CASES</h3>
            
            <div class="grammar-grid">
                <a href="other/essive.html" class="grammar-card">
                    <h3>Essive Case</h3>
                    <p>Indicates a temporary state or role, formed with -na/-nä (e.g., lapsena - as a child)</p>
                </a>
                
                <a href="other/translative.html" class="grammar-card">
                    <h3>Translative Case</h3>
                    <p>Indicates a change of state, formed with -ksi (e.g., opettajaksi - to become a teacher)</p>
                </a>
                
                <a href="other/instructive.html" class="grammar-card">
                    <h3>Instructive Case</h3>
                    <p>Indicates means or manner, formed with -in (e.g., käsin - by hand)</p>
                </a>
                
                <a href="other/abessive.html" class="grammar-card">
                    <h3>Abessive Case</h3>
                    <p>Indicates being without something, formed with -tta/-ttä (e.g., rahatta - without money)</p>
                </a>
                
                <a href="other/comitative.html" class="grammar-card">
                    <h3>Comitative Case</h3>
                    <p>Indicates accompaniment, formed with -ine + possessive suffix (e.g., vaimoineni - with my wife)</p>
                </a>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>














