/* Games Styles for Opiskelen Suomea */

/* Games Grid */
.games-section {
    margin-top: 2rem;
}

.games-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 1.25rem;
    margin-top: 1.5rem;
}

/* Game Card */
.game-card {
    background-color: #ffffff;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #eaeaea;
    display: flex;
    flex-direction: column;
    height: 100%;
}

.game-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.game-header {
    padding: 0.8rem 1rem;
    background-color: var(--primary-color);
    color: white;
}

.game-header h3 {
    margin: 0;
    font-size: 1.2rem;
}

.game-body {
    padding: 0.8rem 1rem;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
}

.game-body p {
    margin-bottom: 0.8rem;
    color: var(--text-color);
    font-size: 0.9rem;
    line-height: 1.4;
}

.game-preview {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: auto;
    padding: 0.5rem 0;
    color: var(--primary-color);
}

.game-preview i {
    font-size: 2rem; /* Smaller icon size */
}

.game-footer {
    padding: 0.8rem;
    background-color: #f8f8f8;
    border-top: 1px solid #eaeaea;
    text-align: center;
}

.play-button {
    display: inline-block;
    background-color: var(--primary-color);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 4px;
    text-decoration: none;
    font-weight: 500;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.play-button:hover {
    background-color: #002a66;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Dark Mode Styles */
.dark-mode .game-card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border-color);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.dark-mode .game-header {
    background-color: var(--primary-color);
}

.dark-mode .game-body p {
    color: var(--dark-text-color);
}

.dark-mode .game-preview {
    color: var(--primary-color);
}

.dark-mode .game-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-top: 1px solid var(--dark-border-color);
}

.dark-mode .play-button:hover {
    background-color: #1a4a8f;
}

/* Responsive Styles */
@media (max-width: 767px) {
    .games-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 0.8rem;
    }
}

@media (max-width: 480px) {
    .games-grid {
        grid-template-columns: 1fr;
    }
}