﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Family Members - Finnish Vocabulary - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .family-tree {
            max-width: 800px;
            margin: 30px auto;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .family-tree img {
            max-width: 100%;
            height: auto;
            display: block;
            margin: 0 auto;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .family-tree {
            background-color: #2a2a2a;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="family-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="family-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="family-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Family Members</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Family Members in Finnish</h2>
            <p>Family relationships are an important part of any language. Finnish has specific terms for various family members, and like other Finnish nouns, these terms can be inflected in different cases. This page covers the most common family-related vocabulary in Finnish, their pronunciation, and how to use them in sentences.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Immediate Family</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Notes</th>
                </tr>
                <tr>
                    <td>perhe</td>
                    <td class="pronunciation">per-he</td>
                    <td>family</td>
                    <td>The general term for family</td>
                </tr>
                <tr>
                    <td>vanhemmat</td>
                    <td class="pronunciation">van-hem-mat</td>
                    <td>parents</td>
                    <td>Plural form only</td>
                </tr>
                <tr>
                    <td>äiti</td>
                    <td class="pronunciation">äi-ti</td>
                    <td>mother</td>
                    <td>Informal: "äiti"; formal: "äitini" (my mother)</td>
                </tr>
                <tr>
                    <td>isä</td>
                    <td class="pronunciation">i-sä</td>
                    <td>father</td>
                    <td>Informal: "isä"; formal: "isäni" (my father)</td>
                </tr>
                <tr>
                    <td>lapsi</td>
                    <td class="pronunciation">lap-si</td>
                    <td>child</td>
                    <td>Plural: "lapset" (children)</td>
                </tr>
                <tr>
                    <td>tytär</td>
                    <td class="pronunciation">tü-tär</td>
                    <td>daughter</td>
                    <td>Stem changes in different cases</td>
                </tr>
                <tr>
                    <td>poika</td>
                    <td class="pronunciation">poi-ka</td>
                    <td>son</td>
                    <td>Also means "boy" in general</td>
                </tr>
                <tr>
                    <td>veli</td>
                    <td class="pronunciation">ve-li</td>
                    <td>brother</td>
                    <td>Plural: "veljet" (brothers)</td>
                </tr>
                <tr>
                    <td>sisko</td>
                    <td class="pronunciation">sis-ko</td>
                    <td>sister</td>
                    <td>Also "sisar" in more formal contexts</td>
                </tr>
                <tr>
                    <td>sisarukset</td>
                    <td class="pronunciation">si-sa-ruk-set</td>
                    <td>siblings</td>
                    <td>Plural form only</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Minulla on kaksi veljeä ja yksi sisko.</p>
                <p><strong>English:</strong> I have two brothers and one sister.</p>
                <p><strong>Finnish:</strong> Äitini on opettaja.</p>
                <p><strong>English:</strong> My mother is a teacher.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Extended Family</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Notes</th>
                </tr>
                <tr>
                    <td>isovanhemmat</td>
                    <td class="pronunciation">i-so-van-hem-mat</td>
                    <td>grandparents</td>
                    <td>Literally "big parents"</td>
                </tr>
                <tr>
                    <td>isoäiti / mummo / mummi</td>
                    <td class="pronunciation">i-so-äi-ti / mum-mo / mum-mi</td>
                    <td>grandmother</td>
                    <td>"Mummo/mummi" are more informal</td>
                </tr>
                <tr>
                    <td>isoisä / ukki / vaari</td>
                    <td class="pronunciation">i-so-i-sä / uk-ki / vaa-ri</td>
                    <td>grandfather</td>
                    <td>"Ukki/vaari" are more informal</td>
                </tr>
                <tr>
                    <td>lapsenlapsi</td>
                    <td class="pronunciation">lap-sen-lap-si</td>
                    <td>grandchild</td>
                    <td>Literally "child's child"</td>
                </tr>
                <tr>
                    <td>tyttärentytär</td>
                    <td class="pronunciation">tüt-tä-ren-tü-tär</td>
                    <td>granddaughter (daughter's daughter)</td>
                    <td>Specific term for daughter's daughter</td>
                </tr>
                <tr>
                    <td>pojanpoika</td>
                    <td class="pronunciation">po-jan-poi-ka</td>
                    <td>grandson (son's son)</td>
                    <td>Specific term for son's son</td>
                </tr>
                <tr>
                    <td>setä</td>
                    <td class="pronunciation">se-tä</td>
                    <td>uncle (father's brother)</td>
                    <td>Specifically father's brother</td>
                </tr>
                <tr>
                    <td>eno</td>
                    <td class="pronunciation">e-no</td>
                    <td>uncle (mother's brother)</td>
                    <td>Specifically mother's brother</td>
                </tr>
                <tr>
                    <td>täti</td>
                    <td class="pronunciation">tä-ti</td>
                    <td>aunt</td>
                    <td>Can be either mother's or father's sister</td>
                </tr>
                <tr>
                    <td>serkku</td>
                    <td class="pronunciation">serk-ku</td>
                    <td>cousin</td>
                    <td>Gender-neutral term for cousin</td>
                </tr>
                <tr>
                    <td>veljenpoika</td>
                    <td class="pronunciation">vel-jen-poi-ka</td>
                    <td>nephew (brother's son)</td>
                    <td>Literally "brother's son"</td>
                </tr>
                <tr>
                    <td>siskonpoika</td>
                    <td class="pronunciation">sis-kon-poi-ka</td>
                    <td>nephew (sister's son)</td>
                    <td>Literally "sister's son"</td>
                </tr>
                <tr>
                    <td>veljentytär</td>
                    <td class="pronunciation">vel-jen-tü-tär</td>
                    <td>niece (brother's daughter)</td>
                    <td>Literally "brother's daughter"</td>
                </tr>
                <tr>
                    <td>siskontytär</td>
                    <td class="pronunciation">sis-kon-tü-tär</td>
                    <td>niece (sister's daughter)</td>
                    <td>Literally "sister's daughter"</td>
                </tr>
            </table>
            
            <div class="note-box">
                <p><strong>Note:</strong> Finnish distinguishes between maternal and paternal relatives in some cases. For example, "eno" is specifically a mother's brother, while "setä" is a father's brother.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>In-Laws and Other Relations</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                    <th>Notes</th>
                </tr>
                <tr>
                    <td>puoliso</td>
                    <td class="pronunciation">puo-li-so</td>
                    <td>spouse</td>
                    <td>Gender-neutral term for husband/wife</td>
                </tr>
                <tr>
                    <td>aviomies</td>
                    <td class="pronunciation">a-vi-o-mies</td>
                    <td>husband</td>
                    <td>Literally "marriage man"</td>
                </tr>
                <tr>
                    <td>vaimo</td>
                    <td class="pronunciation">vai-mo</td>
                    <td>wife</td>
                    <td>From old Finnish</td>
                </tr>
                <tr>
                    <td>aviopari</td>
                    <td class="pronunciation">a-vi-o-pa-ri</td>
                    <td>married couple</td>
                    <td>Literally "marriage pair"</td>
                </tr>
                <tr>
                    <td>appi</td>
                    <td class="pronunciation">ap-pi</td>
                    <td>father-in-law</td>
                    <td>Spouse's father</td>
                </tr>
                <tr>
                    <td>anoppi</td>
                    <td class="pronunciation">a-nop-pi</td>
                    <td>mother-in-law</td>
                    <td>Spouse's mother</td>
                </tr>
                <tr>
                    <td>vävy</td>
                    <td class="pronunciation">vä-vü</td>
                    <td>son-in-law</td>
                    <td>Daughter's husband</td>
                </tr>
                <tr>
                    <td>miniä</td>
                    <td class="pronunciation">mi-ni-ä</td>
                    <td>daughter-in-law</td>
                    <td>Son's wife</td>
                </tr>
                <tr>
                    <td>lanko</td>
                    <td class="pronunciation">lan-ko</td>
                    <td>brother-in-law</td>
                    <td>Spouse's brother or sister's husband</td>
                </tr>
                <tr>
                    <td>käly</td>
                    <td class="pronunciation">kä-lü</td>
                    <td>sister-in-law</td>
                    <td>Spouse's sister or brother's wife</td>
                </tr>
            </table>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Tapaan anoppini ja appeni ensi viikonloppuna.</p>
                <p><strong>English:</strong> I'm meeting my mother-in-law and father-in-law next weekend.</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Modern Family Terms</h3>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Finnish</th>
                    <th>Pronunciation</th>
                    <th>English</th>
                </tr>
                <tr>
                    <td>uusperhe</td>
                    <td class="pronunciation">uus-per-he</td>
                    <td>blended family</td>
                </tr>
                <tr>
                    <td>isäpuoli</td>
                    <td class="pronunciation">i-sä-puo-li</td>
                    <td>stepfather</td>
                </tr>
                <tr>
                    <td>äitipuoli</td>
                    <td class="pronunciation">äi-ti-puo-li</td>
                    <td>stepmother</td>
                </tr>
                <tr>
                    <td>lapsipuoli</td>
                    <td class="pronunciation">lap-si-puo-li</td>
                    <td>stepchild</td>
                </tr>
                <tr>
                    <td>ottovanhemmat</td>
                    <td class="pronunciation">ot-to-van-hem-mat</td>
                    <td>adoptive parents</td>
                </tr>
                <tr>
                    <td>ottolapsi</td>
                    <td class="pronunciation">ot-to-lap-si</td>
                    <td>adopted child</td>
                </tr>
                <tr>
                    <td>kummi</td>
                    <td class="pronunciation">kum-mi</td>
                    <td>godparent</td>
                </tr>
                <tr>
                    <td>kummilapsi</td>
                    <td class="pronunciation">kum-mi-lap-si</td>
                    <td>godchild</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Using Family Terms in Sentences</h3>
            
            <p>In Finnish, possessive suffixes are often used with family terms. Here are some examples:</p>
            
            <div class="example-box">
                <p><strong>Finnish:</strong> Äitini on kotoisin Turusta. (My mother is from Turku.)</p>
                <p><strong>Finnish:</strong> Isäsi soitti eilen. (Your father called yesterday.)</p>
                <p><strong>Finnish:</strong> Hänen siskonsa asuu Helsingissä. (His/her sister lives in Helsinki.)</p>
                <p><strong>Finnish:</strong> Meidän perheessämme on viisi henkilöä. (There are five people in our family.)</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> When using possessive suffixes with family terms, the word stem might change. For example: "veli" (brother) → "veljeni" (my brother).</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















