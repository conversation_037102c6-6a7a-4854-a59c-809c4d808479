# Opiskelen Suomea

Finnish Language Learning for Professional Environments

## Features

- Browse 80 chapters of Finnish language content organized by 5 professional categories
- View detailed vocabulary, grammar points, and dialogues for each chapter
- Interactive interface with category tabs and chapter cards
- Responsive design for desktop and mobile devices
- Real content loaded from markdown files

## Categories

1. **Daily Life** (Chapters 1-30): Common everyday situations and conversations
2. **Web Development IT** (Chapters 31-50): Technical vocabulary for IT professionals
3. **Cleaner** (Chapters 51-60): Vocabulary for cleaning professionals
4. **Kitchen Assistant** (Chapters 61-70): Terms for working in food service
5. **Warehouse** (Chapters 71-80): Language for logistics and warehouse operations

## How to Run the Website with Real Content

To display the actual content from the markdown files, you need to run a simple web server. Follow these steps:

### Install Node.js

1. Download and install Node.js from [nodejs.org](https://nodejs.org/)
2. Check the installation by opening Command Prompt (or PowerShell) and typing:
   ```
   node --version
   npm --version
   ```

### Install Dependencies

1. Open Command Prompt (or PowerShell)
2. Navigate to the project directory:
   ```
   cd "e:/Finland Tu/Opiskelen_Suomea"
   ```
3. Install the dependencies:
   ```
   npm install
   ```

### Run the Web Server

1. In the same Command Prompt (or PowerShell) window, run one of these commands:
   ```
   npm start
   ```
   or directly:
   ```
   node server.js
   ```
2. You will see the message:
   ```
   Server running at http://localhost:3000
   Access the website at http://localhost:3000/index.html
   ```
3. Open your web browser and go to: http://localhost:3000/index.html

## Project Structure

- `index.html`: Main web page for browsing categories and chapters
- `chapter.html`: Page for displaying individual chapter content
- `video.html`: Page for video content
- `styles.css`: CSS file for the interface
- JavaScript files:
  - `script.js`: Main JavaScript for user interactions on index and video pages
  - `highlight.js`: Handles text highlighting functionality
  - `chapter-new.js`: Loads and displays chapter content
  - `chapter-navigation.js`: Handles chapter navigation and table of contents
  - `speech-new.js`: Provides text-to-speech functionality for conversations
  - `server.js`: Express.js web server to serve content and API endpoints
- `chapters/`: Directory containing markdown files for each chapter
  - `Daily_Life/`: Chapters 1-30
  - `Web_Development_IT/`: Chapters 31-50
  - `Cleaner/`: Chapters 51-60
  - `Kitchen_Assistant/`: Chapters 61-70
  - `Warehouse/`: Chapters 71-80

## Technical Details

- Built with HTML, CSS, and JavaScript
- Uses Express.js for the backend server
- Converts markdown to HTML for displaying chapter content
- Responsive design for all device sizes

## License

MIT
AIzaSyBvYJhD4x5LGYopqgbK75HRV_mVKofLmlA

AIzaSyDKi3FrLNB2-FmXxGOU2momFdtDjiJsCKM

AIzaSyBH4TfcIfig2Ub1_RiYaunoZi7u0e_pjOw

AIzaSyCZQkAx-HKqL4jTeeJNXR8Bm8DVuTmEgnU

AIzaSyADrQbyDK4cyaWr1Zb08Jq7k6ouSHYIoYo

AIzaSyCTS5rhAER5crnL0vzqHgIBRmlT8cFIYK0

