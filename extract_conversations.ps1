# Script to extract conversations from chapter files and create summary files
# This script extracts the conversation text from all chapter files in each category folder
# and creates a single text file per category with all conversations combined.

# Define the category folders
$categoryFolders = @(
    "Daily_Life",
    "Web_Development_IT",
    "Cleaner",
    "Kitchen_Assistant",
    "Warehouse"
)

# Function to extract conversation text from a file
function Extract-ConversationText {
    param (
        [string]$filePath
    )

    $content = Get-Content -Path $filePath -Raw

    # Check if the file contains a conversation section
    if ($content -match "<div class=`"conversation`">") {
        $conversationMatch = [regex]::Match($content, "<div class=`"conversation`">(.*?)</div>", [System.Text.RegularExpressions.RegexOptions]::Singleline)

        if ($conversationMatch.Success) {
            $conversationHtml = $conversationMatch.Groups[1].Value

            # Get the chapter title
            if ($content -match "^# (Chapter \d+: .+)") {
                $chapterTitle = $matches[1]
            } else {
                $chapterTitle = [System.IO.Path]::GetFileNameWithoutExtension($filePath)
            }

            # Extract speaker and dialogue from each paragraph
            $paragraphs = [regex]::Matches($conversationHtml, "<p><strong>(.*?)</strong>: (.*?)(?:<br>.*?)?</p>", [System.Text.RegularExpressions.RegexOptions]::Singleline)

            $cleanConversation = "# $chapterTitle`n`n"

            foreach ($paragraph in $paragraphs) {
                $speaker = $paragraph.Groups[1].Value
                $dialogue = $paragraph.Groups[2].Value

                # Remove any HTML tags from the dialogue
                $dialogue = $dialogue -replace "<[^>]+>", ""

                # Add to clean conversation
                $cleanConversation += "${speaker}: $dialogue`n`n"
            }

            return $cleanConversation
        }
    }

    return $null
}

# Process each category folder
foreach ($folder in $categoryFolders) {
    $folderPath = Join-Path -Path "chapters" -ChildPath $folder

    # Check if the folder exists
    if (Test-Path -Path $folderPath) {
        Write-Host "Processing $folder folder..."

        # Get all chapter files in the folder
        $chapterFiles = Get-ChildItem -Path $folderPath -Filter "chapter*.md" | Sort-Object Name

        $allConversations = "# $folder Conversations`n`n"
        $conversationCount = 0

        # Process each chapter file
        foreach ($file in $chapterFiles) {
            Write-Host "  Processing $($file.Name)..."

            $conversationText = Extract-ConversationText -filePath $file.FullName

            if ($conversationText) {
                $allConversations += "$conversationText`n---`n`n"
                $conversationCount++
            }
        }

        # Save the combined conversations to a file if any were found
        if ($conversationCount -gt 0) {
            $outputFileName = "$($folder)_conversations.txt"
            $outputFilePath = $outputFileName

            # Use .NET directly to ensure proper UTF-8 encoding with BOM
            [System.IO.File]::WriteAllText($outputFilePath, $allConversations, [System.Text.Encoding]::UTF8)

            Write-Host "Created $outputFilePath with $conversationCount conversations."
        } else {
            Write-Host "No conversations found in $folder folder."
        }
    } else {
        Write-Host "Folder $folderPath does not exist. Skipping."
    }
}

Write-Host "Conversation extraction complete."
