# Chapter 45: <PERSON><PERSON><PERSON><PERSON>ky ja optimointi / Performance & Optimization

## Objectives / Tavoitteet
- Learn vocabulary related to web performance and optimization in Finnish
- Understand how to discuss performance bottlenecks and improvements
- Be able to explain optimization techniques for websites and applications
- Master basic conversations about creating fast and efficient web experiences

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Su<PERSON>tuskyky - Performance
2. Optimointi - Optimization
3. Latausaika - Loading time
4. <PERSON><PERSON><PERSON><PERSON><PERSON> - Bottleneck
5. <PERSON><PERSON><PERSON>uisti - Cache
6. Pak<PERSON>us - Compression
7. Minifiointi - Minification
8. Laiska lataus - Lazy loading
9. Renderöinti - Rendering
10. Kuormitus - Load
11. Skaalautuvuus - Scalability
12. Vasteaika - Response time
13. Resurssit - Resources
14. Mittaus - Measurement
15. K<PERSON>yttäjäkokemus - User experience

## Grammar Points / Kielioppi
1. **Technical Verbs for Performance Operations**:
   - Action verbs for optimization activities
   - Example: Optimoin verkkosivun latausaikaa. (I optimize the website's loading time.)

2. **Conditional Forms for Performance Scenarios**:
   - Expressing optimization conditions
   - Example: <PERSON><PERSON>, sivu<PERSON> latautuisi nopeammin. (If the images would be compressed, the site would load faster.)

3. **Inessive Case (-ssa/-ssä) for Performance Contexts**:
   - In optimization environments
   - Example: Suorituskyvyn optimoinnissa on monta vaihetta. (There are many stages in performance optimization.)

4. **Elative Case (-sta/-stä) for Performance Sources**:
   - From performance sources
   - Example: Hitaus johtuu raskaista kuvista. (The slowness results from heavy images.)

5. **Translative Case (-ksi) for Performance Transformations**:
   - Converting to optimized states
   - Example: Muunnan JavaScript-tiedostot minifioiduiksi. (I convert the JavaScript files into minified ones.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Web performance workshop / Verkkosuorituskyvyn työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa suorituskyvyn ja optimoinnin työpajaan! Tänään opimme, miten voimme parantaa verkkosivujen ja -sovellusten nopeutta ja tehokkuutta.<br>
<em>(ter-ve-tu-lo-a suo-ri-tus-ky-vyn ja op-ti-moin-nin työ-pa-jaan! tä-nään o-pim-me, mi-ten voim-me pa-ran-taa verk-ko-si-vu-jen ja -so-vel-lus-ten no-pe-ut-ta ja te-hok-kuut-ta.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Verkkosivuni latautuu melko hitaasti, joten haluaisin oppia, miten voin parantaa sen suorituskykyä.<br>
<em>(kii-tos! verk-ko-si-vu-ni la-tau-tuu mel-ko hi-taas-ti, jo-ten ha-lu-ai-sin op-pi-a, mi-ten voin pa-ran-taa sen suo-ri-tus-ky-ky-ä.)</em></p>

<p><strong>Ohjaaja</strong>: Hyvä! Aloitetaan ymmärtämällä, miksi suorituskyky on tärkeää ja mitkä ovat yleisimmät pullonkaulat.<br>
<em>(hy-vä! a-loi-te-taan ym-mär-tä-mäl-lä, mik-si suo-ri-tus-ky-ky on tär-ke-ää ja mit-kä o-vat y-lei-sim-mät pul-lon-kau-lat.)</em></p>

<p><strong>Osallistuja</strong>: Miksi suorituskyky on niin tärkeää verkkosivuille?<br>
<em>(mik-si suo-ri-tus-ky-ky on niin tär-ke-ää verk-ko-si-vuil-le?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin mitata verkkosivuni suorituskykyä?<br>
<em>(mi-ten voin mi-ta-ta verk-ko-si-vu-ni suo-ri-tus-ky-ky-ä?)</em></p>

<p><strong>Osallistuja</strong>: Mitkä ovat yleisimmät suorituskykyongelmat verkkosivuilla?<br>
<em>(mit-kä o-vat y-lei-sim-mät suo-ri-tus-ky-ky-on-gel-mat verk-ko-si-vuil-la?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin optimoida kuvat verkkosivullani?<br>
<em>(mi-ten voin op-ti-moi-da ku-vat verk-ko-si-vul-la-ni?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin optimoida JavaScript- ja CSS-tiedostoja?<br>
<em>(mi-ten voin op-ti-moi-da ja-va-script- ja CSS-tie-dos-to-ja?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin hyödyntää välimuistia suorituskyvyn parantamiseksi?<br>
<em>(mi-ten voin hyö-dyn-tää vä-li-muis-ti-a suo-ri-tus-ky-vyn pa-ran-ta-mi-sek-si?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin optimoida palvelinpuolen suorituskykyä?<br>
<em>(mi-ten voin op-ti-moi-da pal-ve-lin-puo-len suo-ri-tus-ky-ky-ä?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla optimoida verkkosivuani näiden ohjeiden avulla?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la op-ti-moi-da verk-ko-si-vu-a-ni näi-den oh-jei-den a-vul-la?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita mittaamalla nykyinen suorituskyky Lighthousella tai PageSpeed Insightsilla. Tämä antaa sinulle lähtökohdan ja auttaa tunnistamaan suurimmat pullonkaulat. Keskity ensin helppoihin voittoihin, kuten kuvien optimointiin ja minifiointiin. Sitten voit siirtyä monimutkaisempiin optimointeihin. Muista testata muutokset eri laitteilla ja yhteyksillä. Suorituskyvyn optimointi on jatkuva prosessi, ei kertaluontoinen tehtävä.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta mit-taa-mal-la ny-kyi-nen suo-ri-tus-ky-ky light-hou-sel-la tai pa-ge-speed in-sights-il-la. tä-mä an-taa si-nul-le läh-tö-koh-dan ja aut-taa tun-nis-ta-maan suu-rim-mat pul-lon-kau-lat. kes-ki-ty en-sin help-poi-hin voit-toi-hin, ku-ten ku-vi-en op-ti-moin-tiin ja mi-ni-fi-oin-tiin. sit-ten voit siir-ty-ä mo-ni-mut-kai-sem-piin op-ti-moin-tei-hin. muis-ta tes-ta-ta muu-tok-set e-ri lait-teil-la ja yh-te-yk-sil-lä. suo-ri-tus-ky-vyn op-ti-moin-ti on jat-ku-va pro-ses-si, ei ker-ta-luon-toi-nen teh-tä-vä.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin parantaa verkkosivuni suorituskykyä.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin pa-ran-taa verk-ko-si-vu-ni suo-ri-tus-ky-ky-ä.)</em></p>
</div>

### Cultural Notes:
- Finland has excellent internet infrastructure, but Finnish developers still value optimization for global audiences and mobile users
- Finnish design philosophy often emphasizes clean, minimal interfaces which naturally tend to load faster
- Finnish tech companies typically value user experience, including performance, as a key quality metric
- Finland's tech education increasingly emphasizes performance optimization as part of sustainable development
- Finnish developers often need to consider optimization for international users who may have varying connection speeds


