# Input and output file paths
$inputFile  = "Finnish_CORE2000.txt"
$outputFile = "Finnish_2000_formatted.txt"

# Read all lines
$lines = Get-Content -Path $inputFile -Encoding UTF8

# Prepare output array
$formattedLines = @()
$formattedLines += "No`tFinnish`tFinnish_Sentence`tEnglish`tEnglish_Sentence"

# Loop in steps of two (Finnish/English)
for ($i = 0; $i -lt $lines.Count; $i += 2) {
    if ($i + 1 -ge $lines.Count) { break }

    $finnishLine = $lines[$i].Trim()
    $englishLine = $lines[$i + 1].Trim()

    if ([string]::IsNullOrWhiteSpace($finnishLine) -or [string]::IsNullOrWhiteSpace($englishLine)) {
        continue
    }

    # Extract leading number
    if ($finnishLine -match '^(\d+)') {
        $number = $matches[1]
        $rest   = $finnishLine.Substring($number.Length).Trim()

        # (Case-sensitive) split at the first space before ANY uppercase letter
          if ($rest -cmatch '^(.+?)\s+(?=\p{Lu})(.+)$') {
            $finnishWord     = $matches[1]
            $finnishSentence = $matches[2]
        }
        # 2) Fallback: split at first punctuation (comma, period, colon, semicolon)
        elseif ($rest -match '^(.+?)[,.:;]\s*(.+)$') {
            $finnishWord     = $matches[1]
            $finnishSentence = $matches[2]
        }
        # 3) Final fallback: first token + rest
        elseif ($rest -match '^([^\s]+)\s+(.+)$') {
            $finnishWord     = $matches[1]
            $finnishSentence = $matches[2]
        }
        else {
            $finnishWord     = $rest
            $finnishSentence = ""
        }

        # English: split on first space
        if ($englishLine -cmatch '^(.+?)\s+(?=\p{Lu})(.+)$') {
            $englishWord     = $matches[1]
            $englishSentence = $matches[2]
        }
        else {
            $englishWord     = $englishLine
            $englishSentence = ""
        }

        # Capitalize English word (handles hyphens)
        if ($englishWord.Length -gt 0) {
            if ($englishWord -match '-') {
                $parts = $englishWord -split '-'
                for ($h = 0; $h -lt $parts.Length; $h++) {
                    $parts[$h] = $parts[$h].Substring(0,1).ToUpper() +
                                 $parts[$h].Substring(1).ToLower()
                }
                $englishWord = $parts -join '-'
            }
            else {
                $englishWord = $englishWord.Substring(0,1).ToUpper() +
                               $englishWord.Substring(1).ToLower()
            }
        }

        # Append formatted line
        $formattedLines += "$number`t$finnishWord`t$finnishSentence`t$englishWord`t$englishSentence"
    }
}

# Write out
$formattedLines | Out-File -FilePath $outputFile -Encoding UTF8
Write-Host "Formatted $($formattedLines.Count - 1) entries and saved to $outputFile"
