<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Most Common Finnish Words - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        /* Column width styles */
        .vocabulary-table th:nth-child(1), 
        .vocabulary-table td:nth-child(1) {
            width: 1%;
            white-space: nowrap;
        }
        
        .vocabulary-table th:nth-child(2), 
        .vocabulary-table td:nth-child(2) {
            width: 15%;
            white-space: nowrap;
        }
        
        .vocabulary-table th:nth-child(3), 
        .vocabulary-table td:nth-child(3) {
            width: auto;
        }
        
        .vocabulary-table th:nth-child(4), 
        .vocabulary-table td:nth-child(4) {
            width: 10%;
            white-space: nowrap;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .word-category {
            font-weight: 500;
            color: #0066cc;
        }
        
        .search-container {
            margin: 20px 0;
        }
        
        .search-input {
            padding: 10px;
            width: 100%;
            max-width: 500px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        
        .filter-container {
            margin: 15px 0;
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .filter-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            border-radius: 20px;
            padding: 5px 15px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .filter-button:hover, .filter-button.active {
            background-color: #0066cc;
            color: white;
            border-color: #0066cc;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        
        .pagination-button {
            background-color: #f5f5f5;
            border: 1px solid #ddd;
            padding: 8px 15px;
            margin: 0 5px;
            cursor: pointer;
            border-radius: 4px;
            transition: all 0.3s ease;
        }
        
        .pagination-button:hover, .pagination-button.active {
            background-color: #0066cc;
            color: white;
            border-color: #0066cc;
        }
        
        /* Loading indicator */
        .loading-indicator {
            text-align: center;
            padding: 20px;
            font-size: 18px;
            color: #0066cc;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .filter-button {
            background-color: #333;
            border-color: #444;
        }
        
        [data-theme="dark"] .filter-button:hover, [data-theme="dark"] .filter-button.active {
            background-color: #0066cc;
            border-color: #0066cc;
        }
        
        [data-theme="dark"] .pagination-button {
            background-color: #333;
            border-color: #444;
        }
        
        [data-theme="dark"] .pagination-button:hover, [data-theme="dark"] .pagination-button.active {
            background-color: #0066cc;
            border-color: #0066cc;
        }
        
        [data-theme="dark"] .search-input {
            background-color: #333;
            border-color: #444;
            color: #fff;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="common-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../video.html">Videos</a></li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="common-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="common-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Most Common Finnish Words</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Most Common Finnish Words</h2>
            <p>This page presents the 2000 most frequently used Finnish words, organized by category and frequency. Learning these common words will give you a strong foundation for understanding and communicating in Finnish. The words are based on frequency analyses of written and spoken Finnish.</p>
            
            <div class="note-box">
                <p><strong>Note:</strong> Finnish is a highly inflected language, so many words appear in different forms. The words listed here are in their basic forms (nominative singular for nouns, first infinitive for verbs). In actual usage, you'll encounter many variations of these words.</p>
            </div>
            
            <div class="search-container">
                <input type="text" id="word-search" class="search-input" placeholder="Search for a word...">
                <div class="filter-container">
                    <button class="filter-button active" data-filter="all">All</button>
                    <button class="filter-button" data-filter="pronouns">Pronouns</button>
                    <button class="filter-button" data-filter="verbs">Verbs</button>
                    <button class="filter-button" data-filter="nouns">Nouns</button>
                    <button class="filter-button" data-filter="adjectives">Adjectives</button>
                    <button class="filter-button" data-filter="adverbs">Adverbs</button>
                    <button class="filter-button" data-filter="conjunctions">Conjunctions</button>
                    <button class="filter-button" data-filter="prepositions">Prepositions</button>
                    <button class="filter-button" data-filter="interjection">Interjections</button>
                    <button class="filter-button" data-filter="phrase">Phrases</button>
                </div>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Top 2000 Most Common Finnish Words</h3>
            
            <div class="pagination">
                <button class="pagination-button active" data-page="1">1-100</button>
                <button class="pagination-button" data-page="2">101-200</button>
                <button class="pagination-button" data-page="3">201-300</button>
                <button class="pagination-button" data-page="4">301-400</button>
                <button class="pagination-button" data-page="5">401-500</button>
                <button class="pagination-button" data-page="6">501-600</button>
                <button class="pagination-button" data-page="7">601-700</button>
                <button class="pagination-button" data-page="8">701-800</button>
                <button class="pagination-button" data-page="9">801-900</button>
                <button class="pagination-button" data-page="10">901-1000</button>
                <button class="pagination-button" data-page="11">1001-1100</button>
                <button class="pagination-button" data-page="12">1101-1200</button>
                <button class="pagination-button" data-page="13">1201-1300</button>
                <button class="pagination-button" data-page="14">1301-1400</button>
                <button class="pagination-button" data-page="15">1401-1500</button>
                <button class="pagination-button" data-page="16">1501-1600</button>
                <button class="pagination-button" data-page="17">1601-1700</button>
                <button class="pagination-button" data-page="18">1701-1800</button>
                <button class="pagination-button" data-page="19">1801-1900</button>
                <button class="pagination-button" data-page="20">1901-2000</button>
            </div>
            
            <div id="loading-indicator" class="loading-indicator">
                <i class="fas fa-spinner fa-spin"></i> Loading vocabulary data...
            </div>
            
            <table class="vocabulary-table" id="words-table">
                <thead>
                    <tr>
                        <th>Rank</th>
                        <th>Word</th>
                        <th>Sentence</th>
                        <th>Category</th>
                    </tr>
                </thead>
                <tbody id="vocabulary-body">
                    <!-- Table content will be dynamically generated -->
                </tbody>
            </table>
        </section>
    </div>

    <script>
        // Global variables
        let vocabularyData = [];
        let currentPage = 1;
        let currentFilter = 'all';
        let searchTerm = '';
        
        // Function to fetch vocabulary data
        async function fetchVocabularyData() {
            try {
                const response = await fetch('../../../finnish_vocabulary.json');
                if (!response.ok) {
                    throw new Error('Failed to fetch vocabulary data');
                }
                vocabularyData = await response.json();
                document.getElementById('loading-indicator').style.display = 'none';
                renderVocabularyTable();
            } catch (error) {
                console.error('Error fetching vocabulary data:', error);
                document.getElementById('loading-indicator').textContent = 'Error loading vocabulary data. Please try again later.';
            }
        }
        
        // Function to render the vocabulary table
        function renderVocabularyTable() {
            const tableBody = document.getElementById('vocabulary-body');
            tableBody.innerHTML = '';
            
            // Calculate start and end indices for current page
            const startIndex = (currentPage - 1) * 100;
            const endIndex = startIndex + 100;
            
            // Filter data based on current filter and search term
            const filteredData = vocabularyData.filter(item => {
                const matchesFilter = currentFilter === 'all' || 
                                     item.category.toLowerCase() === currentFilter.toLowerCase();
                const matchesSearch = searchTerm === '' || 
                                     item.finnish.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                     item.english.toLowerCase().includes(searchTerm.toLowerCase());
                return matchesFilter && matchesSearch;
            });
            
            // Get data for current page
            const pageData = filteredData.filter(item => 
                item.rank > startIndex && item.rank <= endIndex
            );
            
            // Create table rows
            pageData.forEach(item => {
                const row = document.createElement('tr');
                
                const rankCell = document.createElement('td');
                rankCell.textContent = item.rank;
                
                const wordCell = document.createElement('td');
                wordCell.innerHTML = `${item.finnish}<br>${item.english}`;
                
                const sentenceCell = document.createElement('td');
                sentenceCell.innerHTML = `${item.finnishSentence}<br>${item.englishSentence}`;
                
                const categoryCell = document.createElement('td');
                categoryCell.innerHTML = `<span class="word-category">${item.category}</span>`;
                
                row.appendChild(rankCell);
                row.appendChild(wordCell);
                row.appendChild(sentenceCell);
                row.appendChild(categoryCell);
                
                tableBody.appendChild(row);
            });
            
            // If no results found
            if (pageData.length === 0) {
                const row = document.createElement('tr');
                const cell = document.createElement('td');
                cell.colSpan = 4;
                cell.textContent = 'No matching words found.';
                cell.style.textAlign = 'center';
                cell.style.padding = '20px';
                row.appendChild(cell);
                tableBody.appendChild(row);
            }
        }
        
        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            // Fetch vocabulary data
            fetchVocabularyData();
            
            // Dark mode toggle
            const darkToggle = document.getElementById('common-toggle-dark');
            const darkToggleMobile = document.getElementById('common-toggle-dark-mobile');
            const body = document.body;
            
            // Check for saved theme preference or use default
            const currentTheme = localStorage.getItem('theme') || 'light';
            if (currentTheme === 'dark') {
                body.setAttribute('data-theme', 'dark');
                darkToggle.innerHTML = '<i class="fas fa-sun"></i>';
                darkToggleMobile.innerHTML = '<i class="fas fa-sun"></i>';
            }
            
            // Function to toggle theme
            function toggleTheme() {
                if (body.getAttribute('data-theme') === 'dark') {
                    body.removeAttribute('data-theme');
                    localStorage.setItem('theme', 'light');
                    darkToggle.innerHTML = '<i class="fas fa-moon"></i>';
                    darkToggleMobile.innerHTML = '<i class="fas fa-moon"></i>';
                } else {
                    body.setAttribute('data-theme', 'dark');
                    localStorage.setItem('theme', 'dark');
                    darkToggle.innerHTML = '<i class="fas fa-sun"></i>';
                    darkToggleMobile.innerHTML = '<i class="fas fa-sun"></i>';
                }
            }
            
            // Event listeners for theme toggle
            darkToggle.addEventListener('click', toggleTheme);
            darkToggleMobile.addEventListener('click', toggleTheme);
            
            // Mobile menu toggle
            const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
            const navLinks = document.getElementById('nav-links');
            
            mobileMenuToggle.addEventListener('click', function() {
                navLinks.classList.toggle('active');
            });
            
            // Text highlighting toggle
            const highlightToggle = document.getElementById('common-toggle-highlight');
            
            highlightToggle.addEventListener('click', function() {
                body.classList.toggle('highlight-mode');
                highlightToggle.classList.toggle('active');
            });
            
            // Search functionality
            const searchInput = document.getElementById('word-search');
            
            searchInput.addEventListener('input', function() {
                searchTerm = this.value.toLowerCase();
                renderVocabularyTable();
            });
            
            // Category filtering
            const filterButtons = document.querySelectorAll('.filter-button');
            
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    currentFilter = this.getAttribute('data-filter');
                    renderVocabularyTable();
                });
            });
            
            // Pagination
            const paginationButtons = document.querySelectorAll('.pagination-button');
            
            paginationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active class from all buttons
                    paginationButtons.forEach(btn => btn.classList.remove('active'));
                    
                    // Add active class to clicked button
                    this.classList.add('active');
                    
                    currentPage = parseInt(this.getAttribute('data-page'));
                    renderVocabularyTable();
                });
            });
        });
    </script>
</body>
</html>