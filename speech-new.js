// Enhanced speech synthesis functionality
(function () {
  // Check if browser supports speech synthesis
  if (!("speechSynthesis" in window)) {
    console.warn(
      "This browser does not support the Web Speech API. Text-to-speech functionality will not work."
    );
    return;
  }

  // Initialize flag for intentional speech cancellation
  window.intentionalSpeechCancellation = false;

  // Create speech synthesis objects
  const synth = window.speechSynthesis;
  let voices = [];

  // Function to load voices
  function loadVoices() {
    voices = synth.getVoices();
    console.log("Loaded " + voices.length + " voices");
  }

  // Load voices initially
  loadVoices();

  // Chrome loads voices asynchronously, so we need this event
  if (synth.onvoiceschanged !== undefined) {
    synth.onvoiceschanged = loadVoices;
  }

  // Add event listener to stop speech when page is unloaded
  window.addEventListener("beforeunload", function () {
    if (synth.speaking) {
      synth.cancel();
    }
  });

  // Add a global event listener for when speech is cancelled
  document.addEventListener("speechSynthesisCancelled", function () {
    // Reset all playing buttons
    document.querySelectorAll(".audio-play-button.playing").forEach((btn) => {
      btn.innerHTML = '<i class="fas fa-play"></i> Play';
      btn.classList.remove("playing");
    });
  });

  // Function to initialize audio buttons
  function initializeAudioButtons() {
    // Get all audio play buttons that don't already have event listeners
    const audioButtons = document.querySelectorAll(
      ".audio-play-button:not(.initialized)"
    );

    if (audioButtons.length === 0) {
      return;
    }

    // Add click event to each button
    audioButtons.forEach((button) => {
      // Reset the button content to ensure it's clean
      button.innerHTML = '<i class="fas fa-play"></i> Play';

      // Mark this button as initialized
      button.classList.add("initialized");

      button.addEventListener("click", function () {
        const conversationId = this.getAttribute("data-conversation-id");
        const conversationElement = document.getElementById(conversationId);

        if (!conversationElement) {
          console.error("Conversation element not found:", conversationId);
          return;
        }

        // If the button that was clicked is already playing, just stop and reset
        if (this.classList.contains("playing")) {
          // Set a flag to indicate this is an intentional cancellation
          window.intentionalSpeechCancellation = true;

          // Stop any speech that's playing
          synth.cancel();

          // Reset the flag after a short delay
          setTimeout(() => {
            window.intentionalSpeechCancellation = false;
          }, 100);

          // Reset this button
          this.innerHTML = '<i class="fas fa-play"></i> Play';
          this.classList.remove("playing");

          // Remove the speaking class from the h3-container
          if (conversationElement.classList.contains("h3-container")) {
            conversationElement.classList.remove("speaking");
          }

          // Dispatch custom event to notify that speech was cancelled
          document.dispatchEvent(new CustomEvent("speechSynthesisCancelled"));
          return;
        }

        // If there's already speech playing from another button, stop it
        if (synth.speaking) {
          // Set a flag to indicate this is an intentional cancellation
          window.intentionalSpeechCancellation = true;

          synth.cancel();

          // Reset the flag after a short delay
          setTimeout(() => {
            window.intentionalSpeechCancellation = false;
          }, 100);

          // Reset all other buttons
          document
            .querySelectorAll(".audio-play-button.playing")
            .forEach((btn) => {
              btn.innerHTML = '<i class="fas fa-play"></i> Play';
              btn.classList.remove("playing");

              // Find the parent h3-container and remove the speaking class
              const container = btn.closest(".h3-container");
              if (container) {
                container.classList.remove("speaking");
              }
            });

          // Dispatch custom event to notify that speech was cancelled
          document.dispatchEvent(new CustomEvent("speechSynthesisCancelled"));
        }

        // Extract text from the conversation div (excluding the button container)
        const speechParts = [];

        // Get all paragraph elements from the conversation
        let paragraphs = [];

        // Check if this is an h3-container
        if (conversationElement.classList.contains("h3-container")) {
          // For h3-container, we need to find the next sibling elements until the next h3-container
          let currentElement = conversationElement.nextElementSibling;

          while (
            currentElement &&
            !currentElement.classList.contains("h3-container")
          ) {
            // If it's a paragraph, add it to our list
            if (currentElement.tagName === "P") {
              paragraphs.push(currentElement);
            }
            currentElement = currentElement.nextElementSibling;
          }
        } else {
          // Regular conversation element
          paragraphs = Array.from(
            conversationElement.querySelectorAll("p")
          ).filter((node) => !node.querySelector(".audio-player-container"));
        }

        // Process each paragraph to extract speaker and text
        paragraphs.forEach((paragraph) => {
          // Find the speaker name (in <strong> tag)
          const speakerElement = paragraph.querySelector("strong");
          // Find the pronunciation guide (in <em> tag)
          const pronunciationElement = paragraph.querySelector("em");

          if (speakerElement) {
            const speaker = speakerElement.textContent.trim();

            // Get the text content without the pronunciation guide
            let text = paragraph.textContent;

            // Remove the speaker name
            text = text.replace(speakerElement.textContent, "").trim();

            // Remove the pronunciation guide if it exists
            if (pronunciationElement) {
              text = text.replace(pronunciationElement.textContent, "").trim();
            }

            // Remove any leading colon and trim
            text = text.replace(/^:\\s*/, "").trim();

            // Add to speech parts if there's actual text to speak
            if (text) {
              speechParts.push({
                speaker: speaker,
                text: text,
              });
            }
          }
        });

        // If no speech parts were found, try to use the h3 title as fallback
        if (speechParts.length === 0) {
          // Check if this is an h3-container
          if (conversationElement.classList.contains("h3-container")) {
            // Get the h3 element
            const h3Element = conversationElement.querySelector("h3");
            if (h3Element && h3Element.textContent) {
              // Add the h3 title as a speech part
              speechParts.push({
                text: h3Element.textContent,
                lang: "fi-FI",
              });
              console.log(
                "Using h3 title as fallback text:",
                h3Element.textContent
              );
            } else {
              console.warn(
                "No conversation text found and no h3 title:",
                conversationId
              );
              alert("No text found to read in this section.");
              return;
            }
          } else {
            console.warn("No conversation text found:", conversationId);
            alert("No text found to read in this conversation.");
            return;
          }
        }

        // Update button appearance to show it's playing
        this.innerHTML = '<i class="fas fa-stop"></i> Stop';
        this.classList.add("playing");

        // Add a subtle animation to the h3-container if this is an h3 title
        if (conversationElement.classList.contains("h3-container")) {
          conversationElement.classList.add("speaking");
        }

        // Function to speak the next part with improved error handling
        let currentPartIndex = 0;
        let isSpeaking = false;

        function speakNextPart() {
          // If button is no longer in playing state or speech was intentionally cancelled, stop
          if (
            !button.classList.contains("playing") ||
            window.intentionalSpeechCancellation
          ) {
            return;
          }

          // If we've spoken all parts, start over (auto-replay)
          if (currentPartIndex >= speechParts.length) {
            // Reset the index to start over
            currentPartIndex = 0;
            // Continue playing (don't reset button)
          }

          // If already speaking, wait and try again
          if (isSpeaking) {
            setTimeout(speakNextPart, 100);
            return;
          }

          try {
            // Get the current part to speak
            const part = speechParts[currentPartIndex];

            // Create a new utterance
            const utterance = new SpeechSynthesisUtterance(part.text);

            // Get the language from the button's data attribute
            const language = button.getAttribute("data-language") || "fi-FI";
            utterance.lang = language;

            // Find voices for the selected language
            const langVoices = voices.filter((voice) =>
              voice.lang.startsWith(utterance.lang.split("-")[0])
            );

            // Determine which speaker this is (for pitch adjustment)
            const speakerIndex = speechParts.findIndex(
              (p) => p.speaker === part.speaker
            );

            // Set voice if available
            if (langVoices.length > 0) {
              utterance.voice = langVoices[0];
            }

            // Set speech parameters
            utterance.rate = 0.9; // Slightly slower than normal

            // Alternate pitch based on speaker
            utterance.pitch = speakerIndex % 2 === 0 ? 1.2 : 0.8;

            // Set flag to indicate we're speaking
            isSpeaking = true;

            // When speech ends, move to next part
            utterance.onend = function () {
              isSpeaking = false;
              currentPartIndex++;
              setTimeout(speakNextPart, 100); // Small delay between parts
            };

            // Handle errors
            utterance.onerror = function (event) {
              console.error("Speech synthesis error:", event);
              isSpeaking = false;

              // Check if this is an intentional interruption (from clicking stop)
              if (
                event.error === "interrupted" ||
                window.intentionalSpeechCancellation
              ) {
                console.log("Speech was intentionally interrupted");
                // Don't show error message or continue to next part for intentional interruptions
                return;
              } else {
                // Show error message for actual errors, but only on first error
                if (currentPartIndex === 0) {
                  alert(
                    "There was an error with the text-to-speech. This might be due to browser limitations or missing voice support for the selected language."
                  );
                }

                // Try to continue with next part only for non-intentional errors
                currentPartIndex++;
                setTimeout(speakNextPart, 100);
              }
            };

            // Start speaking
            synth.speak(utterance);
          } catch (error) {
            console.error("Error in speech synthesis:", error);
            isSpeaking = false;

            // Only continue to next part if this wasn't an intentional cancellation
            if (!window.intentionalSpeechCancellation) {
              currentPartIndex++;
              setTimeout(speakNextPart, 100);
            }
          }
        }

        // Start speaking the first part
        speakNextPart();
      });
    });
  }

  // Initialize buttons when DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    // Initial initialization - run immediately and then again after a delay
    initializeAudioButtons();
    setTimeout(initializeAudioButtons, 500);

    // Set up a mutation observer to detect when new content is added
    const observer = new MutationObserver(function (mutations) {
      let shouldInitialize = false;

      mutations.forEach(function (mutation) {
        if (mutation.addedNodes.length) {
          shouldInitialize = true;
        }
      });

      if (shouldInitialize) {
        // Run immediately and then again after a delay
        initializeAudioButtons();
        setTimeout(initializeAudioButtons, 100);
      }
    });

    // Start observing the chapter content container
    const chapterContent = document.getElementById("chapter-content");
    if (chapterContent) {
      observer.observe(chapterContent, {
        childList: true,
        subtree: true,
      });
    }
  });

  // Also initialize buttons when window loads (for cases where content is already there)
  window.addEventListener("load", function () {
    setTimeout(initializeAudioButtons, 1000);
  });

  // Make the initialization function available globally
  window.initializeAudioButtons = initializeAudioButtons;
})();

