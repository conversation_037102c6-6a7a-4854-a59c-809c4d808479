﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Yes/No Questions in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Yes/No Questions</span>
        </div>
        
        <section class="grammar-section">
            <h2>Yes/No Questions in Finnish</h2>
            <p>Yes/no questions (closed questions) are questions that can be answered with "yes" or "no". In Finnish, these questions have specific structures and patterns. This page explains how to form and use yes/no questions in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMING YES/NO QUESTIONS WITH -KO/-KÖ</h3>
            
            <div class="grammar-content">
                <p>The most common way to form a yes/no question in Finnish is to add the question particle -ko/-kö to the verb and move it to the beginning of the sentence:</p>
                
                <div class="grammar-example">
                    <p>Statement: <span class="finnish">Sinä puhut suomea.</span> <span class="english">You speak Finnish.</span></p>
                    <p>Question: <span class="finnish">Puhutko sinä suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Statement: <span class="finnish">Hän on kotona.</span> <span class="english">He/she is at home.</span></p>
                    <p>Question: <span class="finnish">Onko hän kotona?</span> <span class="english">Is he/she at home?</span></p>
                </div>
                
                <p>The choice between -ko and -kö follows vowel harmony:</p>
                <ul>
                    <li>Use -kö with words containing front vowels (ä, ö, y)</li>
                    <li>Use -ko with words containing back vowels (a, o, u) or neutral vowels (e, i)</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>STEPS TO FORM A YES/NO QUESTION</h3>
            
            <div class="grammar-content">
                <ol>
                    <li>Take the verb from the statement</li>
                    <li>Add the question particle -ko/-kö to it</li>
                    <li>Move this verb with the question particle to the beginning of the sentence</li>
                    <li>Keep the rest of the sentence in the same order</li>
                    <li>Add a question mark at the end</li>
                </ol>
                
                <div class="grammar-example">
                    <p>Statement: <span class="finnish">Sinä tulet huomenna.</span> <span class="english">You are coming tomorrow.</span></p>
                    <p>Question: <span class="finnish">Tuletko sinä huomenna?</span> <span class="english">Are you coming tomorrow?</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Statement: <span class="finnish">Te menette elokuviin.</span> <span class="english">You (plural) are going to the movies.</span></p>
                    <p>Question: <span class="finnish">Menettekö te elokuviin?</span> <span class="english">Are you (plural) going to the movies?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>YES/NO QUESTIONS WITH DIFFERENT VERB FORMS</h3>
            
            <div class="grammar-content">
                <h4>1. Present tense</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Puhutko suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                    <p><span class="finnish">Asuvatko he Helsingissä?</span> <span class="english">Do they live in Helsinki?</span></p>
                </div>
                
                <h4>2. Past tense</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Puhuitko eilen suomea?</span> <span class="english">Did you speak Finnish yesterday?</span></p>
                    <p><span class="finnish">Kävikö hän kaupassa?</span> <span class="english">Did he/she go to the store?</span></p>
                </div>
                
                <h4>3. Perfect tense</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Oletko käynyt Suomessa?</span> <span class="english">Have you been to Finland?</span></p>
                    <p><span class="finnish">Ovatko he opiskelleet suomea?</span> <span class="english">Have they studied Finnish?</span></p>
                </div>
                
                <h4>4. Conditional</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Tulisitko huomenna?</span> <span class="english">Would you come tomorrow?</span></p>
                    <p><span class="finnish">Haluaisiko hän kahvia?</span> <span class="english">Would he/she like coffee?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>QUESTIONS WITH OTHER ELEMENTS</h3>
            
            <div class="grammar-content">
                <p>The question particle -ko/-kö can also be attached to elements other than verbs to create questions with different emphases:</p>
                
                <h4>1. With nouns or pronouns</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Sinäkö puhut suomea?</span> <span class="english">Is it YOU who speaks Finnish? (emphasis on "you")</span></p>
                    <p><span class="finnish">Suomeako hän opiskelee?</span> <span class="english">Is it FINNISH that he/she is studying? (emphasis on "Finnish")</span></p>
                </div>
                
                <h4>2. With adverbs</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Huomennako sinä tulet?</span> <span class="english">Is it TOMORROW that you're coming? (emphasis on "tomorrow")</span></p>
                    <p><span class="finnish">Hyvinko sinä voit?</span> <span class="english">Are you doing WELL? (emphasis on "well")</span></p>
                </div>
                
                <h4>3. With negation</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Etkö sinä puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></p>
                    <p><span class="finnish">Eivätkö he tule?</span> <span class="english">Aren't they coming?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ANSWERING YES/NO QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>In Finnish, there are several ways to answer yes/no questions:</p>
                
                <h4>1. Affirmative answers</h4>
                <div class="grammar-example">
                    <p>Question: <span class="finnish">Puhutko suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                    <p>Answers:</p>
                    <p><span class="finnish">Kyllä.</span> <span class="english">Yes.</span></p>
                    <p><span class="finnish">Kyllä puhun.</span> <span class="english">Yes, I do.</span></p>
                    <p><span class="finnish">Puhun.</span> <span class="english">I do.</span></p>
                </div>
                
                <h4>2. Negative answers</h4>
                <div class="grammar-example">
                    <p>Question: <span class="finnish">Puhutko suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                    <p>Answers:</p>
                    <p><span class="finnish">Ei.</span> <span class="english">No.</span></p>
                    <p><span class="finnish">En puhu.</span> <span class="english">I don't.</span></p>
                    <p><span class="finnish">En.</span> <span class="english">I don't.</span></p>
                </div>
                
                <p>In Finnish, it's common to repeat the verb in the answer, either in its affirmative or negative form. This is more precise than simply saying "kyllä" or "ei".</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INTONATION QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>In spoken Finnish, a statement can be turned into a question simply by using rising intonation, similar to English:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Sinä tulet huomenna?</span> <span class="english">You're coming tomorrow?</span></p>
                    <p><span class="finnish">Hän on jo täällä?</span> <span class="english">He/she is already here?</span></p>
                </div>
                
                <p>This method is common in casual conversation but is not typically used in formal writing. In writing, these would be marked with a question mark, but they maintain the word order of a statement.</p>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















