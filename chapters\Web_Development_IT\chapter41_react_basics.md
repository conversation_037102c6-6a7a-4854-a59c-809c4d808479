# Chapter 41: React-perusteet / React Basics

## Objectives / Tavoitteet
- Learn vocabulary related to React development in Finnish
- Understand how to discuss component-based UI development
- Be able to explain React concepts and functionality
- Master basic conversations about creating modern web interfaces with React

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Ko<PERSON>nentti - Component
2. Tila - State
3. Propsi - Prop
4. Renderöinti - Rendering
5. Koukku - Hook
6. Virtuaalinen DOM - Virtual DOM
7. Yksisivuinen sovellus - Single-page application
8. Elinkaarimetodi - Lifecycle method
9. Tapahtumankäsittelijä - Event handler
10. Tilanmuutos - State change
11. Käyttöliittymä - User interface
12. <PERSON>ak<PERSON>ivinen - Reactive
13. JSX - JSX
14. Konteksti - Context
15. Reitti - Route

## Grammar Points / Kielioppi
1. **Technical Verbs for React Operations**:
   - Action verbs for UI programming
   - Example: Renderöin komponentin näytölle. (I render the component to the screen.)

2. **Conditional Forms for React Logic**:
   - Expressing UI conditions
   - Example: <PERSON><PERSON> til<PERSON> m<PERSON>, komponentti renderöitäisiin uudelle<PERSON>. (If the state would change, the component would be re-rendered.)

3. **Inessive Case (-ssa/-ssä) for React Contexts**:
   - In component structures
   - Example: Komponentissa on useita tiloja. (There are several states in the component.)

4. **Elative Case (-sta/-stä) for React Data Sources**:
   - From component sources
   - Example: Tieto tulee vanhempikomponentista. (The information comes from the parent component.)

5. **Translative Case (-ksi) for React Transformations**:
   - Converting UI elements
   - Example: Muunnan datan käyttöliittymäelementiksi. (I convert the data into a UI element.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: React programming workshop / React-ohjelmoinnin työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa React-perusteiden työpajaan! Tänään opimme, miten React-kirjastoa käytetään modernien käyttöliittymien rakentamiseen.<br>
<em>(ter-ve-tu-lo-a React-pe-rus-tei-den työ-pa-jaan! tä-nään o-pim-me, mi-ten React-kir-jas-to-a käy-te-tään mo-der-ni-en käyt-tö-liit-ty-mi-en ra-ken-ta-mi-seen.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen käyttänyt perinteistä JavaScriptiä, mutta en ole vielä kokeillut Reactia.<br>
<em>(kii-tos! o-len käyt-tä-nyt pe-rin-teis-tä ja-va-scrip-ti-ä, mut-ta en o-le vie-lä ko-keil-lut re-ac-ti-a.)</em></p>

<p><strong>Ohjaaja</strong>: Hienoa! React on Facebookin kehittämä JavaScript-kirjasto käyttöliittymien rakentamiseen. Sen keskeinen idea on jakaa käyttöliittymä uudelleenkäytettäviin komponentteihin, jotka päivittyvät automaattisesti, kun niiden tila muuttuu.<br>
<em>(hie-no-a! React on fa-ce-boo-kin ke-hit-tä-mä ja-va-script-kir-jas-to käyt-tö-liit-ty-mi-en ra-ken-ta-mi-seen. sen kes-kei-nen i-de-a on ja-ka-a käyt-tö-liit-ty-mä uu-del-leen-käy-tet-tä-viin kom-po-nent-tei-hin, jot-ka päi-vit-ty-vät au-to-maat-ti-ses-ti, kun nii-den ti-la muut-tuu.)</em></p>

<p><strong>Osallistuja</strong>: Miten pääsen alkuun Reactin kanssa?<br>
<em>(mi-ten pää-sen al-kuun re-ac-tin kans-sa?)</em></p>

<p><strong>Osallistuja</strong>: Mitä ovat React-komponentit?<br>
<em>(mi-tä o-vat re-act-kom-po-nen-tit?)</em></p>

<p><strong>Osallistuja</strong>: Mitä ovat propsit ja tila Reactissa?<br>
<em>(mi-tä o-vat prop-sit ja ti-la re-ac-tis-sa?)</em></p>

<p><strong>Osallistuja</strong>: Mitä ovat React-koukut?<br>
<em>(mi-tä o-vat re-act-kou-kut?)</em></p>

<p><strong>Osallistuja</strong>: Miten tapahtumia käsitellään Reactissa?<br>
<em>(mi-ten ta-pah-tu-mi-a kä-si-tel-lään re-ac-tis-sa?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin hakea dataa API:sta Reactissa?<br>
<em>(mi-ten voin ha-ke-a da-ta-a API:s-ta re-ac-tis-sa?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin luoda reitityksen React-sovelluksessa?<br>
<em>(mi-ten voin luo-da rei-ti-tyk-sen re-act-so-vel-luk-ses-sa?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen React-sovelluksen?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen re-act-so-vel-luk-sen?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloitetaan luomalla uusi React-sovellus Create React App -työkalulla ja rakennetaan yksinkertainen tehtävälista-sovellus. Tämä on hyvä ensimmäinen projekti, koska se sisältää monia React-peruskonsepteja: komponentteja, tilanhallintaa, tapahtumankäsittelyä ja lomakkeita. Muista, että React-kehitys on iteratiivista, joten aloita yksinkertaisesta toimivasta versiosta ja lisää ominaisuuksia vähitellen.<br>
<em>(eh-dot-to-mas-ti! a-loi-te-taan luo-mal-la uu-si re-act-so-vel-lus cre-ate re-act app -työ-ka-lul-la ja ra-ken-ne-taan yk-sin-ker-tai-nen teh-tä-vä-lis-ta-so-vel-lus. tä-mä on hy-vä en-sim-mäi-nen pro-jek-ti, kos-ka se si-säl-tää mo-ni-a re-act-pe-rus-kon-sep-te-ja: kom-po-nent-te-ja, ti-lan-hal-lin-ta-a, ta-pah-tu-man-kä-sit-te-ly-ä ja lo-mak-kei-ta. muis-ta, et-tä re-act-ke-hi-tys on i-te-ra-tii-vis-ta, jo-ten a-loi-ta yk-sin-ker-tai-ses-ta toi-mi-vas-ta ver-si-os-ta ja li-sää o-mi-nai-suuk-si-a vä-hi-tel-len.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten React toimii ja miten voin käyttää sitä modernien käyttöliittymien rakentamiseen.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten re-act toi-mii ja mi-ten voin käyt-tää si-tä mo-der-ni-en käyt-tö-liit-ty-mi-en ra-ken-ta-mi-seen.)</em></p>
</div>

### Cultural Notes:
- Finland has a growing React development community, with many tech companies adopting it for frontend development
- Finnish startups often use React for building modern web applications due to its efficiency and component-based architecture
- Many Finnish tech meetups and conferences feature React topics and workshops
- Finnish educational institutions increasingly include React in their web development curricula
- The Finnish tech ecosystem values React's ability to create responsive, maintainable user interfaces


