#!/usr/bin/env python3
"""
Script to update all remaining level 3 Finnish grammar files with correct dropdown structure
"""

import os
import re
import glob

# Define the correct dropdown structures
CORRECT_VIDEOS_DROPDOWN = '''                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt"><PERSON>ome<PERSON></a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>'''

CORRECT_CATEGORIES_DROPDOWN = '''                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>'''

CORRECT_ENTERTAINMENT_DROPDOWN = '''                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>'''

SUBMENU_JAVASCRIPT = '''
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });

                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });'''

# List of files to update (excluding already updated ones)
FILES_TO_UPDATE = [
    # Cases/Grammatical (accusative.html needs completion)
    "finnish_grammar/cases/grammatical/accusative.html",
    
    # Cases/Locative
    "finnish_grammar/cases/locative/ablative.html",
    "finnish_grammar/cases/locative/adessive.html", 
    "finnish_grammar/cases/locative/allative.html",
    "finnish_grammar/cases/locative/elative.html",
    "finnish_grammar/cases/locative/illative.html",
    "finnish_grammar/cases/locative/inessive.html",
    
    # Cases/Other
    "finnish_grammar/cases/other/abessive.html",
    "finnish_grammar/cases/other/comitative.html",
    "finnish_grammar/cases/other/essive.html",
    "finnish_grammar/cases/other/instructive.html",
    "finnish_grammar/cases/other/translative.html",
    
    # Nouns/Types
    "finnish_grammar/nouns/types/type1.html",
    "finnish_grammar/nouns/types/type2.html",
    "finnish_grammar/nouns/types/type3.html",
    "finnish_grammar/nouns/types/type4.html",
    "finnish_grammar/nouns/types/type5.html",
    
    # Verbs/Types
    "finnish_grammar/verbs/types/type1.html",
    "finnish_grammar/verbs/types/type2.html",
    "finnish_grammar/verbs/types/type3.html",
    "finnish_grammar/verbs/types/type4.html",
    "finnish_grammar/verbs/types/type5.html",
    "finnish_grammar/verbs/types/type6.html"
]

def update_file(file_path):
    """Update a single file with correct dropdown structure"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Update Videos dropdown - handle various existing patterns
        videos_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Videos</a>.*?</li>'
        content = re.sub(videos_pattern, CORRECT_VIDEOS_DROPDOWN, content, flags=re.DOTALL)
        
        # Update Categories dropdown
        categories_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>.*?</li>'
        content = re.sub(categories_pattern, CORRECT_CATEGORIES_DROPDOWN, content, flags=re.DOTALL)
        
        # Update Entertainment dropdown
        entertainment_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>.*?</li>'
        content = re.sub(entertainment_pattern, CORRECT_ENTERTAINMENT_DROPDOWN, content, flags=re.DOTALL)
        
        # Add submenu JavaScript if not already present
        if 'dropdown-submenu' not in content:
            # Find the end of the existing JavaScript and add submenu functionality
            js_end_pattern = r'(\s+}\s*}\);\s*</script>)'
            content = re.sub(js_end_pattern, SUBMENU_JAVASCRIPT + r'\1', content)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Updated: {file_path}")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error updating {file_path}: {e}")
        return False

def main():
    """Main function to update all files"""
    print("Starting update of level 3 Finnish grammar files...")
    
    updated_count = 0
    error_count = 0
    
    for file_path in FILES_TO_UPDATE:
        if os.path.exists(file_path):
            if update_file(file_path):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
            error_count += 1
    
    print(f"\nUpdate completed!")
    print(f"Files updated: {updated_count}")
    print(f"Errors: {error_count}")
    
    if error_count == 0:
        print("All level 3 files have been successfully updated with the correct dropdown structure!")

if __name__ == "__main__":
    main()
