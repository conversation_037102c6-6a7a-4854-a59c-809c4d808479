# PowerShell script to update navigation in all grammar HTML files
$grammarDir = "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar"

# Get all HTML files in the grammar directory and its subdirectories
$htmlFiles = Get-ChildItem -Path $grammarDir -Filter "*.html" -Recurse

foreach ($file in $htmlFiles) {
    Write-Host "Processing file: $($file.FullName)"
    
    # Read the file content
    $content = Get-Content -Path $file.FullName -Raw
    
    # Determine the relative path to the root based on the file's depth
    $depth = ($file.FullName.Substring($grammarDir.Length).Split('\') | Where-Object { $_ -ne "" }).Count
    $relativePath = "../" * $depth
    
    # Replace the navigation links
    if ($content -match '<li><a href="[^"]*index.html">Home</a></li>\s*<li><a href="[^"]*video.html">Videos</a></li>') {
        $oldPattern = '<li><a href="[^"]*index.html">Home</a></li>\s*<li><a href="[^"]*video.html">Videos</a></li>'
        $newContent = $content -replace $oldPattern, "<li><a href=`"$($relativePath)index.html`">Home</a></li>`n                <li><a href=`"$($relativePath)audio.html`">Audio</a></li>`n                <li><a href=`"$($relativePath)video.html`">Videos</a></li>"
        
        # Write the updated content back to the file
        Set-Content -Path $file.FullName -Value $newContent
        Write-Host "Updated navigation in: $($file.FullName)"
    } else {
        Write-Host "Navigation pattern not found in: $($file.FullName)"
    }
}

Write-Host "Navigation update complete!"