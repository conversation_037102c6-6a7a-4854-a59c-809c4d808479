# Chapter 47: <PERSON><PERSON><PERSON><PERSON><PERSON>j<PERSON><PERSON>kemus (UX) / User Experience

## Objectives / Tavoitteet
- Learn vocabulary related to user experience design in Finnish
- Understand how to discuss UX principles and methodologies
- Be able to explain user research and testing approaches
- Master basic conversations about creating intuitive and satisfying digital experiences

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. K<PERSON>yttäjäkokemus - User experience
2. Käyttäjätutkimus - User research
3. Käytettävyystestaus - Usability testing
4. K<PERSON><PERSON>täjäpersoona - User persona
5. K<PERSON><PERSON>täjäpolku - User journey
6. K<PERSON>yttötapaus - Use case
7. Prototyyppi - Prototype
8. Rautalankamalli - Wireframe
9. Saavutettavuus - Accessibility
10. Käyttäjäkeskeinen suunnittelu - User-centered design
11. V<PERSON>rovaikutussuunnittelu - Interaction design
12. Informaatioarkkitehtuuri - Information architecture
13. Käyttökokemustavoite - UX goal
14. K<PERSON>yttäjätyytyväisyys - User satisfaction
15. <PERSON><PERSON><PERSON>tökonteksti - Context of use

## Grammar Points / Kielioppi
1. **Technical Verbs for UX Operations**:
   - Action verbs for UX activities
   - Example: Tutkin käyttäjien tarpeita haastatteluilla. (I research users' needs with interviews.)

2. **Conditional Forms for UX Scenarios**:
   - Expressing UX conditions
   - Example: Jos käyttäjät testaisivat prototyyppiä, löytäisimme ongelmakohdat. (If users would test the prototype, we would find the problem areas.)

3. **Inessive Case (-ssa/-ssä) for UX Contexts**:
   - In UX environments
   - Example: Käyttäjäkokemuksessa on huomioitava tunteet ja käytännöllisyys. (In user experience, emotions and practicality must be considered.)

4. **Elative Case (-sta/-stä) for UX Sources**:
   - From UX sources
   - Example: Oivallukset tulevat käyttäjätutkimuksesta. (Insights come from user research.)

5. **Translative Case (-ksi) for UX Transformations**:
   - Converting to UX states
   - Example: Muunnan käyttäjätarinat konkreettisiksi suunnitteluratkaisuiksi. (I convert user stories into concrete design solutions.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: UX design workshop / Käyttäjäkokemussuunnittelun työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa käyttäjäkokemussuunnittelun työpajaan! Tänään opimme, miten luodaan miellyttäviä, tehokkaita ja merkityksellisiä käyttäjäkokemuksia digitaalisissa tuotteissa.<br>
<em>(ter-ve-tu-lo-a käyt-tä-jä-ko-ke-mus-suun-nit-te-lun työ-pa-jaan! tä-nään o-pim-me, mi-ten luo-daan miel-lyt-tä-vi-ä, te-hok-kai-ta ja mer-ki-tyk-sel-li-si-ä käyt-tä-jä-ko-ke-muk-si-a di-gi-taa-li-sis-sa tuot-teis-sa.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Ymmärrän jo käyttöliittymäsuunnittelua, mutta haluaisin oppia enemmän käyttäjäkokemuksesta.<br>
<em>(kii-tos! ym-mär-rän jo käyt-tö-liit-ty-mä-suun-nit-te-lu-a, mut-ta ha-lu-ai-sin op-pi-a e-nem-män käyt-tä-jä-ko-ke-muk-ses-ta.)</em></p>

<p><strong>Ohjaaja</strong>: Hienoa! Aloitetaan selventämällä, miten käyttäjäkokemus (UX) eroaa käyttöliittymäsuunnittelusta (UI) ja miksi se on niin tärkeää. (hie-no-a! a-loi-te-taan sel-ven-tä-mäl-lä, mi-ten käyt-tä-jä-ko-ke-mus (UX) e-ro-aa käyt-tö-liit-ty-mä-suun-nit-te-lus-ta (UI) ja mik-si se on niin tär-ke-ää.)</p>

<p><strong>Osallistuja</strong>: Mikä on käyttöliittymän ja käyttäjäkokemuksen ero?<br>
<em>(mi-kä on käyt-tö-liit-ty-män ja käyt-tä-jä-ko-ke-muk-sen e-ro?)</em></p>

<p><strong>Ohjaaja</strong>: Käyttöliittymän ja käyttäjäkokemuksen ero on seuraava:</p>

<p><strong>Osallistuja</strong>: Mitkä ovat käyttäjäkokemuksen tärkeimmät periaatteet?<br>
<em>(mit-kä o-vat käyt-tä-jä-ko-ke-muk-sen tär-keim-mät pe-ri-aat-teet?)</em></p>

<p><strong>Osallistuja</strong>: Miten käyttäjätutkimusta tehdään?<br>
<em>(mi-ten käyt-tä-jä-tut-ki-mus-ta teh-dään?)</em></p>

<p><strong>Osallistuja</strong>: Miten käyttäjäkokemusta suunnitellaan käytännössä?<br>
<em>(mi-ten käyt-tä-jä-ko-ke-mus-ta suun-ni-tel-laan käy-tän-nös-sä?)</em></p>

<p><strong>Osallistuja</strong>: Miten saavutettavuus huomioidaan käyttäjäkokemuksessa?<br>
<em>(mi-ten saa-vu-tet-ta-vuus huo-mi-oi-daan käyt-tä-jä-ko-ke-muk-ses-sa?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla parantaa projektini käyttäjäkokemusta näiden ohjeiden avulla?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la pa-ran-taa pro-jek-ti-ni käyt-tä-jä-ko-ke-mus-ta näi-den oh-jei-den a-vul-la?)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin luoda parempia käyttäjäkokemuksia digitaalisissa tuotteissani.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin luo-da pa-rem-pi-a käyt-tä-jä-ko-ke-muk-si-a di-gi-taa-li-sis-sa tuot-teis-sa-ni.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong tradition in user-centered design, influenced by companies like Nokia and Fiskars
- Finnish design philosophy emphasizes functionality, simplicity, and respect for the user's needs
- Finnish UX designers often value inclusive design that works for everyone, reflecting the egalitarian values of Finnish society
- Finland's education system produces many skilled UX professionals, with several universities offering specialized programs
- Finnish companies increasingly recognize UX as a competitive advantage, not just a nice-to-have feature


