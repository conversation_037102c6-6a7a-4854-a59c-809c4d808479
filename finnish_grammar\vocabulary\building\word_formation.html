﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Word Formation in Finnish - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .vocabulary-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .vocabulary-section {
            margin-bottom: 30px;
        }
        
        .vocabulary-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .vocabulary-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .vocabulary-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .vocabulary-table th, .vocabulary-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        
        .vocabulary-table th {
            background-color: #f5f5f5;
            font-weight: 500;
        }
        
        .vocabulary-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .example-box {
            background-color: #f5f5f5;
            border-left: 4px solid #0066cc;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .example-box p {
            margin: 5px 0;
        }
        
        .note-box {
            background-color: #fff8e1;
            border-left: 4px solid #ffc107;
            padding: 15px;
            margin: 20px 0;
            border-radius: 0 5px 5px 0;
        }
        
        .pronunciation {
            font-style: italic;
            color: #666;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        .audio-button {
            background-color: #0066cc;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .audio-button:hover {
            background-color: #0055aa;
        }
        
        .word-formation-diagram {
            max-width: 100%;
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .word-formation-diagram h4 {
            margin-top: 0;
            color: #0066cc;
        }
        
        .word-formation-diagram .word-parts {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            margin: 15px 0;
        }
        
        .word-part {
            padding: 5px 10px;
            margin: 5px;
            background-color: #e6f2ff;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .word-arrow {
            margin: 0 10px;
            color: #666;
            font-size: 1.2em;
        }
        
        .word-result {
            padding: 5px 15px;
            margin: 5px;
            background-color: #0066cc;
            color: white;
            border-radius: 3px;
            font-weight: 500;
        }
        
        .word-meaning {
            margin-top: 10px;
            font-style: italic;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .vocabulary-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .vocabulary-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .example-box {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .note-box {
            background-color: #332b00;
            border-left: 4px solid #ffc107;
        }
        
        [data-theme="dark"] .pronunciation {
            color: #aaa;
        }
        
        [data-theme="dark"] .word-formation-diagram {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .word-part {
            background-color: #1a3d66;
            color: #ddd;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="formation-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="formation-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="formation-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="vocabulary-container">
        <div class="breadcrumbs">
            <a href="../../index.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Vocabulary</a>
            <span class="separator">></span>
            <span>Word Formation</span>
        </div>
        
        <section class="vocabulary-section">
            <h2>Word Formation in Finnish</h2>
            <p>Finnish is known for its ability to create new words through various word formation processes. This page explains how Finnish builds new words using derivation, compounding, and other methods. Understanding these patterns will help you recognize and learn new Finnish vocabulary more effectively.</p>
        </section>
        
        <section class="vocabulary-section">
            <h3>Derivation: Creating Words with Suffixes</h3>
            <p>One of the most common ways to form new words in Finnish is through derivation – adding suffixes to existing words to create new ones with related meanings. Finnish has a rich system of derivational suffixes.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Suffix</th>
                    <th>Function</th>
                    <th>Examples</th>
                </tr>
                <tr>
                    <td>-ja/-jä</td>
                    <td>Forms agent nouns (person who does something)</td>
                    <td>opettaa (to teach) → opettaja (teacher)<br>
                        ajaa (to drive) → ajaja (driver)</td>
                </tr>
                <tr>
                    <td>-lainen/-läinen</td>
                    <td>Forms nouns indicating nationality or membership</td>
                    <td>Suomi (Finland) → suomalainen (Finnish person)<br>
                        kaupunki (city) → kaupunkilainen (city dweller)</td>
                </tr>
                <tr>
                    <td>-llinen</td>
                    <td>Forms adjectives meaning "having" or "characterized by"</td>
                    <td>ystävä (friend) → ystävällinen (friendly)<br>
                        rauha (peace) → rauhallinen (peaceful)</td>
                </tr>
                <tr>
                    <td>-ton/-tön</td>
                    <td>Forms adjectives meaning "without" (similar to English "-less")</td>
                    <td>onni (happiness) → onneton (unhappy)<br>
                        toivo (hope) → toivoton (hopeless)</td>
                </tr>
                <tr>
                    <td>-inen</td>
                    <td>Forms adjectives indicating material or characteristic</td>
                    <td>puu (wood) → puinen (wooden)<br>
                        kesä (summer) → kesäinen (summery)</td>
                </tr>
                <tr>
                    <td>-sto/-stö</td>
                    <td>Forms collective nouns</td>
                    <td>kirja (book) → kirjasto (library)<br>
                        laiva (ship) → laivasto (fleet)</td>
                </tr>
                <tr>
                    <td>-us/-ys</td>
                    <td>Forms abstract nouns from adjectives or verbs</td>
                    <td>vapaa (free) → vapaus (freedom)<br>
                        rakastaa (to love) → rakkaus (love)</td>
                </tr>
                <tr>
                    <td>-uus/-yys</td>
                    <td>Forms abstract nouns from adjectives</td>
                    <td>uusi (new) → uutuus (novelty)<br>
                        yksinäinen (lonely) → yksinäisyys (loneliness)</td>
                </tr>
                <tr>
                    <td>-in</td>
                    <td>Forms nouns for tools or instruments</td>
                    <td>avata (to open) → avain (key)<br>
                        soittaa (to play) → soitin (musical instrument)</td>
                </tr>
                <tr>
                    <td>-nen</td>
                    <td>Forms diminutives or adjectives</td>
                    <td>kala (fish) → kalanen (little fish)<br>
                        puna (red) → punainen (red)</td>
                </tr>
                <tr>
                    <td>-ma/-mä</td>
                    <td>Forms result nouns from verbs</td>
                    <td>elää (to live) → elämä (life)<br>
                        kuolla (to die) → kuolema (death)</td>
                </tr>
                <tr>
                    <td>-sti</td>
                    <td>Forms adverbs from adjectives</td>
                    <td>nopea (fast) → nopeasti (quickly)<br>
                        hidas (slow) → hitaasti (slowly)</td>
                </tr>
            </table>
            
            <div class="word-formation-diagram">
                <h4>Example of Derivation Process</h4>
                <div class="word-parts">
                    <span class="word-part">kirja</span>
                    <span class="word-arrow">+</span>
                    <span class="word-part">-sto</span>
                    <span class="word-arrow">→</span>
                    <span class="word-result">kirjasto</span>
                </div>
                <div class="word-meaning">
                    <strong>kirja</strong> (book) + <strong>-sto</strong> (collective suffix) = <strong>kirjasto</strong> (library, collection of books)
                </div>
            </div>
            
            <div class="example-box">
                <p><strong>Multiple derivations:</strong> Finnish allows for multiple suffixes to be added to a word:</p>
                <p>kirja (book) → kirjallinen (literary) → kirjallisuus (literature)</p>
                <p>oppia (to learn) → opettaa (to teach) → opettaja (teacher) → opettajatar (female teacher)</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Compounding: Combining Words</h3>
            <p>Compounding is extremely common in Finnish. Two or more words can be combined to create a new word with a related meaning. Finnish compound words are written as a single word without spaces or hyphens.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Compound Type</th>
                    <th>Structure</th>
                    <th>Examples</th>
                </tr>
                <tr>
                    <td>Noun + Noun</td>
                    <td>The most common type, where the first noun modifies the second</td>
                    <td>kirja (book) + kauppa (shop) → kirjakauppa (bookstore)<br>
                        jää (ice) + kaappi (cabinet) → jääkaappi (refrigerator)</td>
                </tr>
                <tr>
                    <td>Adjective + Noun</td>
                    <td>An adjective modifies a noun</td>
                    <td>iso (big) + äiti (mother) → isoäiti (grandmother)<br>
                        uusi (new) + vuosi (year) → uusivuosi (New Year)</td>
                </tr>
                <tr>
                    <td>Verb + Noun</td>
                    <td>A verb stem combined with a noun</td>
                    <td>istua (to sit) + paikka (place) → istumapaikka (seat)<br>
                        nukkua (to sleep) + huone (room) → makuuhuone (bedroom)</td>
                </tr>
                <tr>
                    <td>Noun + Verb derivative</td>
                    <td>A noun combined with a verb-derived word</td>
                    <td>vero (tax) + maksaa (to pay) → veronmaksaja (taxpayer)<br>
                        ruoka (food) + laittaa (to make) → ruoanlaitto (cooking)</td>
                </tr>
                <tr>
                    <td>Multiple compounds</td>
                    <td>Three or more words combined</td>
                    <td>keski (middle) + aika (time) + tutkimus (research) → keskiaikatutkimus (medieval studies)<br>
                        työ (work) + voima (power) + toimisto (office) → työvoimatoimisto (employment office)</td>
                </tr>
            </table>
            
            <div class="word-formation-diagram">
                <h4>Example of Compounding Process</h4>
                <div class="word-parts">
                    <span class="word-part">lento</span>
                    <span class="word-arrow">+</span>
                    <span class="word-part">kone</span>
                    <span class="word-arrow">→</span>
                    <span class="word-result">lentokone</span>
                </div>
                <div class="word-meaning">
                    <strong>lento</strong> (flight) + <strong>kone</strong> (machine) = <strong>lentokone</strong> (airplane)
                </div>
            </div>
            
            <div class="note-box">
                <p><strong>Note:</strong> When forming compounds, the first part often appears in the genitive case (with -n ending) or in a special compound form. For example:</p>
                <p>kala (fish) + keitto (soup) → kalakeitto (fish soup)</p>
                <p>maito (milk) + pullo (bottle) → maitopullo (milk bottle)</p>
                <p>But sometimes the genitive is used:</p>
                <p>lapsi (child) + huone (room) → lastenhuone (children's room) [lapsi → lasten (genitive plural)]</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Prefixes and Foreign Prefixes</h3>
            <p>Unlike many Indo-European languages, Finnish traditionally doesn't use many prefixes. However, with increasing international influence, some foreign prefixes have been adopted, especially in academic and technical vocabulary.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Prefix</th>
                    <th>Meaning</th>
                    <th>Examples</th>
                </tr>
                <tr>
                    <td>epä-</td>
                    <td>One of the few native Finnish prefixes, meaning "un-", "non-", or "dis-"</td>
                    <td>epävarma (uncertain)<br>
                        epäonnistua (to fail)</td>
                </tr>
                <tr>
                    <td>anti-</td>
                    <td>Against, opposing</td>
                    <td>antibiootit (antibiotics)<br>
                        antiseptinen (antiseptic)</td>
                </tr>
                <tr>
                    <td>super-</td>
                    <td>Above, beyond</td>
                    <td>supermarketti (supermarket)<br>
                        supersankari (superhero)</td>
                </tr>
                <tr>
                    <td>mini-</td>
                    <td>Small</td>
                    <td>minigolf (mini golf)<br>
                        minihame (miniskirt)</td>
                </tr>
                <tr>
                    <td>mikro-</td>
                    <td>Very small</td>
                    <td>mikroaaltouuni (microwave oven)<br>
                        mikrobiologia (microbiology)</td>
                </tr>
                <tr>
                    <td>makro-</td>
                    <td>Large</td>
                    <td>makrotalous (macroeconomics)<br>
                        makrokuvaus (macro photography)</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Conversion and Zero Derivation</h3>
            <p>Finnish sometimes creates new words by using an existing word in a new grammatical function without adding any affixes.</p>
            
            <div class="example-box">
                <p><strong>Examples of conversion:</strong></p>
                <p>syödä (to eat) → syönti (eating) [verbal noun]</p>
                <p>juosta (to run) → juoksu (running) [verbal noun]</p>
                <p>kylmä (cold) [adjective] → kylmä (coldness) [noun]</p>
            </div>
        </section>
        
        <section class="vocabulary-section">
            <h3>Abbreviation and Clipping</h3>
            <p>Modern Finnish, especially in colloquial speech, often shortens longer words.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Original Word</th>
                    <th>Abbreviated Form</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>matematiikka</td>
                    <td>matikka</td>
                    <td>mathematics</td>
                </tr>
                <tr>
                    <td>televisio</td>
                    <td>telkkari</td>
                    <td>television</td>
                </tr>
                <tr>
                    <td>tietokone</td>
                    <td>kone</td>
                    <td>computer</td>
                </tr>
                <tr>
                    <td>ravintola</td>
                    <td>rafla</td>
                    <td>restaurant</td>
                </tr>
                <tr>
                    <td>puhelin</td>
                    <td>puhelin → puhelin → puhelin</td>
                    <td>telephone</td>
                </tr>
                <tr>
                    <td>yliopisto</td>
                    <td>ylkkäri</td>
                    <td>university</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Acronyms and Initialisms</h3>
            <p>Like many languages, Finnish uses acronyms and initialisms, especially for organizations and technical terms.</p>
            
            <table class="vocabulary-table">
                <tr>
                    <th>Acronym</th>
                    <th>Full Form</th>
                    <th>Meaning</th>
                </tr>
                <tr>
                    <td>Kela</td>
                    <td>Kansaneläkelaitos</td>
                    <td>Social Insurance Institution</td>
                </tr>
                <tr>
                    <td>YLE</td>
                    <td>Yleisradio</td>
                    <td>Finnish Broadcasting Company</td>
                </tr>
                <tr>
                    <td>HSL</td>
                    <td>Helsingin seudun liikenne</td>
                    <td>Helsinki Region Transport</td>
                </tr>
                <tr>
                    <td>THL</td>
                    <td>Terveyden ja hyvinvoinnin laitos</td>
                    <td>Finnish Institute for Health and Welfare</td>
                </tr>
            </table>
        </section>
        
        <section class="vocabulary-section">
            <h3>Practical Tips for Learning Finnish Word Formation</h3>
            
            <div class="example-box">
                <p><strong>1. Learn the common suffixes</strong> - Recognizing suffixes like -ja/-jä, -inen, and -llinen will help you understand many derived words.</p>
                <p><strong>2. Break down compound words</strong> - When you encounter a long Finnish word, try to identify its component parts.</p>
                <p><strong>3. Look for patterns</strong> - Notice how certain types of words are typically formed (e.g., professions often use the -ja/-jä suffix).</p>
                <p><strong>4. Practice creating words</strong> - Try forming new words using the patterns you've learned.</p>
                <p><strong>5. Pay attention to vowel harmony</strong> - Remember that suffixes follow vowel harmony rules (e.g., -jä with front vowels, -ja with back vowels).</p>
            </div>
            
            <div class="note-box">
                <p><strong>Note on vowel harmony:</strong> Finnish word formation follows vowel harmony rules. If a word contains the front vowels ä, ö, or y, then suffixes will use the front vowel variant (e.g., -jä, -tön). If a word contains only back vowels (a, o, u), then suffixes will use the back vowel variant (e.g., -ja, -ton).</p>
            </div>
        </section>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















