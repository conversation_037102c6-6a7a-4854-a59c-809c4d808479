## 📖 <PERSON>hung Lớp <PERSON> (TIẾNG VIỆT & TIẾNG PHẦN LAN)

**<PERSON><PERSON><PERSON> đích / Purpose:**

- T<PERSON><PERSON> hợp từ vựng phổ biến trong giao tiếp hàng ngày và lĩnh vực IT. / Koota yleiskieli ja IT-sananvarasto.
- Tập trung vào các câu hội thoại, thả<PERSON> luận (discussion) thực tế. / Keskittyä aidon keskustelun skenaarioihin.

---

# 🔖 Danh Sách Chương / Chapter List

## 📌 30 Chapters for Daily Life (Hàng ngày)

1. Terveh<PERSON>s aamulla / Morning Greetings
2. Perhe ja ystävät / Family & Friends
3. Koti ja asuminen / Home & Living
4. Päivittäiset ostokset / Daily Shopping
5. Liikenne ja kulkuvälineet / Transport & Vehicles
6. Terveys ja hyvinvointi / Health & Wellness
7. Sää ja vuodenajat / Weather & Seasons
8. Vapaaaika ja harrastukset / Leisure & Hobbies
9. <PERSON><PERSON> ja r<PERSON>lu / Nutrition & Meals
10. Ravitsemus er<PERSON> / Special Diets
11. Musiikki ja elokuvat / Music & Movies
12. Muoti ja pukeutuminen / Fashion & Clothing
13. Ystävälliset keskustelut / Friendly Small Talk
14. Juhlapyhät ja traditiot / Holidays & Traditions
15. Koulutus ja opiskelu / Education & Study
16. Työpaikka ja ura / Workplace & Career
17. Raha ja pankkipalvelut / Money & Banking
18. Matkapuhelimet & viestintä / Phones & Communication
19. Teknologian peruskäyttö / Basic Tech Use
20. Netiketti ja sosiaalinen media / Netiquette & Social Media
21. Lemmikit ja eläimet / Pets & Animals
22. Ympäristö ja kestävyys / Environment & Sustainability
23. Kierrätys ja jätteiden käsittely / Recycling & Waste
24. Turvallisuus kotona / Home Safety
25. Ensisijainen apu / First Aid Basics
26. Paikallinen kulttuuri / Local Culture
27. Virastot ja palvelut / Offices & Services
28. Tilaukset verkossa / Online Ordering
29. Korjaukset ja huolto / Repairs & Maintenance
30. Yhteenveto: Päivittäinen keskustelu / Review: Daily Conversations

## 📌 10–20 Chapters for Web Development IT (Web-kehitys IT)

31. HTML-perusteet / HTML Basics
32. CSS-tyylittely / CSS Styling
33. Responsiivinen suunnittelu / Responsive Design
34. JavaScript-perusteet / JavaScript Basics
35. DOM-manipulaatio / DOM Manipulation
36. Versiohallinta Git / Git Version Control
37. Web-palvelimet ja hosting / Web Servers & Hosting
38. RESTful API-periaatteet / RESTful APIs
39. Frontend-frameworkit / Frontend Frameworks
40. Backend-kehitys Node.js / Backend with Node.js
41. Tietokantaintegraatio / Database Integration
42. Turvallisuus webissä / Web Security
43. DevOps ja CI/CD / DevOps & CI/CD
44. Kontit Dockerilla / Containers with Docker
45. Pilvi-integraatiot / Cloud Integrations
46. SEO ja suorituskyky / SEO & Performance
47. Testaus ja virheenkäsittely / Testing & Debugging
48. Jatkuva kehitys / Continuous Development
49. Sovellusten julkaisu / Deployment & Release
50. Uudet web-trendit / Emerging Web Trends

## 📌 10 Chapters for Cleaner (Siivooja)

51. Työvälineet ja kemikaalit / Tools & Chemicals
52. Pintojen puhdistus / Surface Cleaning
53. Lattioiden pesu / Floor Cleaning
54. Ikkunoiden pyyhintä / Window Washing
55. Jätteiden lajittelu / Waste Sorting
56. Hygienia ja turvallisuus / Hygiene & Safety
57. Aikataulutus työtehtäville / Scheduling Tasks
58. Asiakaskohtaiset ohjeet / Client-specific Instructions
59. Laatuarvioinnit / Quality Checks
60. Päivittäinen raportointi / Daily Reporting

## 📌 10 Chapters for Kitchen Assistant (Keittiöapulainen)

61. Raaka-aineet ja varastointi / Ingredients & Storage
62. Esivalmistelut / Prep Work
63. Keitto- ja paistotekniikat / Cooking Techniques
64. Hygienia keittiössä / Kitchen Hygiene
65. Työvälineet ja välinehuolto / Utensils & Maintenance
66. Annosten kasaus / Plating & Presentation
67. Erityisruokavaliot / Special Diets Prep
68. Tilaukset ja inventaario / Orders & Inventory
69. Aikataulutus vuoroissa / Shift Scheduling
70. Hätätilanteet keittiössä / Kitchen Emergencies

## 📌 10 Chapters for Warehouse (Varasto)

71. Vastaanotto ja tarkastus / Receiving & Inspection
72. Varastointitekniikat / Storage Techniques
73. Tilausten keräily / Order Picking
74. Pakkaus ja lähetys / Packing & Shipping
75. Turvallisuus varastossa / Warehouse Safety
76. Trukki- ja laiteajot / Forklift & Equipment
77. Inventaario ja varastokirjanpito / Inventory Management
78. Jätteiden hallinta / Waste Management
79. Laatutarkastukset / Quality Inspections
80. Päivittäinen raportointi / Daily Reporting

---

## 🎯 Cấu Trúc Chung Mỗi Chapter / Structure per Chapter

| Phần | Tiếng Việt                                                         | Tiếng Phần Lan                                               |
| ---- | ------------------------------------------------------------------ | ------------------------------------------------------------ |
| 1    | **Mục tiêu (Objectives)**: Kỹ năng đạt được.                       | **Tavoitteet**: Osaamistavoitteet.                           |
| 2    | **Thời lượng audio (Audio Duration)**: 5 phút.                     | **Äänitys kesto**: 5 minuuttia.                                |
| 3    | **Từ vựng (Vocabulary)**: 12–15 từ.                                | **Sanasto**: 12–15 sanaa.                                    |
| 4    | **Ngữ pháp (Grammar Points)**: 3–5 điểm.                           | **Kielioppi**: 3–5 pääkohtaa.                                |
| 5    | **Kịch bản & Ghi chú (Script & Notes)**: Đoạn hội thoại + phát âm. | **Käsikirjoitus & muistiinpanot**: dialogi + ääntämisohjeet. |

---
