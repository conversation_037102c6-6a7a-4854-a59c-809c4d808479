#!/usr/bin/env python3
"""
Script to update all remaining Finnish grammar files with correct dropdown structure
"""

import os
import re

# Define the correct dropdown structures for different levels

# For level 2 files (like nouns/index.html, verbs/index.html)
LEVEL2_VIDEOS_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suome<PERSON></a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>"""

LEVEL2_CATEGORIES_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../index.html#daily-life">Daily Life</a>
                        <a href="../../index.html#web-development">Web Development</a>
                        <a href="../../index.html#cleaner">Cleaner</a>
                        <a href="../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>"""

LEVEL2_ENTERTAINMENT_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../games.html">Games</a>
                    </div>
                </li>"""

# For level 3 files (already defined in previous script)
LEVEL3_VIDEOS_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>"""

LEVEL3_CATEGORIES_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>"""

LEVEL3_ENTERTAINMENT_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>"""

# For level 4 files (like sentence_structure/conjunctions/coordinating.html)
LEVEL4_VIDEOS_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>"""

LEVEL4_CATEGORIES_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>"""

LEVEL4_ENTERTAINMENT_DROPDOWN = """                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>"""

# Submenu JavaScript functionality
SUBMENU_JAVASCRIPT = """
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });

                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });"""

# List of all files to update
FILES_TO_UPDATE = [
    # Level 2 files (nouns, verbs, sentence_structure index files)
    ("finnish_grammar/nouns/index.html", 2),
    ("finnish_grammar/verbs/index.html", 2),
    ("finnish_grammar/sentence_structure/index.html", 2),
    # Level 2 files (other noun features)
    ("finnish_grammar/nouns/compound_nouns.html", 2),
    ("finnish_grammar/nouns/consonant_gradation.html", 2),
    ("finnish_grammar/nouns/derivation.html", 2),
    # Level 2 files (other verb features)
    ("finnish_grammar/verbs/consonant_gradation.html", 2),
    ("finnish_grammar/verbs/negation.html", 2),
    # Level 3 files (plural forms)
    ("finnish_grammar/nouns/plurals/genitive.html", 3),
    ("finnish_grammar/nouns/plurals/nominative.html", 3),
    ("finnish_grammar/nouns/plurals/other_cases.html", 3),
    ("finnish_grammar/nouns/plurals/partitive.html", 3),
    # Level 3 files (possessives)
    ("finnish_grammar/nouns/possessives/index.html", 3),
    # Level 3 files (verb tenses)
    ("finnish_grammar/verbs/tenses/future.html", 3),
    ("finnish_grammar/verbs/tenses/past.html", 3),
    ("finnish_grammar/verbs/tenses/present.html", 3),
    # Level 3 files (verb moods)
    ("finnish_grammar/verbs/moods/conditional.html", 3),
    ("finnish_grammar/verbs/moods/imperative.html", 3),
    ("finnish_grammar/verbs/moods/potential.html", 3),
    # Level 4 files (sentence structure - conjunctions)
    ("finnish_grammar/sentence_structure/conjunctions/coordinating.html", 4),
    ("finnish_grammar/sentence_structure/conjunctions/relative.html", 4),
    ("finnish_grammar/sentence_structure/conjunctions/reported_speech.html", 4),
    ("finnish_grammar/sentence_structure/conjunctions/subordinating.html", 4),
    # Level 4 files (sentence structure - negation)
    ("finnish_grammar/sentence_structure/negation/basic.html", 4),
    ("finnish_grammar/sentence_structure/negation/double.html", 4),
    ("finnish_grammar/sentence_structure/negation/questions.html", 4),
    # Level 4 files (sentence structure - questions)
    ("finnish_grammar/sentence_structure/questions/indirect.html", 4),
    ("finnish_grammar/sentence_structure/questions/question_words.html", 4),
    ("finnish_grammar/sentence_structure/questions/yes_no.html", 4),
    # Level 4 files (sentence structure - word order)
    ("finnish_grammar/sentence_structure/word_order/adverbials.html", 4),
    ("finnish_grammar/sentence_structure/word_order/basic.html", 4),
    ("finnish_grammar/sentence_structure/word_order/objects.html", 4),
    ("finnish_grammar/sentence_structure/word_order/subject_predicate.html", 4),
]


def get_dropdown_templates(level):
    """Get the correct dropdown templates for the given level"""
    if level == 2:
        return (
            LEVEL2_VIDEOS_DROPDOWN,
            LEVEL2_CATEGORIES_DROPDOWN,
            LEVEL2_ENTERTAINMENT_DROPDOWN,
        )
    elif level == 3:
        return (
            LEVEL3_VIDEOS_DROPDOWN,
            LEVEL3_CATEGORIES_DROPDOWN,
            LEVEL3_ENTERTAINMENT_DROPDOWN,
        )
    elif level == 4:
        return (
            LEVEL4_VIDEOS_DROPDOWN,
            LEVEL4_CATEGORIES_DROPDOWN,
            LEVEL4_ENTERTAINMENT_DROPDOWN,
        )
    else:
        return None, None, None


def update_file(file_path, level):
    """Update a single file with correct dropdown structure"""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        original_content = content
        videos_dropdown, categories_dropdown, entertainment_dropdown = (
            get_dropdown_templates(level)
        )

        if not videos_dropdown:
            print(f"✗ Invalid level {level} for {file_path}")
            return False

        # Update Videos dropdown - handle various existing patterns
        videos_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Videos</a>.*?</li>'
        content = re.sub(videos_pattern, videos_dropdown, content, flags=re.DOTALL)

        # Update Categories dropdown
        categories_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Categories</a>.*?</li>'
        content = re.sub(
            categories_pattern, categories_dropdown, content, flags=re.DOTALL
        )

        # Update Entertainment dropdown
        entertainment_pattern = r'<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>.*?</li>'
        content = re.sub(
            entertainment_pattern, entertainment_dropdown, content, flags=re.DOTALL
        )

        # Add submenu JavaScript if not already present
        if "dropdown-submenu" not in content or "submenu-header" not in content:
            # Find the end of the existing JavaScript and add submenu functionality
            js_end_pattern = r"(\s+}\s*}\);\s*</script>)"
            if re.search(js_end_pattern, content):
                content = re.sub(js_end_pattern, SUBMENU_JAVASCRIPT + r"\1", content)

        # Only write if content changed
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            print(f"✓ Updated: {file_path} (level {level})")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False

    except Exception as e:
        print(f"✗ Error updating {file_path}: {e}")
        return False


def main():
    """Main function to update all files"""
    print("Starting update of all remaining Finnish grammar files...")

    updated_count = 0
    error_count = 0

    for file_path, level in FILES_TO_UPDATE:
        if os.path.exists(file_path):
            if update_file(file_path, level):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
            error_count += 1

    print(f"\nUpdate completed!")
    print(f"Files updated: {updated_count}")
    print(f"Errors: {error_count}")

    if error_count == 0:
        print("All remaining Finnish grammar files have been successfully updated!")


if __name__ == "__main__":
    main()
