$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Where-Object { $_.FullName -ne "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar/index.html" } | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
    
  # Calculate the correct relative path to the games.html file
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $depth = ($filePath.Split("\").Length - 1)
      
  # For files in finnish_grammar folder, we need to go up one level
  # For files in subfolders, we need to go up additional levels
  $prefix = "../" * $depth
      
  # Look for the Entertainment dropdown section and fix the path
  if ($content -match '<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>\s*<div class="dropdown-content">\s*<a href="[^"]*">Games</a>') {
    # Replace the path with the correct one using regex with capture groups
    $newContent = $content -replace '(<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>\s*<div class="dropdown-content">\s*<a href=")[^"]*(">Games</a>)', "`$1${prefix}games.html`$2"
      
    # Save the modified content back to the file if changes were made
    if ($newContent -ne $originalContent) {
      Set-Content -Path $file -Value $newContent
      Write-Host "Fixed path in: $file"
    }
  }
}

Write-Host "All paths have been fixed!"