﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Genitive Case - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../index.html#web-development">Web Development</a>
                        <a href="../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Cases</a>
            <span class="separator">></span>
            <span>Genitive Case</span>
        </div>
        
        <section class="grammar-section">
            <h2>Genitive Case (Genetiivi)</h2>
            <p>The genitive case in Finnish is used to express possession and ownership. It is formed by adding the ending -n to the word stem.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMATION OF THE GENITIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The genitive case is formed by adding -n to the word stem. The stem often undergoes consonant gradation.</p>
                
                <p>Examples of words in the genitive case:</p>
                <ul>
                    <li>talo (house) → talon (of the house)</li>
                    <li>kissa (cat) → kissan (of the cat)</li>
                    <li>pöytä (table) → pöydän (of the table)</li>
                    <li>ihminen (person) → ihmisen (of the person)</li>
                    <li>kirja (book) → kirjan (of the book)</li>
                </ul>
                
                <p>The genitive plural is formed by adding -jen, -ien, or -ten to the word stem:</p>
                <ul>
                    <li>talo → talojen (of the houses)</li>
                    <li>kissa → kissojen (of the cats)</li>
                    <li>pöytä → pöytien (of the tables)</li>
                    <li>ihminen → ihmisten (of the people)</li>
                    <li>kirja → kirjojen (of the books)</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">Talon katto on punainen.</span> <span class="english">The roof of the house is red.</span></p>
                    <p><span class="finnish">Talojen katot ovat punaisia.</span> <span class="english">The roofs of the houses are red.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE OF THE GENITIVE CASE</h3>
            
            <div class="grammar-content">
                <p>The genitive case is used in the following situations:</p>
                
                <h4>1. To express possession</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minun kirjani on pöydällä.</span> <span class="english">My book is on the table.</span></p>
                    <p><span class="finnish">Opettajan auto on sininen.</span> <span class="english">The teacher's car is blue.</span></p>
                </div>
                
                <h4>2. With postpositions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Talon edessä on puu.</span> <span class="english">There is a tree in front of the house.</span></p>
                    <p><span class="finnish">Kirjan alla on avain.</span> <span class="english">There is a key under the book.</span></p>
                </div>
                
                <h4>3. As the object of certain verbs</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Ostan kirjan.</span> <span class="english">I will buy a/the book.</span></p>
                    <p><span class="finnish">Löysin avaimen.</span> <span class="english">I found a/the key.</span></p>
                </div>
                
                <h4>4. To express quantity</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Kuppi kahvia</span> <span class="english">A cup of coffee</span></p>
                    <p><span class="finnish">Litra maitoa</span> <span class="english">A liter of milk</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>CONSONANT GRADATION IN THE GENITIVE CASE</h3>
            
            <div class="grammar-content">
                <p>Many Finnish words undergo consonant gradation when forming the genitive case. This means that certain consonants change between strong and weak forms.</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Strong → Weak</th>
                        <th>Nominative</th>
                        <th>Genitive</th>
                    </tr>
                    <tr>
                        <td>kk → k</td>
                        <td>kukka (flower)</td>
                        <td>kukan</td>
                    </tr>
                    <tr>
                        <td>pp → p</td>
                        <td>kauppa (shop)</td>
                        <td>kaupan</td>
                    </tr>
                    <tr>
                        <td>tt → t</td>
                        <td>katto (roof)</td>
                        <td>katon</td>
                    </tr>
                    <tr>
                        <td>k → ∅ (disappears)</td>
                        <td>joki (river)</td>
                        <td>joen</td>
                    </tr>
                    <tr>
                        <td>p → v</td>
                        <td>leipä (bread)</td>
                        <td>leivän</td>
                    </tr>
                    <tr>
                        <td>t → d</td>
                        <td>katu (street)</td>
                        <td>kadun</td>
                    </tr>
                </table>
                
                <div class="grammar-example">
                    <p><span class="finnish">Kukan väri on keltainen.</span> <span class="english">The color of the flower is yellow.</span></p>
                    <p><span class="finnish">Kadun nimi on Mannerheimintie.</span> <span class="english">The name of the street is Mannerheimintie.</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SPECIAL CASES AND EXCEPTIONS</h3>
            
            <div class="grammar-content">
                <p>Some words have special forms in the genitive case:</p>
                
                <h4>1. Words ending in -nen</h4>
                <p>Words ending in -nen change to -sen in the genitive:</p>
                <div class="grammar-example">
                    <p><span class="finnish">nainen → naisen</span> <span class="english">woman → of the woman</span></p>
                    <p><span class="finnish">suomalainen → suomalaisen</span> <span class="english">Finn → of the Finn</span></p>
                </div>
                
                <h4>2. Words ending in -e</h4>
                <p>Many words ending in -e add -en in the genitive:</p>
                <div class="grammar-example">
                    <p><span class="finnish">perhe → perheen</span> <span class="english">family → of the family</span></p>
                    <p><span class="finnish">huone → huoneen</span> <span class="english">room → of the room</span></p>
                </div>
                
                <h4>3. Words ending in -i</h4>
                <p>Many words ending in -i change to -en in the genitive:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kivi → kiven</span> <span class="english">stone → of the stone</span></p>
                    <p><span class="finnish">ovi → oven</span> <span class="english">door → of the door</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }


    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Only handle submenu on desktop or when mobile menu is open
                if (window.innerWidth > 767 || navLinks.classList.contains('show')) {
                    // Close other active submenus
                    dropdownSubmenus.forEach(otherSubmenu => {
                        if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                            otherSubmenu.classList.remove('active');
                        }
                    });

                    // Toggle current submenu
                    submenu.classList.toggle('active');
                }
            });
        }
    });

    // Close submenus when clicking outside (but not when mobile menu is involved)
    document.addEventListener('click', function(e) {
        // Don't close submenus if clicking on mobile menu elements
        if (!e.target.closest('.dropdown-submenu') && 
            !e.target.closest('.mobile-menu-toggle') && 
            !e.target.closest('.nav-links')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });
});
</script>
</body>
</html>
















