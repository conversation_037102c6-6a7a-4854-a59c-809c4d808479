﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Noun Derivation - Finnish Grammar - Opiskelen Su<PERSON>a</title>
    <link rel="stylesheet" href="../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../index.html">Home</a></li>
                <li><a href="../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../grammar.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../index.html#daily-life">Daily Life</a>
                        <a href="../../index.html#web-development">Web Development</a>
                        <a href="../../index.html#cleaner">Cleaner</a>
                        <a href="../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../grammar.html">Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="index.html">Nouns</a>
            <span class="separator">></span>
            <span>Derivation</span>
        </div>
        
        <section class="grammar-section">
            <h2>Noun Derivation in Finnish</h2>
            <p>Finnish is a language rich in derivational morphology, allowing the creation of new nouns from other words using various suffixes. This page explains how to derive nouns from different word classes in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>DERIVING NOUNS FROM VERBS</h3>
            
            <div class="grammar-content">
                <p>Finnish has several suffixes to derive nouns from verbs:</p>
                
                <h4>1. -minen</h4>
                <p>The most common and regular way to form a noun from a verb. It creates a noun that refers to the action of the verb:</p>
                <div class="grammar-example">
                    <p><span class="finnish">lukea (to read) → lukeminen</span> <span class="english">reading</span></p>
                    <p><span class="finnish">juosta (to run) → juokseminen</span> <span class="english">running</span></p>
                    <p><span class="finnish">opiskella (to study) → opiskeleminen</span> <span class="english">studying</span></p>
                </div>
                
                <h4>2. -ja/-jä</h4>
                <p>Forms nouns that refer to the person who performs the action:</p>
                <div class="grammar-example">
                    <p><span class="finnish">opettaa (to teach) → opettaja</span> <span class="english">teacher</span></p>
                    <p><span class="finnish">lukea (to read) → lukija</span> <span class="english">reader</span></p>
                    <p><span class="finnish">myydä (to sell) → myyjä</span> <span class="english">seller</span></p>
                </div>
                
                <h4>3. -ma/-mä</h4>
                <p>Forms nouns that refer to the result of an action:</p>
                <div class="grammar-example">
                    <p><span class="finnish">elää (to live) → elämä</span> <span class="english">life</span></p>
                    <p><span class="finnish">kuolla (to die) → kuolema</span> <span class="english">death</span></p>
                    <p><span class="finnish">sanoa (to say) → sanoma</span> <span class="english">message</span></p>
                </div>
                
                <h4>4. -us/-ys</h4>
                <p>Forms abstract nouns from verbs:</p>
                <div class="grammar-example">
                    <p><span class="finnish">rakentaa (to build) → rakennus</span> <span class="english">building</span></p>
                    <p><span class="finnish">kouluttaa (to educate) → koulutus</span> <span class="english">education</span></p>
                    <p><span class="finnish">kysyä (to ask) → kysymys</span> <span class="english">question</span></p>
                </div>
                
                <h4>5. -in</h4>
                <p>Forms nouns that refer to tools or instruments:</p>
                <div class="grammar-example">
                    <p><span class="finnish">avata (to open) → avain</span> <span class="english">key</span></p>
                    <p><span class="finnish">soittaa (to play) → soitin</span> <span class="english">musical instrument</span></p>
                    <p><span class="finnish">laskea (to calculate) → laskin</span> <span class="english">calculator</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>DERIVING NOUNS FROM ADJECTIVES</h3>
            
            <div class="grammar-content">
                <p>Finnish has several suffixes to derive nouns from adjectives:</p>
                
                <h4>1. -us/-ys</h4>
                <p>Forms abstract nouns that refer to the quality expressed by the adjective:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kaunis (beautiful) → kauneus</span> <span class="english">beauty</span></p>
                    <p><span class="finnish">hyvä (good) → hyvyys</span> <span class="english">goodness</span></p>
                    <p><span class="finnish">paha (bad) → pahuus</span> <span class="english">evil, badness</span></p>
                </div>
                
                <h4>2. -uus/-yys</h4>
                <p>Another suffix for forming abstract nouns from adjectives:</p>
                <div class="grammar-example">
                    <p><span class="finnish">uusi (new) → uutuus</span> <span class="english">novelty</span></p>
                    <p><span class="finnish">vapaa (free) → vapaus</span> <span class="english">freedom</span></p>
                    <p><span class="finnish">terve (healthy) → terveys</span> <span class="english">health</span></p>
                </div>
                
                <h4>3. -kkA</h4>
                <p>Forms nouns that refer to a person or thing with the quality expressed by the adjective:</p>
                <div class="grammar-example">
                    <p><span class="finnish">vanha (old) → vanhukka</span> <span class="english">elderly person</span></p>
                    <p><span class="finnish">kova (hard) → kovakka</span> <span class="english">hard thing</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>DERIVING NOUNS FROM OTHER NOUNS</h3>
            
            <div class="grammar-content">
                <p>Finnish has several suffixes to derive nouns from other nouns:</p>
                
                <h4>1. -lainen/-läinen</h4>
                <p>Forms nouns that refer to a person from a place or group:</p>
                <div class="grammar-example">
                    <p><span class="finnish">Suomi (Finland) → suomalainen</span> <span class="english">Finn, Finnish person</span></p>
                    <p><span class="finnish">kaupunki (city) → kaupunkilainen</span> <span class="english">city dweller</span></p>
                    <p><span class="finnish">yliopisto (university) → yliopistolaine</span> <span class="english">university member</span></p>
                </div>
                
                <h4>2. -sto/-stö</h4>
                <p>Forms collective nouns:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kirja (book) → kirjasto</span> <span class="english">library</span></p>
                    <p><span class="finnish">laiva (ship) → laivasto</span> <span class="english">fleet</span></p>
                    <p><span class="finnish">lehti (leaf, magazine) → lehdistö</span> <span class="english">press, media</span></p>
                </div>
                
                <h4>3. -kko/-kkö</h4>
                <p>Forms collective nouns or groups:</p>
                <div class="grammar-example">
                    <p><span class="finnish">koivu (birch) → koivikko</span> <span class="english">birch grove</span></p>
                    <p><span class="finnish">mänty (pine) → männikkö</span> <span class="english">pine forest</span></p>
                </div>
                
                <h4>4. -la/-lä</h4>
                <p>Forms nouns that refer to places:</p>
                <div class="grammar-example">
                    <p><span class="finnish">kahvi (coffee) → kahvila</span> <span class="english">café</span></p>
                    <p><span class="finnish">ruoka (food) → ruokala</span> <span class="english">canteen</span></p>
                    <p><span class="finnish">sairas (sick) → sairaala</span> <span class="english">hospital</span></p>
                </div>
                
                <h4>5. -nen</h4>
                <p>Forms diminutives or small versions:</p>
                <div class="grammar-example">
                    <p><span class="finnish">lintu (bird) → lintunen</span> <span class="english">little bird</span></p>
                    <p><span class="finnish">kukka (flower) → kukkanen</span> <span class="english">little flower</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON DERIVATIONAL PATTERNS</h3>
            
            <div class="grammar-content">
                <p>Some common patterns in Finnish noun derivation:</p>
                
                <h4>1. Profession names</h4>
                <div class="grammar-example">
                    <p><span class="finnish">opettaa (to teach) → opettaja</span> <span class="english">teacher</span></p>
                    <p><span class="finnish">johtaa (to lead) → johtaja</span> <span class="english">leader, manager</span></p>
                    <p><span class="finnish">tutkia (to research) → tutkija</span> <span class="english">researcher</span></p>
                </div>
                
                <h4>2. Place names</h4>
                <div class="grammar-example">
                    <p><span class="finnish">leipoa (to bake) → leipomo</span> <span class="english">bakery</span></p>
                    <p><span class="finnish">myydä (to sell) → myymälä</span> <span class="english">shop</span></p>
                    <p><span class="finnish">asua (to live) → asunto</span> <span class="english">apartment</span></p>
                </div>
                
                <h4>3. Abstract concepts</h4>
                <div class="grammar-example">
                    <p><span class="finnish">vapaa (free) → vapaus</span> <span class="english">freedom</span></p>
                    <p><span class="finnish">onnellinen (happy) → onnellisuus</span> <span class="english">happiness</span></p>
                    <p><span class="finnish">mahdollinen (possible) → mahdollisuus</span> <span class="english">possibility</span></p>
                </div>
                
                <h4>4. Tools and instruments</h4>
                <div class="grammar-example">
                    <p><span class="finnish">avata (to open) → avain</span> <span class="english">key</span></p>
                    <p><span class="finnish">sulkea (to close) → suljin</span> <span class="english">shutter</span></p>
                    <p><span class="finnish">leikata (to cut) → leikkuri</span> <span class="english">cutter</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>INFLECTING DERIVED NOUNS</h3>
            
            <div class="grammar-content">
                <p>Derived nouns follow the same inflection patterns as other Finnish nouns, based on their ending:</p>
                
                <h4>1. -minen nouns</h4>
                <p>These are type 2 nouns (ending in -nen):</p>
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">lukeminen</span> (reading)</p>
                    <p>Genitive: <span class="finnish">lukemisen</span></p>
                    <p>Partitive: <span class="finnish">lukemista</span></p>
                </div>
                
                <h4>2. -ja/-jä nouns</h4>
                <p>These are type 4 nouns (ending in -a/-ä):</p>
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">opettaja</span> (teacher)</p>
                    <p>Genitive: <span class="finnish">opettajan</span></p>
                    <p>Partitive: <span class="finnish">opettajaa</span></p>
                </div>
                
                <h4>3. -us/-ys nouns</h4>
                <p>These are type 3 nouns (ending in a consonant):</p>
                <div class="grammar-example">
                    <p>Nominative: <span class="finnish">koulutus</span> (education)</p>
                    <p>Genitive: <span class="finnish">koulutuksen</span></p>
                    <p>Partitive: <span class="finnish">koulutusta</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















