$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Where-Object { $_.FullName -ne "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar/index.html" } | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
    $content = Get-Content -Path $file -Raw
    
    # Check if the file already has the Entertainment dropdown
    if ($content -notmatch '<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>') {
        # Find the position to insert the Entertainment dropdown (after the Categories dropdown)
        $pattern = '</li>\s*<li class="highlight-button-container">'
        
        # Calculate the correct relative path to the games.html file
        $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
        $depth = ($filePath.Split("\").Length - 1)
        
        # For files in finnish_grammar folder, we need to go up one level
        # For files in subfolders, we need to go up additional levels
        $prefix = "../" * $depth
        
        $entertainmentDropdown = @"
                </li>
                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="${prefix}games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container">
"@
        
        # Replace the pattern with our new content
        $newContent = $content -replace $pattern, $entertainmentDropdown
        
        # Save the modified content back to the file
        Set-Content -Path $file -Value $newContent
        
        Write-Host "Updated: $file"
    }
    else {
        Write-Host "Already has Entertainment dropdown: $file"
    }
}

Write-Host "All files have been updated!"