﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Basic Word Order in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Basic Word Order</span>
        </div>
        
        <section class="grammar-section">
            <h2>Basic Word Order in Finnish</h2>
            <p>Finnish has a relatively flexible word order, but the basic structure follows the Subject-Verb-Object (SVO) pattern. This page explains the fundamental principles of Finnish word order and how it differs from English.</p>
        </section>

        <section class="grammar-category">
            <h3>THE BASIC SVO STRUCTURE</h3>
            
            <div class="grammar-content">
                <p>The most neutral word order in Finnish is Subject-Verb-Object (SVO), similar to English:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjaa.</span> <span class="english">I am reading a book.</span></p>
                    <p><span class="finnish">Hän syö omenaa.</span> <span class="english">He/she is eating an apple.</span></p>
                    <p><span class="finnish">Lapset pelaavat peliä.</span> <span class="english">The children are playing a game.</span></p>
                </div>
                
                <p>In these examples:</p>
                <ul>
                    <li>Subject (S): minä, hän, lapset</li>
                    <li>Verb (V): luen, syö, pelaavat</li>
                    <li>Object (O): kirjaa, omenaa, peliä</li>
                </ul>
                
                <p>This basic word order is used in neutral, declarative sentences when no particular emphasis is needed.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>FLEXIBILITY IN WORD ORDER</h3>
            
            <div class="grammar-content">
                <p>Unlike English, Finnish uses case endings to mark the grammatical function of words, which allows for more flexibility in word order. The same sentence can be expressed with different word orders, each with slightly different emphasis:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä luen kirjaa.</span> <span class="english">I am reading a book. (neutral)</span></p>
                    <p><span class="finnish">Kirjaa minä luen.</span> <span class="english">It's a book that I'm reading. (emphasis on "book")</span></p>
                    <p><span class="finnish">Luen minä kirjaa.</span> <span class="english">I am indeed reading a book. (emphasis on the action)</span></p>
                </div>
                
                <p>This flexibility is possible because the grammatical roles are indicated by case endings rather than position:</p>
                <ul>
                    <li>The subject is typically in the nominative case (no ending)</li>
                    <li>The object is often in the partitive case (-a/-ä) or accusative case</li>
                </ul>
            </div>
        </section>

        <section class="grammar-category">
            <h3>TOPIC-COMMENT STRUCTURE</h3>
            
            <div class="grammar-content">
                <p>Finnish word order is often described in terms of "topic" and "comment" rather than strictly subject and predicate:</p>
                
                <ul>
                    <li>The <strong>topic</strong> is what the sentence is about (often, but not always, the subject)</li>
                    <li>The <strong>comment</strong> is what is said about the topic</li>
                </ul>
                
                <p>The topic typically comes first in the sentence:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Matti on opettaja.</span> <span class="english">Matti is a teacher. (Topic: Matti)</span></p>
                    <p><span class="finnish">Helsingissä sataa lunta.</span> <span class="english">In Helsinki, it's snowing. (Topic: Helsinki)</span></p>
                    <p><span class="finnish">Tänään minä menen töihin.</span> <span class="english">Today I'm going to work. (Topic: today)</span></p>
                </div>
                
                <p>This topic-comment structure explains why adverbials often appear at the beginning of Finnish sentences when they represent the main topic of discussion.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>EMPHASIS AND FOCUS</h3>
            
            <div class="grammar-content">
                <p>Word order is an important tool for emphasis in Finnish. Generally:</p>
                
                <ul>
                    <li>Words at the beginning of the sentence receive some emphasis</li>
                    <li>Words at the end of the sentence receive the strongest emphasis</li>
                    <li>The middle positions are less emphasized</li>
                </ul>
                
                <div class="grammar-example">
                    <p><span class="finnish">KIRJAA minä luen (en lehteä).</span> <span class="english">It's a BOOK I'm reading (not a magazine).</span></p>
                    <p><span class="finnish">Minä luen KIRJAA.</span> <span class="english">I'm reading a BOOK.</span></p>
                </div>
                
                <p>This principle is used extensively in Finnish to highlight different aspects of the message without needing additional words or structures.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>SUBJECT OMISSION</h3>
            
            <div class="grammar-content">
                <p>In Finnish, personal pronouns as subjects can often be omitted because the verb forms already indicate the person:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Minä puhun suomea. → Puhun suomea.</span> <span class="english">I speak Finnish.</span></p>
                    <p><span class="finnish">Sinä tulet kotiin. → Tulet kotiin.</span> <span class="english">You come home.</span></p>
                </div>
                
                <p>This affects the word order, as the verb may then become the first element in the sentence. This is particularly common in spoken Finnish and informal writing.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>COMMON PATTERNS</h3>
            
            <div class="grammar-content">
                <p>Some common word order patterns in Finnish:</p>
                
                <h4>1. Time expressions often come first</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Huomenna minä menen töihin.</span> <span class="english">Tomorrow I will go to work.</span></p>
                    <p><span class="finnish">Viime viikolla kävin Helsingissä.</span> <span class="english">Last week I visited Helsinki.</span></p>
                </div>
                
                <h4>2. Place expressions typically follow the verb</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Minä asun Helsingissä.</span> <span class="english">I live in Helsinki.</span></p>
                    <p><span class="finnish">Hän kävelee puistossa.</span> <span class="english">He/she walks in the park.</span></p>
                </div>
                
                <h4>3. Manner adverbs usually come before the main verb or at the end</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Hän puhuu hitaasti suomea.</span> <span class="english">He/she speaks Finnish slowly.</span></p>
                    <p><span class="finnish">Hän hitaasti puhuu suomea.</span> <span class="english">He/she slowly speaks Finnish.</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















