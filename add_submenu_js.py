#!/usr/bin/env python3
"""
Script to add submenu JavaScript functionality to all level 3 Finnish grammar files
"""

import os
import re
import glob

# Submenu JavaScript functionality
SUBMENU_JAVASCRIPT = '''
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Close other active submenus
                dropdownSubmenus.forEach(otherSubmenu => {
                    if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                        otherSubmenu.classList.remove('active');
                    }
                });

                // Toggle current submenu
                submenu.classList.toggle('active');
            });
        }
    });

    // Close submenus when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.dropdown-submenu')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });'''

# List of files to update (all level 3 files)
FILES_TO_UPDATE = [
    # Cases/Grammatical (skip partitive.html, nominative.html, genitive.html, accusative.html - already done)
    
    # Cases/Locative
    "finnish_grammar/cases/locative/ablative.html",
    "finnish_grammar/cases/locative/adessive.html", 
    "finnish_grammar/cases/locative/allative.html",
    "finnish_grammar/cases/locative/elative.html",
    "finnish_grammar/cases/locative/illative.html",
    # inessive.html already done
    
    # Cases/Other
    "finnish_grammar/cases/other/abessive.html",
    "finnish_grammar/cases/other/comitative.html",
    # essive.html already done
    "finnish_grammar/cases/other/instructive.html",
    "finnish_grammar/cases/other/translative.html",
    
    # Nouns/Types
    "finnish_grammar/nouns/types/type1.html",
    "finnish_grammar/nouns/types/type2.html",
    "finnish_grammar/nouns/types/type3.html",
    "finnish_grammar/nouns/types/type4.html",
    "finnish_grammar/nouns/types/type5.html",
    
    # Verbs/Types
    "finnish_grammar/verbs/types/type1.html",
    "finnish_grammar/verbs/types/type2.html",
    "finnish_grammar/verbs/types/type3.html",
    "finnish_grammar/verbs/types/type4.html",
    "finnish_grammar/verbs/types/type5.html",
    "finnish_grammar/verbs/types/type6.html"
]

def add_submenu_js(file_path):
    """Add submenu JavaScript functionality to a file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Check if submenu JavaScript is already present
        if 'dropdown-submenu' in content and 'submenu-header' in content:
            print(f"- Already has submenu JS: {file_path}")
            return False
        
        # Find the end of the existing JavaScript and add submenu functionality
        js_end_pattern = r'(\s+}\s*}\);\s*</script>)'
        if re.search(js_end_pattern, content):
            content = re.sub(js_end_pattern, SUBMENU_JAVASCRIPT + r'\1', content)
            
            # Write the updated content
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Added submenu JS: {file_path}")
            return True
        else:
            print(f"✗ Could not find JS pattern in: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error updating {file_path}: {e}")
        return False

def main():
    """Main function to add submenu JS to all files"""
    print("Adding submenu JavaScript to level 3 Finnish grammar files...")
    
    updated_count = 0
    error_count = 0
    
    for file_path in FILES_TO_UPDATE:
        if os.path.exists(file_path):
            if add_submenu_js(file_path):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
            error_count += 1
    
    print(f"\nSubmenu JS addition completed!")
    print(f"Files updated: {updated_count}")
    print(f"Errors: {error_count}")
    
    if error_count == 0:
        print("All level 3 files now have submenu JavaScript functionality!")

if __name__ == "__main__":
    main()
