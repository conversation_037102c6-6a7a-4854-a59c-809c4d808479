$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Where-Object { $_.FullName -ne "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar/index.html" } | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
  
  # Calculate the correct relative path prefix based on file depth
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $folderDepth = ($filePath.Split("\").Length - 1)
  $prefix = "../" * $folderDepth
  
  # Create the complete video channels dropdown content
  $videoChannelsDropdown = @"
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <a href="${prefix}video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="${prefix}video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="${prefix}video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="${prefix}video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="${prefix}video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                        <a href="${prefix}video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                        <a href="${prefix}video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                        <a href="${prefix}video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                        <a href="${prefix}video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                        <a href="${prefix}video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="${prefix}video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>
                    </div>
"@
  
  # Replace the existing video channels dropdown with the complete one
  $pattern = '(?s)(<li class="dropdown">\s*)<a href="javascript:void\(0\)" class="dropbtn">Videos</a>\s*<div class="dropdown-content" id="channels-dropdown">.*?</div>(\s*</li>)'
  $replacement = "`${1}$videoChannelsDropdown`${2}"
  $newContent = $content -replace $pattern, $replacement
  
  # Save the modified content back to the file if changes were made
  if ($newContent -ne $originalContent) {
    Set-Content -Path $file -Value $newContent
    Write-Host "Updated video channels in: $file"
  }
}

Write-Host "All video channels have been updated!"