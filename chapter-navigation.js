// Function to navigate to previous or next chapter
function navigateToChapter(category, currentChapter, direction) {
  console.log(
    `Navigating ${direction} from ${category} chapter ${currentChapter}`
  );

  // Convert to number for arithmetic
  let chapterNum = parseInt(currentChapter);

  // Determine category ranges
  const categoryRanges = {
    "daily-life": { min: 1, max: 30 },
    "web-development": { min: 31, max: 50 },
    "cleaner": { min: 51, max: 60 },
    "kitchen-assistant": { min: 61, max: 70 },
    "warehouse": { min: 71, max: 80 },
  };

  const range = categoryRanges[category];

  if (direction === "prev") {
    chapterNum--;
    // If we go below the minimum, wrap to the previous category
    if (chapterNum < range.min) {
      // Find the previous category
      if (category === "web-development") {
        category = "daily-life";
        chapterNum = categoryRanges["daily-life"].max;
      } else if (category === "cleaner") {
        category = "web-development";
        chapterNum = categoryRanges["web-development"].max;
      } else if (category === "kitchen-assistant") {
        category = "cleaner";
        chapterNum = categoryRanges["cleaner"].max;
      } else if (category === "warehouse") {
        category = "kitchen-assistant";
        chapterNum = categoryRanges["kitchen-assistant"].max;
      } else {
        // If we're already at the first category, stay at the first chapter
        category = "daily-life";
        chapterNum = 1;
      }
    }
  } else if (direction === "next") {
    chapterNum++;
    // If we go above the maximum, wrap to the next category
    if (chapterNum > range.max) {
      // Find the next category
      if (category === "daily-life") {
        category = "web-development";
        chapterNum = categoryRanges["web-development"].min;
      } else if (category === "web-development") {
        category = "cleaner";
        chapterNum = categoryRanges["cleaner"].min;
      } else if (category === "cleaner") {
        category = "kitchen-assistant";
        chapterNum = categoryRanges["kitchen-assistant"].min;
      } else if (category === "kitchen-assistant") {
        category = "warehouse";
        chapterNum = categoryRanges["warehouse"].min;
      } else {
        // If we're already at the last category, stay at the last chapter
        category = "warehouse";
        chapterNum = 80;
      }
    }
  }

  // Log the navigation
  console.log(`Navigating to ${category} chapter ${chapterNum}`);

  // Navigate to the new chapter
  window.location.href = `chapter.html?category=${category}&chapter=${chapterNum}`;
}

// Function to generate table of contents
function generateTableOfContents() {
  const content = document.getElementById("chapter-content");
  const toc = document.getElementById("table-of-contents");

  if (!content || !toc) {
    console.error(
      "Could not find chapter content or table of contents elements"
    );
    return;
  }

  const headings = content.querySelectorAll("h1, h2, h3");

  if (headings.length === 0) {
    toc.innerHTML =
      '<div class="toc-placeholder">No headings found in this chapter</div>';
    return;
  }

  let tocHtml = "";

  headings.forEach((heading) => {
    // If heading doesn't have an ID, generate one
    if (!heading.id) {
      heading.id = heading.textContent.toLowerCase().replace(/[^\w]+/g, "-");
    }

    const id = heading.id;
    const text = heading.textContent;
    const level = heading.tagName.toLowerCase();

    tocHtml += `<a href="#${id}" class="toc-item toc-${level}">${text}</a>`;
  });

  toc.innerHTML = tocHtml;

  // Add click event to TOC items
  const tocItems = toc.querySelectorAll(".toc-item");
  tocItems.forEach((item) => {
    item.addEventListener("click", function (e) {
      e.preventDefault();
      const targetId = this.getAttribute("href").substring(1);
      const targetElement = document.getElementById(targetId);

      if (targetElement) {
        // Smooth scroll to the heading
        targetElement.scrollIntoView({ behavior: "smooth" });

        // Update active state in TOC
        tocItems.forEach((i) => i.classList.remove("active"));
        this.classList.add("active");

        // Hide the dropdown after clicking
        document.getElementById("table-of-contents").classList.remove("show");
      }
    });
  });
}

// Set up navigation and TOC when the DOM is loaded
document.addEventListener("DOMContentLoaded", function () {
  // Get URL parameters
  const params = new URLSearchParams(window.location.search);
  const category = params.get("category");
  const chapterNumber = params.get("chapter");

  if (!category || !chapterNumber) {
    console.error("Missing category or chapter number in URL");
    return;
  }

  console.log(`Setting up navigation for ${category} chapter ${chapterNumber}`);

  // Set up navigation buttons (top and bottom buttons)
  const prevButtons = ["prev-chapter-top", "prev-chapter-bottom"];
  const nextButtons = ["next-chapter-top", "next-chapter-bottom"];

  prevButtons.forEach((buttonId) => {
    const button = document.getElementById(buttonId);
    if (button) {
      console.log("Adding click event to prev button:", buttonId);
      button.addEventListener("click", function () {
        console.log("Prev button clicked:", buttonId);
        navigateToChapter(category, chapterNumber, "prev");
      });
    } else {
      console.warn("Prev button not found:", buttonId);
    }
  });

  nextButtons.forEach((buttonId) => {
    const button = document.getElementById(buttonId);
    if (button) {
      console.log("Adding click event to next button:", buttonId);
      button.addEventListener("click", function () {
        console.log("Next button clicked:", buttonId);
        navigateToChapter(category, chapterNumber, "next");
      });
    } else {
      console.warn("Next button not found:", buttonId);
    }
  });

  // Set up table of contents dropdown
  const tocButton = document.querySelector(".toc-button");
  const tocDropdown = document.getElementById("table-of-contents");

  if (tocButton && tocDropdown) {
    tocButton.addEventListener("click", function (e) {
      e.preventDefault();
      tocDropdown.classList.toggle("show");
    });

    // Close the dropdown when clicking outside
    document.addEventListener("click", function (e) {
      if (
        !e.target.matches(".toc-button") &&
        !e.target.closest("#table-of-contents") &&
        !tocButton.contains(e.target)
      ) {
        tocDropdown.classList.remove("show");
      }
    });
  }

  // Generate table of contents after content is loaded
  // We'll wait a bit to ensure the content is fully loaded
  setTimeout(generateTableOfContents, 1000);
});
