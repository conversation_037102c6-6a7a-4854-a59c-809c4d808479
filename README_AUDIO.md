# Audiolist Feature

The Audiolist feature in Opiskelen Suomea provides text-to-speech functionality for all 80 chapters across 6 categories.

## How It Works

Instead of using pre-recorded audio files, the Audiolist feature uses the Web Speech API to:

1. Fetch chapter content from the website
2. Extract conversation text, vocabulary items, or titles
3. Read the content aloud using the browser's text-to-speech capabilities
4. Auto-replay content until stopped

## Audio Content Structure

The audio content is organized by category:

1. Daily Life (30 chapters)
2. Web Development IT (10 chapters)
3. Cleaner (10 chapters)
4. Kitchen Assistant (10 chapters)
5. Warehouse (10 chapters)
6. Technology (10 chapters)

## Features

- **Play Individual Chapters**: Click the play button next to any chapter to hear its content
- **Play All**: Play all chapters in sequence
- **Shuffle**: Play chapters in random order
- **Auto-Replay**: Each chapter will continue to play in a loop until stopped or until the next chapter starts (in Play All mode)

## Technical Notes

- Uses the Web Speech API (SpeechSynthesis)
- Attempts to use Finnish voices when available
- Falls back to the browser's default voice if no Finnish voice is available
- Extracts content from chapter HTML using regex patterns
- Prioritizes conversation content, then vocabulary items, then titles

## Browser Compatibility

The Web Speech API is supported in most modern browsers:

- Chrome/Edge (best support)
- Firefox
- Safari

For best results, use Chrome or Edge which typically have better voice support.

