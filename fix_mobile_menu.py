#!/usr/bin/env python3
"""
Script to fix mobile menu dropdown functionality in all Finnish grammar files
"""

import os
import re
import glob

# Fixed submenu JavaScript that works properly with mobile menu
FIXED_SUBMENU_JAVASCRIPT = '''
    // Handle nested dropdown menus (submenu functionality)
    const dropdownSubmenus = document.querySelectorAll('.dropdown-submenu');
    dropdownSubmenus.forEach(submenu => {
        const submenuHeader = submenu.querySelector('.submenu-header');
        if (submenuHeader) {
            submenuHeader.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Only handle submenu on desktop or when mobile menu is open
                if (window.innerWidth > 767 || navLinks.classList.contains('show')) {
                    // Close other active submenus
                    dropdownSubmenus.forEach(otherSubmenu => {
                        if (otherSubmenu !== submenu && otherSubmenu.classList.contains('active')) {
                            otherSubmenu.classList.remove('active');
                        }
                    });

                    // Toggle current submenu
                    submenu.classList.toggle('active');
                }
            });
        }
    });

    // Close submenus when clicking outside (but not when mobile menu is involved)
    document.addEventListener('click', function(e) {
        // Don't close submenus if clicking on mobile menu elements
        if (!e.target.closest('.dropdown-submenu') && 
            !e.target.closest('.mobile-menu-toggle') && 
            !e.target.closest('.nav-links')) {
            dropdownSubmenus.forEach(submenu => {
                submenu.classList.remove('active');
            });
        }
    });'''

def fix_mobile_menu_in_file(file_path):
    """Fix mobile menu functionality in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find and replace the problematic submenu JavaScript
        old_submenu_pattern = r'''    // Handle nested dropdown menus \(submenu functionality\)
    const dropdownSubmenus = document\.querySelectorAll\('\.dropdown-submenu'\);
    dropdownSubmenus\.forEach\(submenu => \{
        const submenuHeader = submenu\.querySelector\('\.submenu-header'\);
        if \(submenuHeader\) \{
            submenuHeader\.addEventListener\('click', function\(e\) \{
                e\.preventDefault\(\);
                e\.stopPropagation\(\);

                // Close other active submenus
                dropdownSubmenus\.forEach\(otherSubmenu => \{
                    if \(otherSubmenu !== submenu && otherSubmenu\.classList\.contains\('active'\)\) \{
                        otherSubmenu\.classList\.remove\('active'\);
                    \}
                \}\);

                // Toggle current submenu
                submenu\.classList\.toggle\('active'\);
            \}\);
        \}
    \}\);

    // Close submenus when clicking outside
    document\.addEventListener\('click', function\(e\) \{
        if \(!e\.target\.closest\('\.dropdown-submenu'\)\) \{
            dropdownSubmenus\.forEach\(submenu => \{
                submenu\.classList\.remove\('active'\);
            \}\);
        \}
    \}\);'''
        
        # Replace with fixed version
        content = re.sub(old_submenu_pattern, FIXED_SUBMENU_JAVASCRIPT, content, flags=re.DOTALL)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed mobile menu: {file_path}")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def find_all_html_files():
    """Find all HTML files in finnish_grammar directory"""
    html_files = []
    
    # Find all HTML files recursively
    for root, dirs, files in os.walk('finnish_grammar'):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    return html_files

def main():
    """Main function to fix mobile menu in all files"""
    print("Starting mobile menu fix for all Finnish grammar files...")
    
    html_files = find_all_html_files()
    updated_count = 0
    error_count = 0
    
    for file_path in html_files:
        if fix_mobile_menu_in_file(file_path):
            updated_count += 1
    
    print(f"\nMobile menu fix completed!")
    print(f"Files updated: {updated_count}")
    print(f"Total files checked: {len(html_files)}")
    
    if updated_count > 0:
        print("Mobile menu dropdown functionality has been fixed!")

if __name__ == "__main__":
    main()
