#!/usr/bin/env python3
"""
Simple script to fix mobile menu dropdown functionality
"""

import os
import re

# List of files to fix (excluding partitive.html which is already fixed)
FILES_TO_FIX = [
    # Cases/Grammatical (skip partitive.html - already fixed)
    "finnish_grammar/cases/grammatical/accusative.html",
    "finnish_grammar/cases/grammatical/genitive.html", 
    "finnish_grammar/cases/grammatical/nominative.html",
    
    # Cases/Locative
    "finnish_grammar/cases/locative/ablative.html",
    "finnish_grammar/cases/locative/adessive.html", 
    "finnish_grammar/cases/locative/allative.html",
    "finnish_grammar/cases/locative/elative.html",
    "finnish_grammar/cases/locative/illative.html",
    "finnish_grammar/cases/locative/inessive.html",
    
    # Cases/Other
    "finnish_grammar/cases/other/abessive.html",
    "finnish_grammar/cases/other/comitative.html",
    "finnish_grammar/cases/other/essive.html",
    "finnish_grammar/cases/other/instructive.html",
    "finnish_grammar/cases/other/translative.html",
    
    # Nouns/Types
    "finnish_grammar/nouns/types/type1.html",
    "finnish_grammar/nouns/types/type2.html",
    "finnish_grammar/nouns/types/type3.html",
    "finnish_grammar/nouns/types/type4.html",
    "finnish_grammar/nouns/types/type5.html",
    
    # Verbs/Types
    "finnish_grammar/verbs/types/type1.html",
    "finnish_grammar/verbs/types/type2.html",
    "finnish_grammar/verbs/types/type3.html",
    "finnish_grammar/verbs/types/type4.html",
    "finnish_grammar/verbs/types/type5.html",
    "finnish_grammar/verbs/types/type6.html",
]

def fix_file(file_path):
    """Fix mobile menu in a single file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Find the problematic line and replace it
        old_line = "                // Only handle submenu on desktop or when mobile menu is open"
        if old_line not in content:
            # Look for the original submenu condition
            old_condition = "                // Close other active submenus"
            if old_condition in content:
                # Add the mobile menu check
                content = content.replace(
                    "                e.preventDefault();\n                e.stopPropagation();\n\n                // Close other active submenus",
                    "                e.preventDefault();\n                e.stopPropagation();\n\n                // Only handle submenu on desktop or when mobile menu is open\n                if (window.innerWidth > 767 || navLinks.classList.contains('show')) {\n                    // Close other active submenus"
                )
                
                # Close the if statement
                content = content.replace(
                    "                // Toggle current submenu\n                submenu.classList.toggle('active');",
                    "                    // Toggle current submenu\n                    submenu.classList.toggle('active');\n                }"
                )
                
                # Fix the outside click handler
                content = content.replace(
                    "        if (!e.target.closest('.dropdown-submenu')) {",
                    "        // Don't close submenus if clicking on mobile menu elements\n        if (!e.target.closest('.dropdown-submenu') && \n            !e.target.closest('.mobile-menu-toggle') && \n            !e.target.closest('.nav-links')) {"
                )
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed: {file_path}")
            return True
        else:
            print(f"- Already fixed or no changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function"""
    print("Fixing mobile menu dropdown functionality...")
    
    updated_count = 0
    
    for file_path in FILES_TO_FIX:
        if os.path.exists(file_path):
            if fix_file(file_path):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
    
    print(f"\nFixed {updated_count} files")

if __name__ == "__main__":
    main()
