/* Pagination Styles */
.pagination-container {
  display: flex !important; /* Use !important to override any other display settings */
  justify-content: space-between;
  align-items: center;
  margin: 20px 0;
  padding: 15px;
  background-color: #e9f0f7;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
  border: 1px solid #c0d6e8;
  position: relative;
  z-index: 100; /* Increased z-index to ensure visibility */
  visibility: visible !important; /* Ensure visibility */
}

.page-info {
  font-size: 15px;
  color: #2c3e50;
  font-weight: 600;
  padding: 0 10px;
}

.pagination-controls {
  display: flex;
  gap: 5px;
}

.pagination-button {
  background-color: #fff;
  border: 1px solid #007bff;
  border-radius: 4px;
  padding: 8px 15px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: #007bff;
  font-weight: 500;
  min-width: 40px;
  text-align: center;
}

.pagination-button:hover:not(:disabled) {
  background-color: #007bff;
  color: white;
  border-color: #007bff;
}

.pagination-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pagination-button i {
  font-size: 12px;
}

.no-more-videos {
  text-align: center;
  padding: 10px;
  color: #666;
  font-style: italic;
  margin: 10px 0;
}

.no-more-videos i {
  margin-right: 5px;
  color: #007bff;
}

/* Load More Button (legacy) */
.load-more-button {
  display: block;
  margin: 20px auto;
  padding: 10px 20px;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.load-more-button:hover {
  background-color: #0056b3;
}

.load-more-button i {
  margin-right: 5px;
}

/* Ensure pagination is visible in tab content */
.tab-content .pagination-container {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure pagination is visible in YouTube tabs */
[id$="-tab"] .pagination-container {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Ensure pagination is visible for specific channels */
#finnish-crash-course-tab .pagination-container,
#finnish-to-go-tab .pagination-container,
#suomen-kurssi-tab .pagination-container,
#pagination-finnishcrashcourse,
#pagination-finnishtogo,
#pagination-suomenkurssiyt {
  display: flex !important;
  visibility: visible !important;
  opacity: 1 !important;
  margin: 15px 0 !important;
  z-index: 1000 !important;
  position: relative !important;
}