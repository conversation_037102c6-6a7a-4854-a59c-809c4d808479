// Audiolist functionality for Opiskelen Suomea
(function () {
  // Check if browser supports speech synthesis
  if (!("speechSynthesis" in window)) {
    console.warn(
      "This browser does not support the Web Speech API. Text-to-speech functionality will not work."
    );
    return;
  }

  // Initialize speech synthesis
  const synth = window.speechSynthesis;
  let voices = [];

  // Function to load voices
  function loadVoices() {
    voices = synth.getVoices();
    console.log("Loaded " + voices.length + " voices for audiolist");
  }

  // Load voices initially
  loadVoices();

  // Chrome loads voices asynchronously, so we need this event
  if (synth.onvoiceschanged !== undefined) {
    synth.onvoiceschanged = loadVoices;
  }

  // Play mode constants
  const PLAY_MODE = {
    ONE: "one",
    ALL: "all",
    SHUFFLE: "shuffle",
  };

  // Audio file data structure
  console.log("Defining audioFiles array...");
  const audioFiles = [
    // Daily Life category (30 chapters)
    {
      id: 1,
      name: "chapter01_morning_greetings",
      title: "Morning Greetings",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 1,
    },
    {
      id: 2,
      name: "chapter02_family_friends",
      title: "Family and Friends",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 2,
    },
    {
      id: 3,
      name: "chapter03_home_living",
      title: "Home and Living",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 3,
    },
    {
      id: 4,
      name: "chapter04_daily_shopping",
      title: "Daily Shopping",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 4,
    },
    {
      id: 5,
      name: "chapter05_transport_vehicles",
      title: "Transport and Vehicles",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 5,
    },
    {
      id: 6,
      name: "chapter06_health_wellness",
      title: "Health and Wellness",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 6,
    },
    {
      id: 7,
      name: "chapter07_weather_seasons",
      title: "Weather and Seasons",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 7,
    },
    {
      id: 8,
      name: "chapter08_leisure_hobbies",
      title: "Leisure and Hobbies",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 8,
    },
    {
      id: 9,
      name: "chapter09_nutrition_meals",
      title: "Nutrition and Meals",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 9,
    },
    {
      id: 10,
      name: "chapter10_special_diets",
      title: "Special Diets",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 10,
    },
    {
      id: 11,
      name: "chapter11_clothing_fashion",
      title: "Clothing and Fashion",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 11,
    },
    {
      id: 12,
      name: "chapter12_celebrations_holidays",
      title: "Celebrations and Holidays",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 12,
    },
    {
      id: 13,
      name: "chapter13_banking_finances",
      title: "Banking and Finances",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 13,
    },
    {
      id: 14,
      name: "chapter14_post_services",
      title: "Post and Services",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 14,
    },
    {
      id: 15,
      name: "chapter15_phone_internet",
      title: "Phone and Internet",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 15,
    },
    {
      id: 16,
      name: "chapter16_education_learning",
      title: "Education and Learning",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 16,
    },
    {
      id: 17,
      name: "chapter17_work_employment",
      title: "Work and Employment",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 17,
    },
    {
      id: 18,
      name: "chapter18_travel_tourism",
      title: "Travel and Tourism",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 18,
    },
    {
      id: 19,
      name: "chapter19_nature_environment",
      title: "Nature and Environment",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 19,
    },
    {
      id: 20,
      name: "chapter20_sports_exercise",
      title: "Sports and Exercise",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 20,
    },
    {
      id: 21,
      name: "chapter21_arts_culture",
      title: "Arts and Culture",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 21,
    },
    {
      id: 22,
      name: "chapter22_media_news",
      title: "Media and News",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 22,
    },
    {
      id: 23,
      name: "chapter23_technology_gadgets",
      title: "Technology and Gadgets",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 23,
    },
    {
      id: 24,
      name: "chapter24_emotions_feelings",
      title: "Emotions and Feelings",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 24,
    },
    {
      id: 25,
      name: "chapter25_social_interactions",
      title: "Social Interactions",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 25,
    },
    {
      id: 26,
      name: "chapter26_emergencies_safety",
      title: "Emergencies and Safety",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 26,
    },
    {
      id: 27,
      name: "chapter27_government_services",
      title: "Government Services",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 27,
    },
    {
      id: 28,
      name: "chapter28_immigration_integration",
      title: "Immigration and Integration",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 28,
    },
    {
      id: 29,
      name: "chapter29_finnish_culture",
      title: "Finnish Culture",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 29,
    },
    {
      id: 30,
      name: "chapter30_future_plans",
      title: "Future Plans",
      category: "Daily Life",
      categoryParam: "daily-life",
      chapterNum: 30,
    },

    // Web Development category (10 chapters)
    {
      id: 31,
      name: "chapter31_html_basics",
      title: "HTML Basics",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 31,
    },
    {
      id: 32,
      name: "chapter32_css_styling",
      title: "CSS Styling",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 32,
    },
    {
      id: 33,
      name: "chapter33_responsive_design",
      title: "Responsive Design",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 33,
    },
    {
      id: 34,
      name: "chapter34_javascript_basics",
      title: "JavaScript Basics",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 34,
    },
    {
      id: 35,
      name: "chapter35_dom_manipulation",
      title: "DOM Manipulation",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 35,
    },
    {
      id: 36,
      name: "chapter36_forms_validation",
      title: "Forms Validation",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 36,
    },
    {
      id: 37,
      name: "chapter37_ajax_api_calls",
      title: "AJAX API Calls",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 37,
    },
    {
      id: 38,
      name: "chapter38_databases_sql",
      title: "Databases SQL",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 38,
    },
    {
      id: 39,
      name: "chapter39_php_basics",
      title: "PHP Basics",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 39,
    },
    {
      id: 40,
      name: "chapter40_nodejs_basics",
      title: "Node.js Basics",
      category: "Web Development IT",
      categoryParam: "web-development",
      chapterNum: 40,
    },

    // Cleaner category (10 chapters)
    {
      id: 41,
      name: "chapter51_cleaning_tools",
      title: "Cleaning Tools",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 51,
    },
    {
      id: 42,
      name: "chapter52_cleaning_products",
      title: "Cleaning Products",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 52,
    },
    {
      id: 43,
      name: "chapter53_home_cleaning",
      title: "Home Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 53,
    },
    {
      id: 44,
      name: "chapter54_office_cleaning",
      title: "Office Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 54,
    },
    {
      id: 45,
      name: "chapter55_hotel_cleaning",
      title: "Hotel Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 55,
    },
    {
      id: 46,
      name: "chapter56_industrial_cleaning",
      title: "Industrial Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 56,
    },
    {
      id: 47,
      name: "chapter57_floor_maintenance",
      title: "Floor Maintenance",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 57,
    },
    {
      id: 48,
      name: "chapter58_window_cleaning",
      title: "Window Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 58,
    },
    {
      id: 49,
      name: "chapter59_bathroom_cleaning",
      title: "Bathroom Cleaning",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 59,
    },
    {
      id: 50,
      name: "chapter60_daily_reporting",
      title: "Daily Reporting",
      category: "Cleaner",
      categoryParam: "cleaner",
      chapterNum: 60,
    },

    // Kitchen Assistant category (10 chapters)
    {
      id: 51,
      name: "chapter61_kitchen_basics",
      title: "Kitchen Basics",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 61,
    },
    {
      id: 52,
      name: "chapter62_cooking_methods",
      title: "Cooking Methods",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 62,
    },
    {
      id: 53,
      name: "chapter63_kitchen_utensils_equipment",
      title: "Kitchen Utensils Equipment",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 63,
    },
    {
      id: 54,
      name: "chapter64_food_handling_storage",
      title: "Food Handling Storage",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 64,
    },
    {
      id: 55,
      name: "chapter65_kitchen_cleanliness_hygiene",
      title: "Kitchen Cleanliness Hygiene",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 65,
    },
    {
      id: 56,
      name: "chapter66_special_diets_allergies",
      title: "Special Diets Allergies",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 66,
    },
    {
      id: 57,
      name: "chapter67_customer_service_kitchen",
      title: "Customer Service Kitchen",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 67,
    },
    {
      id: 58,
      name: "chapter68_work_safety_kitchen",
      title: "Work Safety Kitchen",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 68,
    },
    {
      id: 59,
      name: "chapter69_orders_inventory_management",
      title: "Orders Inventory Management",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 69,
    },
    {
      id: 60,
      name: "chapter70_teamwork_communication_kitchen",
      title: "Teamwork Communication Kitchen",
      category: "Kitchen Assistant",
      categoryParam: "kitchen-assistant",
      chapterNum: 70,
    },

    // Warehouse category (10 chapters)
    {
      id: 61,
      name: "chapter71_receiving_inspection",
      title: "Receiving Inspection",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 71,
    },
    {
      id: 62,
      name: "chapter72_storage_techniques",
      title: "Storage Techniques",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 72,
    },
    {
      id: 63,
      name: "chapter73_order_picking",
      title: "Order Picking",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 73,
    },
    {
      id: 64,
      name: "chapter74_packing_shipping",
      title: "Packing Shipping",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 74,
    },
    {
      id: 65,
      name: "chapter75_warehouse_safety",
      title: "Warehouse Safety",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 75,
    },
    {
      id: 66,
      name: "chapter76_forklift_equipment",
      title: "Forklift Equipment",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 76,
    },
    {
      id: 67,
      name: "chapter77_inventory_management",
      title: "Inventory Management",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 77,
    },
    {
      id: 68,
      name: "chapter78_waste_management",
      title: "Waste Management",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 78,
    },
    {
      id: 69,
      name: "chapter79_quality_inspections",
      title: "Quality Inspections",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 79,
    },
    {
      id: 70,
      name: "chapter80_daily_reporting",
      title: "Daily Reporting",
      category: "Warehouse",
      categoryParam: "warehouse",
      chapterNum: 80,
    },

    // Technology category removed as requested
  ];

  // Audio player state
  let currentIndex = 0;
  let isPlaying = false;
  let currentPlayMode = PLAY_MODE.ONE; // Default to play one mode
  let shuffledIndices = [];
  let currentSpeechParts = [];
  let currentPartIndex = 0;
  let isSpeaking = false;
  let intentionalSpeechCancellation = false;

  // Helper functions for backward compatibility
  function isShuffleMode() {
    return currentPlayMode === PLAY_MODE.SHUFFLE;
  }

  function playAllMode() {
    return (
      currentPlayMode === PLAY_MODE.ALL || currentPlayMode === PLAY_MODE.SHUFFLE
    );
  }

  // Subtitle variables
  let subtitleContainer = null;
  let subtitlesEnabled = true; // Default to enabled

  // Initialize the audio player when DOM is loaded
  document.addEventListener("DOMContentLoaded", function () {
    initializeAudioList();
    setupEventListeners();
  });

  // Function to initialize the audio list
  function initializeAudioList() {
    console.log("Initializing audio list...");
    const audioListContainer = document.getElementById("audio-list-container");
    console.log("Audio list container:", audioListContainer);
    if (!audioListContainer) {
      console.error("Audio list container not found!");
      return;
    }
    console.log("Audio files:", audioFiles.length);

    // Create subtitle container with improved styling
    subtitleContainer = document.createElement("div");
    subtitleContainer.className = "subtitle-container";
    subtitleContainer.style.cssText = `
      position: fixed;
      bottom: 50px;
      left: 0;
      right: 0;
      background-color: rgba(0, 0, 0, 0.8);
      color: white;
      padding: 15px 20px;
      text-align: center;
      font-size: 20px;
      font-weight: 500;
      line-height: 1.4;
      z-index: 1000;
      display: none;
      max-width: 80%;
      margin: 0 auto;
      border-radius: 8px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.5);
      border: 1px solid rgba(255, 255, 255, 0.2);
    `;
    document.body.appendChild(subtitleContainer);

    // Populate the audio list with improved styling
    console.log("Creating audio list element...");
    const audioListElement = document.createElement("div");
    audioListElement.className = "audio-list";
    console.log("Audio list element created:", audioListElement);

    audioFiles.forEach((file, index) => {
      const audioItem = document.createElement("div");
      audioItem.className = "audio-item";
      audioItem.dataset.index = index;
      audioItem.dataset.category = file.categoryParam; // Add category as data attribute for styling

      // Create a container for the audio information
      const audioInfo = document.createElement("div");
      audioInfo.className = "audio-info";

      // Create the audio title with improved formatting
      const audioTitle = document.createElement("div");
      audioTitle.className = "audio-title";

      // Format the title with chapter number and category
      const chapterNum = String(file.chapterNum).padStart(2, "0"); // Ensure 2-digit format (01, 02, etc.)
      audioTitle.innerHTML = `<strong>${chapterNum}.</strong> ${file.title} <span class="audio-category">${file.category}</span>`;

      // Create the play button with improved styling
      const playButton = document.createElement("button");
      playButton.className = "audio-play-single";
      playButton.innerHTML = '<i class="fas fa-play"></i>';
      playButton.title = "Play this audio";
      playButton.addEventListener("click", function () {
        playAudio(index);
      });

      // Add elements to the audio item
      audioItem.appendChild(audioTitle);
      audioItem.appendChild(playButton);
      audioListElement.appendChild(audioItem);

      // Add hover effect
      audioItem.addEventListener("mouseenter", function () {
        this.style.borderLeftWidth = "6px";
      });

      audioItem.addEventListener("mouseleave", function () {
        this.style.borderLeftWidth = "4px";
      });
    });

    console.log("Appending audio list element to container...");
    // Clear the container first
    audioListContainer.innerHTML = "";
    audioListContainer.appendChild(audioListElement);
    console.log("Audio list element appended to container");
  }

  // Function to set up event listeners for control buttons
  function setupEventListeners() {
    const playModeButton = document.getElementById("play-mode-button");
    const stopButton = document.getElementById("stop-button");
    const nextButton = document.getElementById("next-button");
    const previousButton = document.getElementById("previous-button");
    const subtitleButton = document.getElementById("subtitle-button");

    // Initialize subtitle button state
    if (subtitleButton) {
      // Use the class styles instead of inline styles
      if (subtitlesEnabled) {
        subtitleButton.classList.add("active");
        subtitleButton.style.backgroundColor = "#4CAF50"; // Green
      } else {
        subtitleButton.style.backgroundColor = "#f44336"; // Red
      }
    }

    // Add event listener for subtitle toggle
    if (subtitleButton) {
      subtitleButton.addEventListener("click", function () {
        subtitlesEnabled = !subtitlesEnabled;

        // Update button appearance
        if (subtitlesEnabled) {
          subtitleButton.classList.add("active");
          subtitleButton.style.backgroundColor = "#4CAF50"; // Green
          // Show current subtitle if speaking
          if (
            isSpeaking &&
            currentSpeechParts &&
            currentSpeechParts[currentPartIndex]
          ) {
            showSubtitle(
              currentSpeechParts[currentPartIndex].text,
              currentSpeechParts[currentPartIndex].speaker
            );
          }
        } else {
          subtitleButton.classList.remove("active");
          subtitleButton.style.backgroundColor = "#f44336"; // Red
          hideSubtitle();
        }
      });
    }

    if (playModeButton) {
      playModeButton.addEventListener("click", function () {
        togglePlayMode();
      });
    }

    if (nextButton) {
      nextButton.addEventListener("click", function () {
        playNextTrack();
      });
    }

    if (previousButton) {
      previousButton.addEventListener("click", function () {
        playPreviousTrack();
      });
    }

    if (stopButton) {
      stopButton.addEventListener("click", function () {
        stopAudio();
      });
    }
  }

  // Function to fetch chapter content
  async function fetchChapterContent(category, chapterNumber) {
    try {
      // Map category parameter to directory name
      const categoryDirs = {
        "daily-life": "Daily_Life",
        "web-development": "Web_Development_IT",
        "cleaner": "Cleaner",
        "kitchen-assistant": "Kitchen_Assistant",
        "warehouse": "Warehouse",
        "technology": "Technology",
      };

      const categoryDir = categoryDirs[category] || category;

      // Format chapter number with leading zeros if needed
      const paddedNumber = chapterNumber.toString().padStart(2, "0");

      // Get the current audio file info
      const currentAudio = audioFiles.find(
        (audio) =>
          audio.categoryParam === category && audio.chapterNum === chapterNumber
      );

      if (!currentAudio) {
        throw new Error("Audio file information not found");
      }

      // Try to fetch the markdown file directly
      const possiblePaths = [];

      // Add specific file name based on the audio name
      if (currentAudio.name) {
        possiblePaths.push(`chapters/${categoryDir}/${currentAudio.name}.md`);
      }

      // Add generic patterns
      possiblePaths.push(
        `chapters/${categoryDir}/chapter${paddedNumber}_${currentAudio.title
          .toLowerCase()
          .replace(/\s+/g, "_")}.md`
      );
      possiblePaths.push(`chapters/${categoryDir}/chapter${paddedNumber}.md`);

      // Try each possible path
      for (const path of possiblePaths) {
        try {
          console.log(`Trying to fetch: ${path}`);
          const response = await fetch(path);
          if (response.ok) {
            const mdContent = await response.text();
            console.log(`Successfully fetched: ${path}`);

            // Convert markdown to HTML-like structure for processing
            return convertMarkdownToHtml(mdContent, currentAudio.title);
          }
        } catch (error) {
          console.log(`Failed to fetch ${path}: ${error.message}`);
        }
      }

      // If that fails, try to get content from the current page
      const chapterContent = document.querySelector(".chapter-content");
      if (chapterContent) {
        return chapterContent.innerHTML;
      }

      // If we still don't have content, create a basic structure with the title
      const title = currentAudio.title;
      const categoryName = currentAudio.category;

      // Create a minimal HTML structure
      return `<h1>${title}</h1>
              <div class="conversation">
                <p><strong>Teacher</strong>: Hyvää päivää! Tervetuloa opiskelemaan ${title.toLowerCase()}.</p>
                <p><strong>Student</strong>: Kiitos! Olen innoissani oppimaan.</p>
                <p><strong>Teacher</strong>: Tämä on ${categoryName} -kurssi.</p>
                <p><strong>Student</strong>: Hyvä, aloitetaan!</p>
              </div>`;
    } catch (error) {
      console.error("Error fetching chapter content:", error);
      return null;
    }
  }

  // Function to convert markdown to HTML-like structure
  function convertMarkdownToHtml(mdContent, title) {
    console.log("Converting markdown to HTML for:", title);
    // Add title if not present
    let htmlContent = `<h1>${title}</h1>\n`;

    // Find conversation section in markdown - first try with the div wrapper
    const conversationMatch = mdContent.match(
      /```mdc?\s*\n<div class="conversation">([\s\S]*?)<\/div>\s*\n```|<div class="conversation">([\s\S]*?)<\/div>/
    );

    if (conversationMatch) {
      console.log("Found conversation section with div wrapper");
      // Use the matched group that contains content
      const conversationContent = conversationMatch[1] || conversationMatch[2];
      htmlContent += `<div class="conversation">${conversationContent}</div>`;
    } else {
      console.log(
        "No div wrapper found, looking for conversation-like content"
      );
      // Look for conversation-like content without the div wrapper
      // This pattern matches markdown format like:
      // **Speaker**: Text
      const paragraphRegex = /\n\*\*([^:]+)\*\*:\s*(.*?)(?=\n\*\*|$)/gs;
      let conversationContent = "";
      let paragraphMatch;
      let foundConversation = false;

      while ((paragraphMatch = paragraphRegex.exec(mdContent)) !== null) {
        foundConversation = true;
        const speaker = paragraphMatch[1].trim();
        const text = paragraphMatch[2].trim();

        console.log(
          `Found conversation line - Speaker: ${speaker}, Text: ${text.substring(
            0,
            30
          )}...`
        );

        if (text) {
          conversationContent += `<p><strong>${speaker}</strong>: ${text}</p>\n`;
        }
      }

      if (foundConversation) {
        console.log("Created conversation content from markdown format");
        htmlContent += `<div class="conversation">${conversationContent}</div>`;
      } else {
        console.log("No conversation found, looking for vocabulary items");
        // If no conversation found, look for vocabulary items
        const vocabSection = mdContent.match(/## Vocabulary[\s\S]*?(?=##|$)/);
        if (vocabSection) {
          console.log("Found vocabulary section");
          htmlContent += '<div class="vocabulary-section">\n';

          // Extract numbered or bulleted vocabulary items
          const vocabItems = vocabSection[0].match(
            /(?:\d+\.|\*)\s+(.*?)(?=\n\d+\.|\n\*|\n\n|$)/g
          );

          if (vocabItems) {
            console.log(`Found ${vocabItems.length} vocabulary items`);
            vocabItems.forEach((item) => {
              // Remove the number/bullet and split by dash or hyphen
              const cleanItem = item.replace(/^\d+\.|\*\s+/, "").trim();
              const parts = cleanItem.split(/\s*[-–]\s*/);

              if (parts.length >= 2) {
                const finnish = parts[0].trim();
                const english = parts[1].trim();
                console.log(`Vocabulary item: ${finnish} - ${english}`);

                htmlContent += `
                  <div class="translation-pair">
                    <span class="finnish-term">${finnish}</span>
                    <span class="english-term">${english}</span>
                  </div>`;
              }
            });
          }

          htmlContent += "</div>";
        } else {
          console.log("No vocabulary section found either");
        }
      }
    }

    return htmlContent;
  }

  // Function to extract speech parts from chapter content
  function extractSpeechParts(content) {
    if (!content) return [];

    const speechParts = [];
    console.log("Extracting speech parts from content...");

    // Extract conversation sections
    const conversationRegex = /<div class="conversation">([\s\S]*?)<\/div>/g;
    let match;
    let foundConversation = false;

    while ((match = conversationRegex.exec(content)) !== null) {
      foundConversation = true;
      const conversationContent = match[1];
      console.log(
        "Found conversation section:",
        conversationContent.substring(0, 100) + "..."
      );

      // Try different regex patterns to extract the conversation parts

      // Pattern 1: <p><strong>Speaker</strong>: Text</p>
      const pattern1 = /<p><strong>(.*?)<\/strong>:\s*(.*?)(?=<\/p>)/gs;
      let m1;
      let foundPattern1 = false;

      while ((m1 = pattern1.exec(conversationContent)) !== null) {
        foundPattern1 = true;
        const speaker = m1[1].trim();
        let text = m1[2].trim();

        // Clean the text
        text = text.replace(/<em>.*?<\/em>/g, "").trim(); // Remove pronunciation guides
        text = text.split(/<br>/)[0].trim(); // Remove content after <br>
        text = text.replace(/<[^>]*>/g, "").trim(); // Remove any remaining HTML tags

        if (text) {
          console.log(
            `Pattern 1 - Adding speech part for ${speaker}: ${text.substring(
              0,
              30
            )}...`
          );
          speechParts.push({
            speaker: speaker,
            text: text,
            lang: "fi-FI",
          });
        }
      }

      // If pattern 1 didn't find anything, try pattern 2
      if (!foundPattern1) {
        // Pattern 2: <p class="speaker">Speaker:</p><p>Text</p>
        const pattern2 = /<p class="speaker">(.*?):<\/p>\s*<p>(.*?)<\/p>/g;
        let m2;

        while ((m2 = pattern2.exec(conversationContent)) !== null) {
          const speaker = m2[1].trim();
          let text = m2[2].trim();
          text = text.replace(/<[^>]*>/g, "").trim(); // Remove any HTML tags

          if (text) {
            console.log(
              `Pattern 2 - Adding speech part for ${speaker}: ${text.substring(
                0,
                30
              )}...`
            );
            speechParts.push({
              speaker: speaker,
              text: text,
              lang: "fi-FI",
            });
          }
        }
      }
    }

    // If no conversation was found using the div wrapper, try to find markdown-style conversation
    if (!foundConversation) {
      console.log(
        "No conversation div found, trying to find markdown-style conversation"
      );

      // Look for patterns like "**Speaker**: Text" in the content
      const markdownPattern = /\*\*(.*?)\*\*:\s*(.*?)(?=\n\*\*|$)/gs;
      let m3;

      while ((m3 = markdownPattern.exec(content)) !== null) {
        const speaker = m3[1].trim();
        let text = m3[2].trim();

        if (text) {
          console.log(
            `Markdown pattern - Adding speech part for ${speaker}: ${text.substring(
              0,
              30
            )}...`
          );
          speechParts.push({
            speaker: speaker,
            text: text,
            lang: "fi-FI",
          });
        }
      }
    }

    // If no conversation sections were found, try to extract any paragraphs
    if (speechParts.length === 0) {
      console.log(
        "No conversation sections found, trying to extract paragraphs..."
      );
      // Extract all paragraphs that might contain Finnish text
      const paragraphRegex = /<p>(.*?)<\/p>/g;
      let paragraphMatch;

      while ((paragraphMatch = paragraphRegex.exec(content)) !== null) {
        const text = paragraphMatch[1]
          .trim()
          .replace(/<.*?>/g, "") // Remove any HTML tags
          .trim();

        if (text && text.length > 5 && !text.startsWith("http")) {
          console.log(
            `Adding paragraph as speech part: ${text.substring(0, 30)}...`
          );
          speechParts.push({
            text: text,
            lang: "fi-FI",
          });
        }
      }
    }

    // If still no content, try to extract vocabulary items
    if (speechParts.length === 0) {
      console.log("No paragraphs found, trying to extract vocabulary items...");

      // First try to find vocabulary section in the content
      const vocabSection = content.match(/## Vocabulary[\s\S]*?(?=##|$)/i);

      if (vocabSection) {
        console.log("Found vocabulary section in markdown");
        const vocabContent = vocabSection[0];

        // Try to extract numbered vocabulary items (e.g., "1. Hyvää huomenta - Good morning")
        const vocabItemRegex = /\d+\.\s+(.*?)\s+-\s+(.*?)(?=\n\d+\.|\n\n|$)/g;
        let vocabItemMatch;
        let foundVocabItems = false;

        while ((vocabItemMatch = vocabItemRegex.exec(vocabContent)) !== null) {
          foundVocabItems = true;
          const finnish = vocabItemMatch[1].trim();
          const english = vocabItemMatch[2].trim();

          if (finnish && english) {
            console.log(
              `Adding numbered vocabulary item: ${finnish} - ${english}`
            );
            speechParts.push({
              text: `${finnish} - ${english}`,
              lang: "fi-FI",
            });
          }
        }

        // If no numbered items found, try other formats
        if (!foundVocabItems) {
          // Try HTML format with translation-pair
          const vocabRegex1 =
            /<div class="translation-pair".*?><span class="finnish-term">(.*?)<\/span><span class="english-term">(.*?)<\/span><\/div>/g;
          let vocabMatch;

          while ((vocabMatch = vocabRegex1.exec(content)) !== null) {
            const finnish = vocabMatch[1].trim();
            const english = vocabMatch[2].trim();

            console.log(`Adding translation pair: ${finnish} - ${english}`);
            speechParts.push({
              text: `${finnish} - ${english}`,
              lang: "fi-FI",
            });
          }
        }
      } else {
        // Try HTML formats directly
        console.log("No vocabulary section found, trying HTML formats");

        // Format 1: translation-pair with finnish-term and english-term
        const vocabRegex1 =
          /<div class="translation-pair".*?><span class="finnish-term">(.*?)<\/span><span class="english-term">(.*?)<\/span><\/div>/g;
        let vocabMatch;

        while ((vocabMatch = vocabRegex1.exec(content)) !== null) {
          const finnish = vocabMatch[1].trim();
          const english = vocabMatch[2].trim();

          console.log(`Adding translation pair: ${finnish} - ${english}`);
          speechParts.push({
            text: `${finnish} - ${english}`,
            lang: "fi-FI",
          });
        }

        // Format 2: vocabulary-item with term and definition
        const vocabRegex2 =
          /<div class="vocabulary-item".*?><span class="term">(.*?)<\/span><span class="definition">(.*?)<\/span><\/div>/g;

        while ((vocabMatch = vocabRegex2.exec(content)) !== null) {
          const term = vocabMatch[1].trim();
          const definition = vocabMatch[2].trim();

          console.log(`Adding vocabulary item: ${term} - ${definition}`);
          speechParts.push({
            text: `${term} - ${definition}`,
            lang: "fi-FI",
          });
        }
      }
    }

    // If still no content, extract any list items
    if (speechParts.length === 0) {
      const listItemRegex = /<li>(.*?)<\/li>/g;
      let listMatch;

      while ((listMatch = listItemRegex.exec(content)) !== null) {
        const text = listMatch[1]
          .trim()
          .replace(/<.*?>/g, "") // Remove any HTML tags
          .trim();

        if (text && text.length > 3) {
          speechParts.push({
            text: text,
            lang: "fi-FI",
          });
        }
      }
    }

    // If still no content, extract the title
    if (speechParts.length === 0) {
      const titleRegex = /<h1.*?>(.*?)<\/h1>/;
      const titleMatch = titleRegex.exec(content);

      if (titleMatch) {
        speechParts.push({
          text: titleMatch[1].trim(),
          lang: "fi-FI",
        });
      }
    }

    // If still no content, extract any headings
    if (speechParts.length === 0) {
      const headingRegex = /<h[2-6].*?>(.*?)<\/h[2-6]>/g;
      let headingMatch;

      while ((headingMatch = headingRegex.exec(content)) !== null) {
        const text = headingMatch[1]
          .trim()
          .replace(/<.*?>/g, "") // Remove any HTML tags
          .trim();

        if (text) {
          speechParts.push({
            text: text,
            lang: "fi-FI",
          });
        }
      }
    }

    // Last resort: use any text content
    if (speechParts.length === 0) {
      // Create a temporary div to parse the HTML and extract text
      const tempDiv = document.createElement("div");
      tempDiv.innerHTML = content;
      const text = tempDiv.textContent.trim();

      if (text) {
        // Split into sentences or chunks
        const chunks = text
          .split(/[.!?]/)
          .filter((chunk) => chunk.trim().length > 10);

        if (chunks.length > 0) {
          chunks.forEach((chunk) => {
            speechParts.push({
              text: chunk.trim(),
              lang: "fi-FI",
            });
          });
        } else {
          // If no good chunks, just use the whole text
          speechParts.push({
            text: text.substring(0, Math.min(text.length, 200)), // Limit length
            lang: "fi-FI",
          });
        }
      }
    }

    // Log the final result
    console.log(`Extracted ${speechParts.length} speech parts`);
    if (speechParts.length > 0) {
      console.log("First speech part:", speechParts[0]);
      if (speechParts.length > 1) {
        console.log("Last speech part:", speechParts[speechParts.length - 1]);
      }
    }

    return speechParts;
  }

  // Function to play a specific audio file
  async function playAudio(index) {
    // If already playing, stop current audio
    if (isPlaying) {
      console.log("Stopping current audio before playing new one");

      // Set intentional cancellation flag to true before stopping speech
      intentionalSpeechCancellation = true;
      stopCurrentSpeech();
      resetPlayButtons();

      // Add a small delay to ensure the speech synthesis has fully stopped
      await new Promise((resolve) => {
        setTimeout(() => {
          intentionalSpeechCancellation = false;
          resolve();
        }, 300);
      });
    }

    console.log("Starting to play audio index:", index);
    currentIndex = index;
    const currentAudio = audioFiles[index];

    console.log(
      "Playing audio:",
      currentAudio.title,
      "Category:",
      currentAudio.category
    );

    try {
      // Show loading state
      updatePlayingState(true);

      // Special handling for chapter 1 of Daily Life
      if (
        currentAudio.categoryParam === "daily-life" &&
        currentAudio.chapterNum === 1
      ) {
        console.log("Using hardcoded conversation for Morning Greetings");

        // Create speech parts directly for this chapter
        currentSpeechParts = [
          { speaker: "Anna", text: "Hyvää huomenta!", lang: "fi-FI" },
          {
            speaker: "Mikko",
            text: "Hyvää huomenta! Mitä kuuluu?",
            lang: "fi-FI",
          },
          {
            speaker: "Anna",
            text: "Kiitos, hyvää. Entä sinulle?",
            lang: "fi-FI",
          },
          { speaker: "Mikko", text: "Myös hyvää, kiitos.", lang: "fi-FI" },
          {
            speaker: "Anna",
            text: "Anteeksi, mikä sinun nimesi on?",
            lang: "fi-FI",
          },
          {
            speaker: "Mikko",
            text: "Minun nimeni on Mikko. Entä sinun?",
            lang: "fi-FI",
          },
          {
            speaker: "Anna",
            text: "Minun nimeni on Anna. Hauska tavata!",
            lang: "fi-FI",
          },
          {
            speaker: "Mikko",
            text: "Hauska tavata, Anna! Oletko suomalainen?",
            lang: "fi-FI",
          },
          {
            speaker: "Anna",
            text: "En ole. Minä olen ulkomaalainen, mutta opiskelen suomea.",
            lang: "fi-FI",
          },
          { speaker: "Mikko", text: "Sinä puhut hyvin suomea!", lang: "fi-FI" },
          {
            speaker: "Anna",
            text: "Kiitos paljon! Juotko kahvia aamuisin?",
            lang: "fi-FI",
          },
          {
            speaker: "Mikko",
            text: "Kyllä, juon aina kahvia aamuisin. Entä sinä?",
            lang: "fi-FI",
          },
          {
            speaker: "Anna",
            text: "Minäkin juon kahvia. Pidän myös teestä.",
            lang: "fi-FI",
          },
          {
            speaker: "Mikko",
            text: "Syötkö aamiaista kahvilassa usein?",
            lang: "fi-FI",
          },
          {
            speaker: "Anna",
            text: "Joskus. Tänään minulla on kiire töihin.",
            lang: "fi-FI",
          },
          {
            speaker: "Mikko",
            text: "Ymmärrän. Minunkin täytyy mennä pian.",
            lang: "fi-FI",
          },
          { speaker: "Anna", text: "Nähdään myöhemmin!", lang: "fi-FI" },
          {
            speaker: "Mikko",
            text: "Kyllä, nähdään! Hyvää päivää!",
            lang: "fi-FI",
          },
          { speaker: "Anna", text: "Hyvää päivää! Huomiseen!", lang: "fi-FI" },
          { speaker: "Mikko", text: "Huomiseen!", lang: "fi-FI" },
        ];

        // Start speaking
        currentPartIndex = 0;
        isPlaying = true;
        speakNextPart();
        return;
      }

      // For other chapters, fetch content normally
      const content = await fetchChapterContent(
        currentAudio.categoryParam,
        currentAudio.chapterNum
      );

      if (!content) {
        throw new Error("Could not fetch chapter content");
      }

      console.log("Fetched content:", content);
      console.log("Content type:", typeof content);
      console.log("Content length:", content.length);

      // Extract speech parts
      currentSpeechParts = extractSpeechParts(content);
      console.log("Extracted speech parts:", currentSpeechParts);

      if (currentSpeechParts.length === 0) {
        console.log(
          "No speech parts found, creating a default one with the title"
        );
        // Create a default speech part with the title
        currentSpeechParts = [
          {
            speaker: "Narrator",
            text: `${currentAudio.title}. This chapter's audio content is being prepared.`,
            lang: "en-US",
          },
        ];
      }

      // Start speaking
      currentPartIndex = 0;
      isPlaying = true;
      speakNextPart();
    } catch (error) {
      console.error("Error playing audio:", error);

      // Show a more user-friendly error message
      let errorMessage = `Could not play audio for ${currentAudio.title}.`;

      if (error.message.includes("No speech content found")) {
        errorMessage = `No speech content found for "${currentAudio.title}". The chapter content may not be available yet.`;
      } else if (error.message.includes("Could not fetch chapter content")) {
        errorMessage = `Could not access the chapter "${currentAudio.title}". The chapter may not exist yet.`;
      }

      alert(errorMessage);
      isPlaying = false;
      resetPlayButtons();

      // If in play all mode, move to next audio
      if (playAllMode) {
        playNextAudio();
      }
    }
  }

  // Function to speak the next part
  function speakNextPart() {
    console.log("Speaking next part, index:", currentPartIndex);

    // If not playing or intentionally cancelled, stop
    if (!isPlaying) {
      console.log("Not playing, stopping speech");
      return;
    }

    if (intentionalSpeechCancellation) {
      console.log("Speech was intentionally cancelled, stopping");
      return;
    }

    // If we've spoken all parts, move to next audio or start over
    if (!currentSpeechParts || currentSpeechParts.length === 0) {
      console.log("No speech parts available");
      return;
    }

    if (currentPartIndex >= currentSpeechParts.length) {
      console.log("Reached end of speech parts");
      if (
        currentPlayMode === PLAY_MODE.ALL ||
        currentPlayMode === PLAY_MODE.SHUFFLE
      ) {
        console.log(`In ${currentPlayMode} mode, moving to next audio`);
        playNextAudio();
      } else {
        // In ONE mode, replay the current audio from the beginning
        console.log("Repeat ONE mode is active, replaying the current audio");
        // Reset the part index to start from the beginning
        currentPartIndex = 0;
        // Add a small delay before replaying
        setTimeout(() => {
          speakNextPart();
        }, 1000);
      }
      return;
    }

    // If already speaking, wait and try again
    if (isSpeaking) {
      console.log("Already speaking, waiting before trying again");
      setTimeout(speakNextPart, 200);
      return;
    }

    // Check if speech synthesis is still speaking from a previous utterance
    if (synth.speaking) {
      console.log(
        "Speech synthesis is still speaking, cancelling previous speech"
      );
      synth.cancel();
      setTimeout(speakNextPart, 200);
      return;
    }

    try {
      // Get the current part to speak
      const part = currentSpeechParts[currentPartIndex];

      // Create a new utterance
      const utterance = new SpeechSynthesisUtterance(part.text);

      // Set language to Finnish
      utterance.lang = part.lang || "fi-FI";

      // Find voices for the selected language
      const langVoices = voices.filter((voice) =>
        voice.lang.startsWith(utterance.lang.split("-")[0])
      );

      // Determine which speaker this is (for pitch adjustment)
      const speakerIndex = currentSpeechParts.findIndex(
        (p) => p.speaker === part.speaker
      );

      // Set voice if available
      if (langVoices.length > 0) {
        utterance.voice = langVoices[0];
      }

      // Set speech parameters
      utterance.rate = 0.9; // Slightly slower than normal

      // Alternate pitch based on speaker
      utterance.pitch = speakerIndex % 2 === 0 ? 1.2 : 0.8;

      // Set flag to indicate we're speaking
      isSpeaking = true;
      console.log(
        `Speaking part ${currentPartIndex + 1}/${
          currentSpeechParts.length
        }: ${part.text.substring(0, 30)}...`
      );

      // When speech ends, move to next part
      utterance.onend = function () {
        console.log(`Speech ended for part ${currentPartIndex + 1}`);
        isSpeaking = false;

        // Only proceed if we're still in play mode and not cancelled
        if (isPlaying && !intentionalSpeechCancellation) {
          currentPartIndex++;

          // Hide subtitle briefly between parts
          hideSubtitle();

          // Small delay between parts
          setTimeout(speakNextPart, 200);
        } else {
          console.log(
            "Not proceeding to next part because playback was stopped or cancelled"
          );
        }
      };

      // Handle errors
      utterance.onerror = function (event) {
        console.log("Speech synthesis error:", event);

        // If we're in the process of intentionally stopping speech,
        // just ignore the error completely
        if (intentionalSpeechCancellation) {
          console.log(
            "Ignoring error because intentionalSpeechCancellation is true"
          );
          return;
        }

        // Set speaking flag to false
        isSpeaking = false;

        // Hide subtitle on error
        hideSubtitle();

        // Check if this is an interrupted error (which happens when speech is cancelled)
        if (event.error === "interrupted") {
          console.log(
            "Speech was interrupted - this is expected when changing modes"
          );

          // If we're still in play mode, the calling function will handle restarting
          if (isPlaying && playAllMode) {
            console.log(
              "Still in play mode, the calling function will handle restarting playback"
            );
          } else {
            console.log("Not in play mode anymore, stopping completely");
            // Make sure we're fully stopped
            isPlaying = false;
            updatePlayingState();
          }
          return;
        } else {
          // This is a real error, not just an interruption
          console.error("Real speech synthesis error:", event);

          // Show error message for actual errors, but only on first error
          if (currentPartIndex === 0) {
            console.error("First part error:", event);
            alert(
              "There was an error with the text-to-speech. This might be due to browser limitations or missing voice support for Finnish language."
            );
          }

          // Only proceed if we're still in play mode
          if (isPlaying && playAllMode) {
            // Try to continue with next part
            console.log("Trying to continue with next part despite error");
            currentPartIndex++;
            setTimeout(speakNextPart, 200);
          } else {
            // Make sure we're fully stopped
            isPlaying = false;
            updatePlayingState();
          }
        }
      };

      // Show subtitle
      showSubtitle(part.text, part.speaker);

      // Start speaking
      synth.speak(utterance);
    } catch (error) {
      console.error("Error in speech synthesis:", error);
      isSpeaking = false;

      // Only continue to next part if this wasn't an intentional cancellation
      if (!intentionalSpeechCancellation) {
        currentPartIndex++;
        setTimeout(speakNextPart, 100);
      }
    }
  }

  // Function to show subtitle
  function showSubtitle(text, speaker) {
    if (!subtitleContainer || !subtitlesEnabled) return;

    // Clean up the text first - remove HTML tags and trim
    let cleanText = text.replace(/<[^>]*>/g, "").trim();

    // Remove any pronunciation guides in parentheses
    cleanText = cleanText.replace(/\([^)]*\)/g, "").trim();

    // Remove any pronunciation guides that might be after <br> tags
    cleanText = cleanText.split(/\s*<br>|\s*\n/)[0].trim();

    // Format the subtitle text
    let subtitleText = cleanText;
    if (speaker) {
      subtitleText = `<strong>${speaker}:</strong> ${cleanText}`;
    }

    // Update and show the subtitle with improved styling
    subtitleContainer.innerHTML = subtitleText;
    subtitleContainer.style.display = "block";
    subtitleContainer.style.backgroundColor = "rgba(0, 0, 0, 0.7)";
    subtitleContainer.style.color = "white";
    subtitleContainer.style.padding = "10px 20px";
    subtitleContainer.style.borderRadius = "5px";
    subtitleContainer.style.maxWidth = "80%";
    subtitleContainer.style.margin = "0 auto";
    subtitleContainer.style.textAlign = "center";
    subtitleContainer.style.fontSize = "18px";
    subtitleContainer.style.fontWeight = "normal";
    subtitleContainer.style.boxShadow = "0 2px 10px rgba(0, 0, 0, 0.3)";
    subtitleContainer.style.zIndex = "1000";

    // Log the subtitle for debugging
    console.log("Showing subtitle:", subtitleText);
  }

  // Function to hide subtitle
  function hideSubtitle() {
    if (!subtitleContainer) return;
    subtitleContainer.style.display = "none";
  }

  // Function to stop current speech
  function stopCurrentSpeech() {
    console.log("Stopping current speech");

    // We don't need to set the intentional cancellation flag here anymore
    // as it should be set by the calling function before calling this function

    // But we'll check if it's set for logging purposes
    if (intentionalSpeechCancellation) {
      console.log(
        "Intentional cancellation flag is set - this is an expected interruption"
      );
    } else {
      console.log(
        "Warning: Stopping speech without setting intentionalSpeechCancellation flag"
      );
      // Set it anyway to be safe
      intentionalSpeechCancellation = true;

      // And schedule it to be reset
      setTimeout(() => {
        console.log(
          "Resetting intentional cancellation flag (set by stopCurrentSpeech)"
        );
        intentionalSpeechCancellation = false;
      }, 500); // Longer timeout to be safe
    }

    // Cancel any ongoing speech
    if (synth.speaking) {
      console.log("Speech synthesis is speaking, cancelling...");
      synth.cancel();
    } else {
      console.log("Speech synthesis was not speaking");
    }

    // Reset state flags
    isSpeaking = false;
    isPlaying = false;
    currentPartIndex = 0;
    currentSpeechParts = [];

    // Hide subtitle when speech stops
    hideSubtitle();

    console.log("Speech stopped successfully");
  }

  // Function to play the next audio in the list
  function playNextAudio() {
    // Only proceed if in ALL or SHUFFLE mode
    if (currentPlayMode === PLAY_MODE.ONE) return;

    if (currentPlayMode === PLAY_MODE.SHUFFLE) {
      // In shuffle mode, get the next index from shuffled array
      const currentShuffleIndex = shuffledIndices.indexOf(currentIndex);
      if (currentShuffleIndex < shuffledIndices.length - 1) {
        playAudio(shuffledIndices[currentShuffleIndex + 1]);
      } else {
        // Reshuffle and start again if we reached the end
        shuffleIndices();
        playAudio(shuffledIndices[0]);
      }
    } else {
      // In sequential mode (ALL), just play the next index
      if (currentIndex < audioFiles.length - 1) {
        playAudio(currentIndex + 1);
      } else {
        // Start from beginning if we reached the end
        playAudio(0);
      }
    }
  }

  // Function to play the next track (used by Next button)
  function playNextTrack() {
    // Set intentional cancellation flag to true before stopping speech
    intentionalSpeechCancellation = true;
    stopCurrentSpeech();

    // Reset the flag after a short delay
    setTimeout(() => {
      intentionalSpeechCancellation = false;

      // Play the next track
      if (currentPlayMode === PLAY_MODE.SHUFFLE) {
        // In shuffle mode, get the next index from shuffled array
        const currentShuffleIndex = shuffledIndices.indexOf(currentIndex);
        if (currentShuffleIndex < shuffledIndices.length - 1) {
          playAudio(shuffledIndices[currentShuffleIndex + 1]);
        } else {
          // Reshuffle and start again if we reached the end
          shuffleIndices();
          playAudio(shuffledIndices[0]);
        }
      } else {
        // In sequential mode (ONE or ALL), just play the next index
        if (currentIndex < audioFiles.length - 1) {
          playAudio(currentIndex + 1);
        } else {
          // Start from beginning if we reached the end
          playAudio(0);
        }
      }
    }, 300);
  }

  // Function to play the previous track (used by Previous button)
  function playPreviousTrack() {
    // Set intentional cancellation flag to true before stopping speech
    intentionalSpeechCancellation = true;
    stopCurrentSpeech();

    // Reset the flag after a short delay
    setTimeout(() => {
      intentionalSpeechCancellation = false;

      // Play the previous track
      if (currentPlayMode === PLAY_MODE.SHUFFLE) {
        // In shuffle mode, get the previous index from shuffled array
        const currentShuffleIndex = shuffledIndices.indexOf(currentIndex);
        if (currentShuffleIndex > 0) {
          playAudio(shuffledIndices[currentShuffleIndex - 1]);
        } else {
          // Go to the last track if we're at the beginning
          playAudio(shuffledIndices[shuffledIndices.length - 1]);
        }
      } else {
        // In sequential mode (ONE or ALL), just play the previous index
        if (currentIndex > 0) {
          playAudio(currentIndex - 1);
        } else {
          // Go to the last track if we're at the beginning
          playAudio(audioFiles.length - 1);
        }
      }
    }, 300);
  }

  // Function to stop audio playback
  function stopAudio() {
    console.log("Stopping all audio playback");

    // Set intentional cancellation flag to true before stopping speech
    intentionalSpeechCancellation = true;
    stopCurrentSpeech();

    // Reset the flag after a short delay
    setTimeout(() => {
      console.log("Resetting intentional cancellation flag after Stop button");
      intentionalSpeechCancellation = false;

      isPlaying = false;
      playAllMode = false;
      updatePlayingState();
      resetPlayButtons();
    }, 300);

    // Reset play all and shuffle buttons immediately for UI feedback
    const playAllButton = document.getElementById("play-all-button");
    const shuffleButton = document.getElementById("shuffle-button");

    if (playAllButton) {
      playAllButton.classList.remove("active");
      playAllButton.style.backgroundColor = ""; // Reset to default
      playAllButton.innerHTML = '<i class="fas fa-play-circle"></i> Play All';
    }

    if (shuffleButton) {
      shuffleButton.classList.remove("active");
      shuffleButton.style.backgroundColor = ""; // Reset to default
    }

    // Hide subtitle
    hideSubtitle();

    console.log("All audio playback stopped");
  }

  // Function to toggle play mode (cycles through ONE -> ALL -> SHUFFLE)
  function togglePlayMode() {
    const playModeButton = document.getElementById("play-mode-button");

    // Cycle through the modes
    switch (currentPlayMode) {
      case PLAY_MODE.ONE:
        // Change from ONE to ALL
        currentPlayMode = PLAY_MODE.ALL;
        console.log("Play mode changed to: REPEAT ALL");

        // Update button appearance
        if (playModeButton) {
          playModeButton.classList.remove("mode-shuffle");
          playModeButton.classList.add("mode-all", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-sync"></i> <span class="button-text">Repeat All</span>';
        }

        // Start playing from the beginning if not already playing
        if (!isPlaying) {
          playAudio(0);
        }
        break;

      case PLAY_MODE.ALL:
        // Change from ALL to SHUFFLE
        currentPlayMode = PLAY_MODE.SHUFFLE;
        console.log("Play mode changed to: REPEAT SHUFFLE");

        // Shuffle the indices
        shuffleIndices();

        // Update button appearance
        if (playModeButton) {
          playModeButton.classList.remove("mode-all");
          playModeButton.classList.add("mode-shuffle", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-random"></i> <span class="button-text">Repeat Shuffle</span>';
        }

        // If already playing, restart with shuffled order
        if (isPlaying) {
          console.log("Restarting playback with shuffled order");
          // Set intentional cancellation flag to true before stopping speech
          intentionalSpeechCancellation = true;
          stopCurrentSpeech();

          // Reset the flag after a short delay
          setTimeout(() => {
            intentionalSpeechCancellation = false;
            playAudio(shuffledIndices[0]);
          }, 300);
        } else {
          // Start playing from the first shuffled index
          playAudio(shuffledIndices[0]);
        }
        break;

      case PLAY_MODE.SHUFFLE:
        // Change from SHUFFLE back to ONE
        currentPlayMode = PLAY_MODE.ONE;
        console.log("Play mode changed to: REPEAT ONE");

        // Update button appearance
        if (playModeButton) {
          playModeButton.classList.remove("mode-shuffle", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-sync-alt"></i> <span class="button-text">Repeat One</span>';
        }

        // When switching to ONE mode, we don't stop the current audio
        // Just update the button appearance and ensure playback doesn't continue to next track

        // Make sure intentionalSpeechCancellation flag is not set
        // This ensures that any ongoing speech continues without interruption
        intentionalSpeechCancellation = false;

        console.log(
          "Repeat One mode enabled, current audio will continue playing in a loop"
        );
        break;
    }

    updatePlayingState();
  }

  // Function to shuffle the indices array
  function shuffleIndices() {
    shuffledIndices = Array.from({ length: audioFiles.length }, (_, i) => i);

    // Fisher-Yates shuffle algorithm
    for (let i = shuffledIndices.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffledIndices[i], shuffledIndices[j]] = [
        shuffledIndices[j],
        shuffledIndices[i],
      ];
    }
  }

  // Function to update the playing state UI
  function updatePlayingState(isLoading) {
    const audioItems = document.querySelectorAll(".audio-item");
    let currentPlayingItem = null;

    audioItems.forEach((item, index) => {
      const playButton = item.querySelector(".audio-play-single");

      if (
        parseInt(item.dataset.index) === currentIndex &&
        (isPlaying || isLoading)
      ) {
        item.classList.add("playing");
        currentPlayingItem = item;
        if (playButton) {
          playButton.innerHTML = isLoading
            ? '<i class="fas fa-spinner fa-spin"></i>'
            : '<i class="fas fa-pause"></i>';
        }
      } else {
        item.classList.remove("playing");
        if (playButton) {
          playButton.innerHTML = '<i class="fas fa-play"></i>';
        }
      }
    });

    // Scroll to the currently playing item with a smooth animation
    if (currentPlayingItem) {
      // Get the container
      const container = document.querySelector(".audio-list-container");
      if (container) {
        // Calculate the scroll position
        const itemTop = currentPlayingItem.offsetTop;
        const containerHeight = container.clientHeight;
        const itemHeight = currentPlayingItem.clientHeight;

        // Scroll to position the item in the middle of the container
        const scrollTo = itemTop - containerHeight + itemHeight * 6;

        // Smooth scroll to the item
        container.scrollTo({
          top: Math.max(0, scrollTo),
          behavior: "smooth",
        });
      }
    }
  }

  // Function to reset all play buttons
  function resetPlayButtons() {
    console.log("Resetting all play buttons");
    const audioItems = document.querySelectorAll(".audio-item");

    audioItems.forEach((item) => {
      item.classList.remove("playing");
      const playButton = item.querySelector(".audio-play-single");
      if (playButton) {
        playButton.innerHTML = '<i class="fas fa-play"></i>';
      }
    });

    // Also reset play mode button if it exists
    const playModeButton = document.getElementById("play-mode-button");
    if (playModeButton) {
      // Update button appearance based on current mode
      switch (currentPlayMode) {
        case PLAY_MODE.ONE:
          playModeButton.classList.remove("mode-all", "mode-shuffle", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-sync-alt"></i> <span class="button-text">Repeat One</span>';
          break;
        case PLAY_MODE.ALL:
          playModeButton.classList.remove("mode-shuffle");
          playModeButton.classList.add("mode-all", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-sync"></i> <span class="button-text">Repeat All</span>';
          break;
        case PLAY_MODE.SHUFFLE:
          playModeButton.classList.remove("mode-all");
          playModeButton.classList.add("mode-shuffle", "active");
          playModeButton.innerHTML =
            '<i class="fas fa-random"></i> <span class="button-text">Repeat Shuffle</span>';
          break;
      }
    }

    // Hide subtitle
    hideSubtitle();
  }

  // Expose the initialization function to the global scope
  window.initializeAudioList = initializeAudioList;
})();

