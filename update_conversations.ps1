$chapterFolders = @(
  "Daily_Life",
  "Web_Development_IT",
  "Kitchen_Assistant",
  "Warehouse",
  "Cleaner"
)

$rootPath = "e:/Finland Tu/Opiskelen_Suomea/chapters"

foreach ($folder in $chapterFolders) {
  $folderPath = Join-Path -Path $rootPath -ChildPath $folder
  $files = Get-ChildItem -Path $folderPath -Filter "*.md"
    
  foreach ($file in $files) {
    $content = Get-Content -Path $file.FullName -Raw
        
    # Check if the file contains a conversation scene
    if ($content -match "### Scene:") {
      Write-Host "Processing $($file.Name)..."
            
      # Find the scene section
      $scenePattern = "### Scene:.*?(?=### |$)"
      $sceneMatches = [regex]::Matches($content, $scenePattern, [System.Text.RegularExpressions.RegexOptions]::Singleline)
            
      foreach ($sceneMatch in $sceneMatches) {
        $originalScene = $sceneMatch.Value
        $sceneTitle = [regex]::Match($originalScene, "### Scene:.*").Value
        $conversationText = $originalScene.Substring($sceneTitle.Length)
                
        # Check if the scene is already in the new format
        if ($conversationText -match "<div class=`"conversation`">") {
          Write-Host "  Scene already in new format, checking for formatting issues..."
          
          # Fix the issue where Cultural Notes section is not properly separated
          if ($content -match "</div>### Cultural Notes:") {
            $content = $content -replace "</div>### Cultural Notes:", "</div>`n`n### Cultural Notes:"
            Write-Host "  Fixed formatting issue with Cultural Notes section"
          }
          continue
        }
                
        # Format the conversation
        $newConversation = "$sceneTitle`n`n<div class=`"conversation`">"
                
        # Split by speaker lines (lines starting with **)
        $speakerLines = [regex]::Matches($conversationText, "\r?\n\*\*[^:]+\*\*:.*?(?=\r?\n\*\*|$)", [System.Text.RegularExpressions.RegexOptions]::Singleline)
                
        foreach ($speakerLine in $speakerLines) {
          $line = $speakerLine.Value.Trim()
                    
          # Extract speaker and text
          if ($line -match "^\*\*([^:]+)\*\*: (.*)$") {
            $speaker = $matches[1]
            $text = $matches[2]
                        
            # Check if there's a pronunciation guide in parentheses
            if ($text -match "(.*?)(\([^)]+\))$") {
              $dialogue = $matches[1].Trim()
              $pronunciation = $matches[2]
                            
              # Format with HTML
              $formattedLine = "<p><strong>$speaker</strong>: $dialogue<br>`n<em>$pronunciation</em></p>`n"
            }
            else {
              # No pronunciation guide
              $formattedLine = "<p><strong>$speaker</strong>: $text</p>`n"
            }
                        
            $newConversation += "`n$formattedLine"
          }
        }
                
        $newConversation += "</div>"
                
        # Replace code blocks to remove ```html and ``` markers
        $newConversation = $newConversation -replace "```html\r?\n", ""
        $newConversation = $newConversation -replace "```\r?\n", ""
                
        # Replace the original scene with the new formatted scene
        $content = $content -replace [regex]::Escape($originalScene), $newConversation
      }
      
      # Fix the issue where Cultural Notes section is not properly separated
      if ($content -match "</div>### Cultural Notes:") {
        $content = $content -replace "</div>### Cultural Notes:", "</div>`n`n### Cultural Notes:"
        Write-Host "  Fixed formatting issue with Cultural Notes section"
      }
            
      # Save the updated content back to the file
      Set-Content -Path $file.FullName -Value $content
      Write-Host "  Updated $($file.Name)"
    }
  }
}

Write-Host "All chapters have been updated!"