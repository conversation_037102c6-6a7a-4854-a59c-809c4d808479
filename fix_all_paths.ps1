$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
  
  # Calculate the correct relative path to the games.html file
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $folderDepth = ($filePath.Split("\").Length - 1)
  $relativePath = "../" * $folderDepth + "games.html"
  
  # Replace the path with the correct one using regex with capture groups
  $newContent = $content -replace '(<li class="dropdown">\s*<a href="javascript:void\(0\)" class="dropbtn">Entertainment</a>\s*<div class="dropdown-content">\s*<a href=")[^"]*(">Games</a>)', "`$1$relativePath`$2"
  
  # Save the modified content back to the file if changes were made
  if ($newContent -ne $originalContent) {
    Set-Content -Path $file -Value $newContent
    Write-Host "Fixed path in: $file to $relativePath"
  }
}

Write-Host "All paths have been fixed!"