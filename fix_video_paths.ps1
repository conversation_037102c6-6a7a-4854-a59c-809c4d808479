$files = Get-ChildItem -Path "e:/Finland Tu/Opiskelen_Suomea/finnish_grammar" -Filter "*.html" -Recurse | Select-Object -ExpandProperty FullName

foreach ($file in $files) {
  $content = Get-Content -Path $file -Raw
  $originalContent = $content
  
  # Calculate the correct relative path prefix based on file depth
  $filePath = $file.Replace("e:/Finland Tu/Opiskelen_Suomea/", "")
  $folderDepth = ($filePath.Split("\").Length - 1)
  $prefix = "../" * $folderDepth
  
  # Check if the file has the video channels dropdown with incorrect paths
  if ($content -match '<a href="[.]{3,}/video.html\?channel=') {
    # Replace all video channel links with the correct paths
    $newContent = $content -replace '(<a href=")[.]{3,}(/video.html\?channel=[^"]+)', "`${1}$prefix`${2}"
    
    # Save the modified content back to the file if changes were made
    if ($newContent -ne $originalContent) {
      Set-Content -Path $file -Value $newContent
      Write-Host "Fixed video paths in: $file"
    }
  }
}

Write-Host "All video paths have been fixed!"