# Chapter 44: Versionhallinta / Version Control

## Objectives / Tavoitteet
- Learn vocabulary related to version control systems in Finnish
- Understand how to discuss Git and other version control tools
- Be able to explain basic version control operations and workflows
- Master basic conversations about tracking changes in code

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Versionhallinta - Version control
2. Tietovarasto - Repository
3. Haara - Branch
4. Yhdistäminen - Merging
5. Kommitti - Commit
6. K<PERSON>onaus - Cloning
7. Työkopio - Working copy
8. Konflikti - Conflict
9. Etävarasto - Remote repository
10. Vetopyyntö - Pull request
11. Työntö - Push
12. Veto - Pull
13. Muutoshistoria - Change history
14. Haaroittaminen - Branching
15. Tagi - Tag

## Grammar Points / Kielioppi
1. **Technical Verbs for Version Control Operations**:
   - Action verbs for version control activities
   - Example: Kommitoin muutokset tietovarastoon. (I commit the changes to the repository.)

2. **Conditional Forms for Version Control Scenarios**:
   - Expressing version control conditions
   - Example: <PERSON><PERSON>, rat<PERSON><PERSON><PERSON><PERSON> ne manuaalisesti. (If conflicts would appear, we would resolve them manually.)

3. **Inessive Case (-ssa/-ssä) for Version Control Contexts**:
   - In version control environments
   - Example: Versionhallinnassa näkyvät kaikki muutokset. (All changes are visible in version control.)

4. **Elative Case (-sta/-stä) for Version Control Sources**:
   - From version control sources
   - Example: Haen uusimmat muutokset etävarastosta. (I fetch the latest changes from the remote repository.)

5. **Illative Case (-Vn) for Version Control Destinations**:
   - Into version control destinations
   - Example: Työnnän muutokset päähaaraan. (I push the changes into the main branch.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Version control workshop / Versionhallinnan työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa versionhallinnan työpajaan! Tänään opimme, miten versionhallintaa käytetään ohjelmistokehityksessä.<br>
<em>(ter-ve-tu-lo-a ver-si-on-hal-lin-nan työ-pa-jaan! tä-nään o-pim-me, mi-ten ver-si-on-hal-lin-taa käy-te-tään oh-jel-mis-to-ke-hi-tyk-ses-sä.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen kuullut Gitistä, mutta en ole varma, miten sitä käytetään tehokkaasti.<br>
<em>(kii-tos! o-len kuul-lut gi-tis-tä, mut-ta en o-le var-ma, mi-ten si-tä käy-te-tään te-hok-kaas-ti.)</em></p>

<p><strong>Ohjaaja</strong>: Git on tosiaan nykyään suosituin versionhallintajärjestelmä. Aloitetaan perusteista: versionhallinta on järjestelmä, joka seuraa muutoksia tiedostoissa ajan myötä, jotta voit palata tiettyihin versioihin myöhemmin.<br>
<em>(git on to-si-aan ny-ky-ään suo-si-tuin ver-si-on-hal-lin-ta-jär-jes-tel-mä. a-loi-te-taan pe-rus-teis-ta: ver-si-on-hal-lin-ta on jär-jes-tel-mä, jo-ka seu-raa muu-tok-si-a tie-dos-tois-sa a-jan my-ö-tä, jot-ta voit pa-la-ta tiet-tyi-hin ver-si-oi-hin my-ö-hem-min.)</em></p>

<p><strong>Osallistuja</strong>: Miksi versionhallinta on tärkeää?<br>
<em>(mik-si ver-si-on-hal-lin-ta on tär-ke-ää?)</em></p>

<p><strong>Osallistuja</strong>: Miten Git-tietovarasto luodaan?<br>
<em>(mi-ten git-tie-to-va-ras-to luo-daan?)</em></p>

<p><strong>Osallistuja</strong>: Miten teen muutoksia ja tallennan ne Git-tietovarastoon?<br>
<em>(mi-ten teen muu-tok-si-a ja tal-len-nan ne git-tie-to-va-ras-toon?)</em></p>

<p><strong>Osallistuja</strong>: Miten haaroja käytetään Gitissä?<br>
<em>(mi-ten haa-ro-ja käy-te-tään gi-tis-sä?)</em></p>

<p><strong>Osallistuja</strong>: Miten konflikteja käsitellään?<br>
<em>(mi-ten kon-flik-te-ja kä-si-tel-lään?)</em></p>

<p><strong>Osallistuja</strong>: Miten vetopyynnöt (pull request) toimivat? (mi-ten ve-to-pyyn-nöt (pull re-quest) toi-mi-vat?)</p>

<p><strong>Osallistuja</strong>: Mitkä ovat yleisimmät Git-työnkulut tiimeille?<br>
<em>(mit-kä o-vat y-lei-sim-mät git-ty-ön-ku-lut tii-meil-le?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla Gitin käyttöä projektissani näiden ohjeiden avulla?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la gi-tin käyt-tö-ä pro-jek-tis-sa-ni näi-den oh-jei-den a-vul-la?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita asentamalla Git, jos sitä ei vielä ole, ja luo ensimmäinen tietovarastosi. Muista, että Git on taito, joka kehittyy käytön myötä. Aloita perusasioista - tee kommitteja säännöllisesti ja käytä kuvaavia viestejä. Kun tunnet olosi mukavaksi perustoimintojen kanssa, voit siirtyä haaroihin ja monimutkaisempiin työnkulkuihin. Gitissä on myös monia hyödyllisiä komentoja, joita emme ehtineet käsitellä, kuten `git log`, `git stash` ja `git rebase`. Tutustu niihin, kun olet omaksunut perusteet.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta a-sen-ta-mal-la git, jos si-tä ei vie-lä o-le, ja luo en-sim-mäi-nen tie-to-va-ras-to-si. muis-ta, et-tä git on tai-to, jo-ka ke-hit-tyy käy-tön my-ö-tä. a-loi-ta pe-rus-a-si-ois-ta - tee kom-mit-te-ja sään-nöl-li-ses-ti ja käy-tä ku-vaa-vi-a vies-te-jä. kun tun-net o-lo-si mu-ka-vak-si pe-rus-toi-min-to-jen kans-sa, voit siir-ty-ä haa-roi-hin ja mo-ni-mut-kai-sem-piin ty-ön-kul-kui-hin. gi-tis-sä on my-ös mo-ni-a hyö-dyl-li-si-ä ko-men-to-ja, joi-ta em-me eh-ti-neet kä-si-tel-lä, ku-ten `git log`, `git stash` ja `git re-ba-se`. tu-tus-tu nii-hin, kun o-let o-mak-su-nut pe-rus-teet.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten versionhallintaa käytetään ohjelmistokehityksessä.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten ver-si-on-hal-lin-taa käy-te-tään oh-jel-mis-to-ke-hi-tyk-ses-sä.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong tech culture where version control is considered an essential part of professional software development
- Finnish developers typically value well-organized repositories with clear commit messages and logical branching
- Many Finnish tech companies use Git with collaborative workflows that emphasize code quality and review
- Open source contribution is valued in the Finnish developer community, where Git skills are highly regarded
- Finnish educational institutions increasingly include version control as a fundamental skill in programming courses


