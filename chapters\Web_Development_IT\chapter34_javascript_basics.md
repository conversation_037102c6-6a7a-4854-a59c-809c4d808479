# Chapter 34: JavaScript-perusteet / JavaScript Basics

## Objectives / Tavoitteet
- Learn vocabulary related to JavaScript programming in Finnish
- Understand how to discuss basic JavaScript concepts and syntax
- Be able to explain simple JavaScript functions and operations
- Master basic conversations about adding interactivity to websites

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Muuttuja - Variable
2. Funktio - Function
3. Ehto - Condition
4. Silmukka - Loop
5. Tapahtuma - Event
6. Kuuntelija - Listener
7. Objekti - Object
8. Taulukko - Array
9. Merkkijono - String
10. Numero - Number
11. Totuusarvo - Boolean
12. Lauseke - Expression
13. Operaattori - Operator
14. Kommentti - Comment
15. Virheenkäsittely - Error handling

## Grammar Points / Kielioppi
1. **Imperative Forms for Programming Instructions**:
   - Commands in coding
   - Example: Määrittele muuttuja ja anna sille arvo. (Define a variable and give it a value.)

2. **Conditional Forms for Programming Logic**:
   - Expressing conditions
   - Example: <PERSON><PERSON> <PERSON><PERSON><PERSON> k<PERSON> painike<PERSON>, funktio suoritettaisiin. (If the user clicks the button, the function would be executed.)

3. **Inessive Case (-ssa/-ssä) for Programming Contexts**:
   - In code structures
   - Example: Silmukassa toistetaan samaa koodia useita kertoja. (In a loop, the same code is repeated multiple times.)

4. **Translative Case (-ksi) for Transformations**:
   - Converting data types
   - Example: Muunna merkkijono numeroksi. (Convert a string to a number.)

5. **Elative Case (-sta/-stä) for Programming Sources**:
   - From something
   - Example: Tiedot haetaan tietokannasta. (The data is fetched from the database.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: JavaScript programming lesson / JavaScript-ohjelmoinnin oppitunti

<div class="conversation">
<p><strong>Opettaja</strong>: Tervetuloa JavaScript-perusteiden kurssille! Tänään opimme, miten JavaScript tuo interaktiivisuutta verkkosivuille.<br>
<em>(ter-ve-tu-lo-a ja-va-script-pe-rus-tei-den kurs-sil-le! tä-nään o-pim-me, mi-ten ja-va-script tuo in-ter-ak-tii-vi-suut-ta verk-ko-si-vuil-le.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos! Olen jo oppinut HTML:ää ja CSS:ää, mutta JavaScript on minulle uutta.<br>
<em>(kii-tos! o-len jo op-pi-nut HTML:ää ja CSS:ää, mut-ta ja-va-script on mi-nul-le uut-ta.)</em></p>

<p><strong>Opettaja</strong>: JavaScript on ohjelmointikieli, joka mahdollistaa dynaamisen sisällön ja käyttäjän vuorovaikutuksen verkkosivuilla. Aloitetaan perusteista: miten JavaScript lisätään verkkosivulle.<br>
<em>(ja-va-script on oh-jel-moin-ti-kie-li, jo-ka mah-dol-lis-taa dy-naa-mi-sen si-säl-lön ja käyt-tä-jän vuo-ro-vai-ku-tuk-sen verk-ko-si-vuil-la. a-loi-te-taan pe-rus-teis-ta: mi-ten ja-va-script li-sä-tään verk-ko-si-vul-le.)</em></p>

<p><strong>Opiskelija</strong>: Miten se tehdään?<br>
<em>(mi-ten se teh-dään?)</em></p>

<p><strong>Opiskelija</strong>: Ymmärrän. Mitä ovat muuttujat JavaScriptissä?<br>
<em>(ym-mär-rän. mi-tä o-vat muut-tu-jat ja-va-scrip-tis-sä?)</em></p>

<p><strong>Opiskelija</strong>: Entä funktiot? Miten ne toimivat?<br>
<em>(en-tä funk-ti-ot? mi-ten ne toi-mi-vat?)</em></p>

<p><strong>Opiskelija</strong>: Miten voin reagoida käyttäjän toimintoihin, kuten klikkauksiin?<br>
<em>(mi-ten voin re-a-goi-da käyt-tä-jän toi-min-toi-hin, ku-ten klik-ka-uk-siin?)</em></p>

<p><strong>Opiskelija</strong>: Entä ehtolauseet? Miten voin suorittaa koodia vain tietyissä tilanteissa?<br>
<em>(en-tä eh-to-lau-seet? mi-ten voin suo-rit-taa koo-di-a vain tie-tyis-sä ti-lan-teis-sa?)</em></p>

<p><strong>Opiskelija</strong>: Miten silmukat toimivat JavaScriptissä?<br>
<em>(mi-ten sil-mu-kat toi-mi-vat ja-va-scrip-tis-sä?)</em></p>

<p><strong>Opiskelija</strong>: Entä taulukot? Miten niitä käytetään?<br>
<em>(en-tä tau-lu-kot? mi-ten nii-tä käy-te-tään?)</em></p>

<p><strong>Opiskelija</strong>: Miten voin muokata HTML-elementtejä JavaScriptillä?<br>
<em>(mi-ten voin muo-ka-ta HTML-e-le-ment-te-jä ja-va-scrip-til-lä?)</em></p>

<p><strong>Opiskelija</strong>: Tämä on todella mielenkiintoista! Voinko nyt kokeilla tehdä yksinkertaisen JavaScript-ohjelman?<br>
<em>(tä-mä on to-del-la mie-len-kiin-tois-ta! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen ja-va-script-oh-jel-man?)</em></p>

<p><strong>Opettaja</strong>: Ehdottomasti! Aloitetaan yksinkertaisella ohjelmalla, joka reagoi käyttäjän toimintaan. Tee HTML-sivu, jossa on painike, ja lisää JavaScript, joka muuttaa tekstiä, kun painiketta klikataan.<br>
<em>(eh-dot-to-mas-ti! a-loi-te-taan yk-sin-ker-tai-sel-la oh-jel-mal-la, jo-ka re-a-goi käyt-tä-jän toi-min-taan. tee HTML-si-vu, jos-sa on pai-ni-ke, ja li-sää ja-va-script, jo-ka muut-taa teks-ti-ä, kun pai-ni-ket-ta kli-ka-taan.)</em></p>

<p><strong>Opiskelija</strong>: Kiitos paljon tästä oppitunnista! Nyt ymmärrän paremmin, miten JavaScript toimii ja miten voin tehdä verkkosivuistani interaktiivisempia.<br>
<em>(kii-tos pal-jon täs-tä op-pi-tun-nis-ta! nyt ym-mär-rän pa-rem-min, mi-ten ja-va-script toi-mii ja mi-ten voin teh-dä verk-ko-si-vuis-ta-ni in-ter-ak-tii-vi-sem-pi-a.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong programming education system, with coding often taught in schools from an early age
- Finnish tech companies value JavaScript skills, as the country has a vibrant web development industry
- Finnish programmers often emphasize clean, efficient code, reflecting the national value of practicality
- Many Finnish tech startups use JavaScript frameworks like React and Angular
- Finland hosts various coding events and hackathons where JavaScript is commonly used


