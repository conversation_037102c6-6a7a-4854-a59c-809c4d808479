﻿<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Negative Questions in Finnish - Finnish Grammar - Opiskelen Suomea</title>
    <link rel="stylesheet" href="../../../styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&family=Roboto+Slab:wght@400;700&display=swap">
    <style>
        .grammar-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .grammar-section {
            margin-bottom: 30px;
        }
        
        .grammar-section h2 {
            color: #0066cc;
            border-bottom: 2px solid #0066cc;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        
        .grammar-section h3 {
            color: #333;
            margin-top: 20px;
            margin-bottom: 15px;
        }
        
        .grammar-list {
            list-style-type: none;
            padding-left: 0;
        }
        
        .grammar-list li {
            margin-bottom: 20px;
            padding-left: 0;
            position: relative;
        }
        
        .grammar-category {
            margin-bottom: 40px;
        }
        
        .grammar-category h3 {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 5px;
        }
        
        .attribution {
            margin-top: 50px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            font-size: 0.9em;
            color: #666;
        }
        
        .grammar-content {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            border-left: 3px solid #0066cc;
        }
        
        .grammar-content p {
            margin-top: 0;
        }
        
        .grammar-content ul {
            padding-left: 20px;
        }
        
        .grammar-content li {
            margin-bottom: 5px;
            padding-left: 0;
        }
        
        .grammar-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .grammar-table th, .grammar-table td {
            border: 1px solid #ddd;
            padding: 10px;
            text-align: left;
        }
        
        .grammar-table th {
            background-color: #f2f2f2;
            font-weight: 500;
        }
        
        .grammar-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        
        .grammar-example {
            margin: 15px 0;
            padding: 10px;
            background-color: #f0f7ff;
            border-radius: 5px;
        }
        
        .grammar-example .finnish {
            font-weight: 500;
            color: #0066cc;
        }
        
        .grammar-example .english {
            color: #666;
            font-style: italic;
        }
        
        .breadcrumbs {
            margin-bottom: 20px;
            font-size: 0.9em;
        }
        
        .breadcrumbs a {
            color: #0066cc;
            text-decoration: none;
        }
        
        .breadcrumbs a:hover {
            text-decoration: underline;
        }
        
        .breadcrumbs .separator {
            margin: 0 5px;
            color: #999;
        }
        
        /* Dark mode adjustments */
        [data-theme="dark"] .grammar-content {
            background-color: #2a2a2a;
            border-left: 3px solid #0066cc;
        }
        
        [data-theme="dark"] .grammar-category h3 {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table th {
            background-color: #333;
        }
        
        [data-theme="dark"] .grammar-table td {
            border-color: #444;
        }
        
        [data-theme="dark"] .grammar-table tr:nth-child(even) {
            background-color: #2a2a2a;
        }
        
        [data-theme="dark"] .grammar-example {
            background-color: #1a3050;
        }
        
        [data-theme="dark"] .grammar-example .english {
            color: #bbb;
        }
    </style>
</head>
<body>
    <nav>
        <div class="container nav-container">
            <div class="logo">
                <a href="../../../index.html">Opiskelen Suomea</a>
            </div>
            <div class="theme-toggle-container mobile-only-theme-toggle">
                <button class="theme-toggle-button" id="grammar-toggle-dark-mobile"><i class="fas fa-moon"></i></button>
            </div>
            <button class="mobile-menu-toggle" id="mobile-menu-toggle">
                <i class="fas fa-bars"></i>
            </button>
            <ul class="nav-links" id="nav-links">
                <li><a href="../../../index.html">Home</a></li>
                <li><a href="../../../audio.html">Audio</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Videos</a>
                    <div class="dropdown-content" id="channels-dropdown">
                        <!-- Individual Channels -->
                        <a href="../../../../video.html?channel=kuulostaahyvalta" data-channel-key="kuulostaahyvalta">Kuulostaa Hyvältä</a>
                        <a href="../../../../video.html?channel=finnishcrashcourse" data-channel-key="finnishcrashcourse">Finnish Crash Course</a>
                        <a href="../../../../video.html?channel=finnishtogo" data-channel-key="finnishtogo">Finnish To Go</a>
                        <a href="../../../../video.html?channel=suomenkurssiyt" data-channel-key="suomenkurssiyt">Suomen Kurssi</a>
                        <a href="../../../../video.html?channel=pipsapossu" data-channel-key="pipsapossu">Pipsa Possu</a>
                        <a href="../../../../video.html?channel=katchatsfinnish" data-channel-key="katchatsfinnish">KatChats Finnish</a>

                        <!-- Yle Areena Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Areena</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=yleareena" data-channel-key="yleareena">Yle Areena 1</a>
                                <a href="../../../../video.html?channel=yleareena2" data-channel-key="yleareena2">Yle Areena 2</a>
                                <a href="../../../../video.html?channel=yleareena3" data-channel-key="yleareena3">Yle Areena 3</a>
                                <a href="../../../../video.html?channel=yleareena4" data-channel-key="yleareena4">Yle Areena 4</a>
                                <a href="../../../../video.html?channel=yleareena5" data-channel-key="yleareena5">Yle Areena 5</a>
                            </div>
                        </div>

                        <!-- Kaapo - WildBrain Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Kaapo - WildBrain</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=kaapowildbrain" data-channel-key="kaapowildbrain">Kaapo - WildBrain 1</a>
                                <a href="../../../../video.html?channel=kaapowildbrain2" data-channel-key="kaapowildbrain2">Kaapo - WildBrain 2</a>
                                <a href="../../../../video.html?channel=kaapowildbrain3" data-channel-key="kaapowildbrain3">Kaapo - WildBrain 3</a>
                                <a href="../../../../video.html?channel=kaapowildbrain4" data-channel-key="kaapowildbrain4">Kaapo - WildBrain 4</a>
                            </div>
                        </div>

                        <!-- Yle Pikku Kakkonen Group -->
                        <div class="dropdown-submenu">
                            <a href="javascript:void(0)" class="submenu-header">Yle Pikku Kakkonen</a>
                            <div class="dropdown-content">
                                <a href="../../../../video.html?channel=ylepikkukakkonen" data-channel-key="ylepikkukakkonen">Yle Pikku Kakkonen 1</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen2" data-channel-key="ylepikkukakkonen2">Yle Pikku Kakkonen 2</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen3" data-channel-key="ylepikkukakkonen3">Yle Pikku Kakkonen 3</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen4" data-channel-key="ylepikkukakkonen4">Yle Pikku Kakkonen 4</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen5" data-channel-key="ylepikkukakkonen5">Yle Pikku Kakkonen 5</a>
                                <a href="../../../../video.html?channel=ylepikkukakkonen6" data-channel-key="ylepikkukakkonen6">Yle Pikku Kakkonen 6</a>
                            </div>
                        </div>
                    </div>
                </li>
                <li><a href="../../index.html" class="active">Grammar</a></li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Categories</a>
                    <div class="dropdown-content">
                        <a href="../../../../index.html#daily-life">Daily Life</a>
                        <a href="../../../../index.html#web-development">Web Development</a>
                        <a href="../../../../index.html#cleaner">Cleaner</a>
                        <a href="../../../../index.html#kitchen-assistant">Kitchen Assistant</a>
                        <a href="../../../../index.html#warehouse">Warehouse</a>
                    </div>
                </li>
                                <li class="dropdown">
                    <a href="javascript:void(0)" class="dropbtn">Entertainment</a>
                    <div class="dropdown-content">
                        <a href="../../../../games.html">Games</a>
                    </div>
                </li>
                <li class="highlight-button-container"><button class="compact-nav-button" id="grammar-toggle-highlight" title="Enable text highlighting"><i class="fas fa-highlighter"></i></button></li>
                <li class="theme-toggle-container">
                    <button class="theme-toggle-button" id="grammar-toggle-dark"><i class="fas fa-moon"></i></button>
                </li>
            </ul>
        </div>
    </nav>

    <div class="grammar-container">
        <div class="breadcrumbs">
            <a href="../../../index.html">Home</a>
            <span class="separator">></span>
            <a href="../../index.html">Finnish Grammar</a>
            <span class="separator">></span>
            <a href="../index.html">Sentence Structure</a>
            <span class="separator">></span>
            <span>Negative Questions</span>
        </div>
        
        <section class="grammar-section">
            <h2>Negative Questions in Finnish</h2>
            <p>Negative questions in Finnish combine the negative verb "ei" with question structures. They often express surprise, disbelief, or expectation of a positive answer. This page explains how to form and use negative questions in Finnish.</p>
        </section>

        <section class="grammar-category">
            <h3>FORMING NEGATIVE YES/NO QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>To form a negative yes/no question in Finnish, you need to:</p>
                
                <ol>
                    <li>Take the appropriate form of the negative verb "ei" (en, et, ei, emme, ette, eivät)</li>
                    <li>Add the question particle -kö to it</li>
                    <li>Place this at the beginning of the sentence</li>
                    <li>Add the main verb in its connegative form</li>
                </ol>
                
                <div class="grammar-example">
                    <p>Affirmative question: <span class="finnish">Puhutko suomea?</span> <span class="english">Do you speak Finnish?</span></p>
                    <p>Negative question: <span class="finnish">Etkö puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></p>
                </div>
                
                <div class="grammar-example">
                    <p>Affirmative question: <span class="finnish">Onko hän kotona?</span> <span class="english">Is he/she at home?</span></p>
                    <p>Negative question: <span class="finnish">Eikö hän ole kotona?</span> <span class="english">Isn't he/she at home?</span></p>
                </div>
                
                <p>Here's how the negative question particle looks for different persons:</p>
                
                <table class="grammar-table">
                    <tr>
                        <th>Person</th>
                        <th>Negative + -kö</th>
                        <th>Example</th>
                    </tr>
                    <tr>
                        <td>minä (I)</td>
                        <td>enkö</td>
                        <td><span class="finnish">Enkö minä puhu suomea?</span> <span class="english">Don't I speak Finnish?</span></td>
                    </tr>
                    <tr>
                        <td>sinä (you)</td>
                        <td>etkö</td>
                        <td><span class="finnish">Etkö sinä puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></td>
                    </tr>
                    <tr>
                        <td>hän (he/she)</td>
                        <td>eikö</td>
                        <td><span class="finnish">Eikö hän puhu suomea?</span> <span class="english">Doesn't he/she speak Finnish?</span></td>
                    </tr>
                    <tr>
                        <td>me (we)</td>
                        <td>emmekö</td>
                        <td><span class="finnish">Emmekö me puhu suomea?</span> <span class="english">Don't we speak Finnish?</span></td>
                    </tr>
                    <tr>
                        <td>te (you plural)</td>
                        <td>ettekö</td>
                        <td><span class="finnish">Ettekö te puhu suomea?</span> <span class="english">Don't you (plural) speak Finnish?</span></td>
                    </tr>
                    <tr>
                        <td>he (they)</td>
                        <td>eivätkö</td>
                        <td><span class="finnish">Eivätkö he puhu suomea?</span> <span class="english">Don't they speak Finnish?</span></td>
                    </tr>
                </table>
            </div>
        </section>

        <section class="grammar-category">
            <h3>NEGATIVE QUESTIONS WITH DIFFERENT TENSES</h3>
            
            <div class="grammar-content">
                <h4>1. Present tense</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Etkö puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></p>
                    <p><span class="finnish">Eikö hän asu Helsingissä?</span> <span class="english">Doesn't he/she live in Helsinki?</span></p>
                </div>
                
                <h4>2. Past tense</h4>
                <p>For past tense negative questions, the structure is:</p>
                <p>Negative + -kö + subject + main verb in connegative form + past participle</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Etkö puhunut eilen suomea?</span> <span class="english">Didn't you speak Finnish yesterday?</span></p>
                    <p><span class="finnish">Eikö hän käynyt kaupassa?</span> <span class="english">Didn't he/she go to the store?</span></p>
                </div>
                
                <h4>3. Perfect tense</h4>
                <p>For perfect tense negative questions, the structure is:</p>
                <p>Negative + -kö + subject + "ole" + past participle</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Etkö ole käynyt Suomessa?</span> <span class="english">Haven't you been to Finland?</span></p>
                    <p><span class="finnish">Eivätkö he ole opiskelleet suomea?</span> <span class="english">Haven't they studied Finnish?</span></p>
                </div>
                
                <h4>4. Conditional</h4>
                <p>For conditional negative questions, the structure is:</p>
                <p>Negative + -kö + subject + conditional connegative form</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Etkö tulisi huomenna?</span> <span class="english">Wouldn't you come tomorrow?</span></p>
                    <p><span class="finnish">Eikö hän haluaisi kahvia?</span> <span class="english">Wouldn't he/she like coffee?</span></p>
                </div>
            </div>
        </section>

        <section class="grammar-category">
            <h3>NEGATIVE QUESTIONS WITH QUESTION WORDS</h3>
            
            <div class="grammar-content">
                <p>When combining question words (kuka, mikä, missä, etc.) with negation, the question word comes first, followed by the negative verb and the main verb:</p>
                
                <div class="grammar-example">
                    <p><span class="finnish">Miksi et tule?</span> <span class="english">Why aren't you coming?</span></p>
                    <p><span class="finnish">Missä hän ei ole käynyt?</span> <span class="english">Where hasn't he/she been?</span></p>
                    <p><span class="finnish">Kuka ei puhu suomea?</span> <span class="english">Who doesn't speak Finnish?</span></p>
                </div>
                
                <p>Note that in these questions, the -kö particle is not used with the negative verb because the question is already formed with a question word.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>ANSWERING NEGATIVE QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>Answering negative questions in Finnish follows a logical pattern rather than the English pattern:</p>
                
                <h4>1. Affirmative answers</h4>
                <div class="grammar-example">
                    <p>Question: <span class="finnish">Etkö puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></p>
                    <p>Answers:</p>
                    <p><span class="finnish">Kyllä puhun.</span> <span class="english">Yes, I do. (Literally: Yes, I speak.)</span></p>
                    <p><span class="finnish">Puhun kyllä.</span> <span class="english">I do speak.</span></p>
                </div>
                
                <h4>2. Negative answers</h4>
                <div class="grammar-example">
                    <p>Question: <span class="finnish">Etkö puhu suomea?</span> <span class="english">Don't you speak Finnish?</span></p>
                    <p>Answers:</p>
                    <p><span class="finnish">En puhu.</span> <span class="english">No, I don't. (Literally: I don't speak.)</span></p>
                    <p><span class="finnish">En.</span> <span class="english">No.</span></p>
                </div>
                
                <p>In Finnish, the answer corresponds to the actual state of affairs rather than agreeing or disagreeing with the negative question as in English. This makes Finnish answers more straightforward and less confusing than in English, where "Yes, I don't" or "No, I do" can be ambiguous.</p>
            </div>
        </section>

        <section class="grammar-category">
            <h3>USAGE AND MEANING OF NEGATIVE QUESTIONS</h3>
            
            <div class="grammar-content">
                <p>Negative questions in Finnish, as in many languages, often carry additional implications beyond simply seeking information:</p>
                
                <h4>1. Expressing surprise or disbelief</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Etkö sinä tiedä sitä?</span> <span class="english">Don't you know that? (I'm surprised you don't know.)</span></p>
                    <p><span class="finnish">Eikö hän tullut juhliin?</span> <span class="english">Didn't he/she come to the party? (I'm surprised he/she didn't come.)</span></p>
                </div>
                
                <h4>2. Expecting a positive answer</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Etkö pidä suklaasta?</span> <span class="english">Don't you like chocolate? (I expect that you do.)</span></p>
                    <p><span class="finnish">Eikö tämä ole hyvä idea?</span> <span class="english">Isn't this a good idea? (I think it is.)</span></p>
                </div>
                
                <h4>3. Making suggestions</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Emmekö voisi mennä elokuviin?</span> <span class="english">Couldn't we go to the movies? (I suggest we go.)</span></p>
                    <p><span class="finnish">Etkö haluaisi kahvia?</span> <span class="english">Wouldn't you like some coffee? (I'm offering you coffee.)</span></p>
                </div>
                
                <h4>4. Expressing criticism or complaint</h4>
                <div class="grammar-example">
                    <p><span class="finnish">Etkö voi olla hiljaa?</span> <span class="english">Can't you be quiet? (I wish you would be quiet.)</span></p>
                    <p><span class="finnish">Eikö kukaan auta minua?</span> <span class="english">Won't anyone help me? (I'm complaining about lack of help.)</span></p>
                </div>
            </div>
        </section>

        <div class="attribution">
            <p>Content adapted from various Finnish grammar resources. This page is designed for educational purposes.</p>
        </div>
    </div>

    


<script>
// Unified Mobile Menu Toggle Functionality
document.addEventListener('DOMContentLoaded', function() {
    const mobileMenuToggle = document.getElementById('mobile-menu-toggle');
    const navLinks = document.getElementById('nav-links');
    
    if (mobileMenuToggle && navLinks) {
        // Toggle mobile menu
        mobileMenuToggle.addEventListener('click', function(e) {
            // Prevent default behavior to avoid adding # to URL
            e.preventDefault();
            e.stopPropagation();
            
            navLinks.classList.toggle('show');
            this.classList.toggle('active');
            
            // Toggle icon between bars and times
            const icon = this.querySelector('i');
            if (icon) {
                if (navLinks.classList.contains('show')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Handle dropdown menus on mobile with improved touch handling
        const dropdownButtons = document.querySelectorAll('.dropbtn');
        dropdownButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                // Check if we're on mobile (window width <= 767px)
                if (window.innerWidth <= 767) {
                    e.preventDefault();
                    e.stopPropagation();
                    const dropdown = this.parentElement;
                    
                    // Close all other dropdowns
                    document.querySelectorAll('.dropdown').forEach(item => {
                        if (item !== dropdown && item.classList.contains('active')) {
                            item.classList.remove('active');
                        }
                    });
                    
                    // Toggle this dropdown
                    dropdown.classList.toggle('active');
                }
            });
        });
        
        // Close mobile menu when clicking outside
        document.addEventListener('click', function(e) {
            if (navLinks.classList.contains('show') && 
                !navLinks.contains(e.target) && 
                e.target !== mobileMenuToggle && 
                !mobileMenuToggle.contains(e.target)) {
                navLinks.classList.remove('show');
                mobileMenuToggle.classList.remove('active');
                
                // Reset icon
                const icon = mobileMenuToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
                
                // Close all dropdowns when mobile menu closes
                document.querySelectorAll('.dropdown.active').forEach(dropdown => {
                    dropdown.classList.remove('active');
                });
            }
        });
    }
    
    // Check if dark mode is enabled in localStorage
    if (localStorage.getItem('darkMode') === 'enabled') {
        document.body.classList.add('dark-mode');
        document.documentElement.setAttribute('data-theme', 'dark');
        themeToggleButtons.forEach(btn => {
            const icon = btn.querySelector('i');
            if (icon) {
                icon.className = 'fas fa-sun';
            }
        });
    }
    
    // Highlight toggle functionality
    const highlightToggleButton = document.getElementById('grammar-toggle-highlight');
    if (highlightToggleButton) {
        highlightToggleButton.addEventListener('click', function() {
            document.body.classList.toggle('highlight-mode');
            this.classList.toggle('active-tool');
            
            if (document.body.classList.contains('highlight-mode')) {
                localStorage.setItem('highlightMode', 'enabled');
            } else {
                localStorage.setItem('highlightMode', 'disabled');
            }
        });
        
        // Check if highlight mode is enabled in localStorage
        if (localStorage.getItem('highlightMode') === 'enabled') {
            document.body.classList.add('highlight-mode');
            highlightToggleButton.classList.add('active-tool');
        }
    }
});
</script>
</body>
</html>
















