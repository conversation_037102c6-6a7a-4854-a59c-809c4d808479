# Chapter 43: Testaus ja laadunvarmistus / Testing & Quality Assurance

## Objectives / Tavoitteet
- Learn vocabulary related to software testing and quality assurance in Finnish
- Understand how to discuss different testing methods and approaches
- Be able to explain testing strategies and quality control processes
- Master basic conversations about ensuring software reliability and quality

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Testaus - Testing
2. Laadunvarmistus - Quality assurance
3. Yksikkötestaus - Unit testing
4. Integraatiotestaus - Integration testing
5. J<PERSON>rjestelmätestaus - System testing
6. Hyväksymistestaus - Acceptance testing
7. Regressiotestaus - Regression testing
8. Testitapaus - Test case
9. Virhe - Bug/Error
10. Virheiden jäljitys - Debugging
11. Testikattavuus - Test coverage
12. Automatisoitu testaus - Automated testing
13. Manuaalinen testaus - Manual testing
14. Testaustyökalu - Testing tool
15. Laadunvalvonta - Quality control

## Grammar Points / Kielioppi
1. **Technical Verbs for Testing Operations**:
   - Action verbs for testing activities
   - Example: <PERSON><PERSON> sovelluksen toiminnallisuutta. (I test the application's functionality.)

2. **Conditional Forms for Testing Scenarios**:
   - Expressing test conditions
   - Example: Jo<PERSON> testi ep<PERSON>, korjaisimme virheen välittömästi. (If the test would fail, we would fix the error immediately.)

3. **Inessive Case (-ssa/-ssä) for Testing Contexts**:
   - In testing environments
   - Example: Yksikkötestauksessa keskitytään pieniin koodin osiin. (In unit testing, we focus on small parts of code.)

4. **Elative Case (-sta/-stä) for Testing Results**:
   - From testing sources
   - Example: Virhe löytyi integraatiotestauksesta. (The error was found from integration testing.)

5. **Translative Case (-ksi) for Testing Transformations**:
   - Converting to testing states
   - Example: Koodi muutetaan testattavaksi refaktoroimalla. (The code is made testable by refactoring.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Software testing workshop / Ohjelmistotestauksen työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa testauksen ja laadunvarmistuksen työpajaan! Tänään opimme, miten varmistamme ohjelmistojemme laadun ja toimivuuden.<br>
<em>(ter-ve-tu-lo-a tes-ta-uk-sen ja laa-dun-var-mis-tuk-sen työ-pa-jaan! tä-nään o-pim-me, mi-ten var-mis-tam-me oh-jel-mis-to-jem-me laa-dun ja toi-mi-vuu-den.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen kehittänyt sovelluksia, mutta en ole varma, testanko niitä riittävästi.<br>
<em>(kii-tos! o-len ke-hit-tä-nyt so-vel-luk-si-a, mut-ta en o-le var-ma, tes-tan-ko nii-tä riit-tä-väs-ti.)</em></p>

<p><strong>Ohjaaja</strong>: Se on yleinen huolenaihe. Aloitetaan käymällä läpi erilaisia testauksen tasoja ja menetelmiä.<br>
<em>(se on y-lei-nen huo-len-ai-he. a-loi-te-taan käy-mäl-lä lä-pi e-ri-lai-si-a tes-ta-uk-sen ta-so-ja ja me-ne-tel-mi-ä.)</em></p>

<p><strong>Osallistuja</strong>: Mitä erilaisia testauksen tasoja on olemassa?<br>
<em>(mi-tä e-ri-lai-si-a tes-ta-uk-sen ta-so-ja on o-le-mas-sa?)</em></p>

<p><strong>Osallistuja</strong>: Miten yksikkötestausta tehdään käytännössä?<br>
<em>(mi-ten yk-sik-kö-tes-ta-us-ta teh-dään käy-tän-nös-sä?)</em></p>

<p><strong>Osallistuja</strong>: Entä integraatiotestaus?<br>
<em>(en-tä in-teg-raa-ti-o-tes-ta-us?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin mitata testauksen kattavuutta?<br>
<em>(mi-ten voin mi-ta-ta tes-ta-uk-sen kat-ta-vuut-ta?)</em></p>

<p><strong>Osallistuja</strong>: Miten automatisoitua testausta voidaan toteuttaa?<br>
<em>(mi-ten au-to-ma-ti-soi-tu-a tes-ta-us-ta voi-daan to-teut-taa?)</em></p>

<p><strong>Osallistuja</strong>: Miten laadunvarmistus eroaa testauksesta?<br>
<em>(mi-ten laa-dun-var-mis-tus e-ro-aa tes-ta-uk-ses-ta?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin aloittaa testauksen käyttöönoton projektissani?<br>
<em>(mi-ten voin a-loit-taa tes-ta-uk-sen käyt-töön-o-ton pro-jek-tis-sa-ni?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla lisätä testejä projektiini näiden ohjeiden avulla?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la li-sä-tä tes-te-jä pro-jek-tii-ni näi-den oh-jei-den a-vul-la?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloita pienestä - valitse yksi komponentti tai funktio ja kirjoita sille muutama yksikkötesti. Kun olet saanut testit toimimaan, voit laajentaa testausta vähitellen. Muista, että testaus on taito, joka kehittyy harjoittelun myötä. Älä lannistu, jos kohtaat aluksi haasteita - ne ovat osa oppimisprosessia.<br>
<em>(eh-dot-to-mas-ti! a-loi-ta pie-nes-tä - va-lit-se yk-si kom-po-nent-ti tai funk-ti-o ja kir-joi-ta sil-le muu-ta-ma yk-sik-kö-tes-ti. kun o-let saa-nut tes-tit toi-mi-maan, voit laa-jen-taa tes-ta-us-ta vä-hi-tel-len. muis-ta, et-tä tes-ta-us on tai-to, jo-ka ke-hit-tyy har-joit-te-lun my-ö-tä. ä-lä lan-nis-tu, jos koh-taat a-luk-si haas-tei-ta - ne o-vat o-sa op-pi-mis-pro-ses-si-a.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin testata sovelluksiani ja varmistaa niiden laadun.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin tes-ta-ta so-vel-luk-si-a-ni ja var-mis-taa nii-den laa-dun.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong focus on software quality and reliability, reflecting the national value of trustworthiness
- Finnish tech companies often emphasize thorough testing practices and quality assurance
- Test-driven development (TDD) and automated testing are increasingly popular in Finnish software development
- Finnish educational institutions typically include testing methodologies in their software engineering curricula
- The Finnish tech community values clean, well-tested code over quick but unreliable solutions


