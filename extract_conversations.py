#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import re
import glob

# Define the category folders
category_folders = [
    "Daily_Life",
    "Cleaner",
    "Kitchen_Assistant",
    "Warehouse",
]

# Web_Development_IT folder is not accessible for some reason


def extract_conversation_text(file_path):
    """Extract conversation text from a file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()

        # Check if the file contains a conversation section
        if '<div class="conversation">' in content:
            print(f"  Found conversation in {os.path.basename(file_path)}")
            # Get the chapter title
            title_match = re.search(r"^# (Chapter \d+: .+)", content, re.MULTILINE)
            if title_match:
                chapter_title = title_match.group(1)
            else:
                chapter_title = os.path.basename(file_path).replace(".md", "")

            # Extract the conversation HTML
            conversation_match = re.search(
                r'<div class="conversation">(.*?)</div>', content, re.DOTALL
            )
            if conversation_match:
                conversation_html = conversation_match.group(1)
                print(
                    f"  Extracted conversation HTML from {os.path.basename(file_path)}"
                )

                # Extract speaker and dialogue from each paragraph
                paragraphs = re.findall(
                    r"<p><strong>(.*?)</strong>: (.*?)(?:<br>.*?)?</p>",
                    conversation_html,
                    re.DOTALL,
                )

                if paragraphs:
                    print(
                        f"  Found {len(paragraphs)} paragraphs in {os.path.basename(file_path)}"
                    )
                    clean_conversation = f"# {chapter_title}\n\n"

                    for speaker, dialogue in paragraphs:
                        # Remove any HTML tags from the dialogue
                        dialogue = re.sub(r"<[^>]+>", "", dialogue)

                        # Add to clean conversation
                        clean_conversation += f"{speaker}: {dialogue}\n\n"

                    return clean_conversation
                else:
                    print(
                        f"  No paragraphs found in conversation in {os.path.basename(file_path)}"
                    )
            else:
                print(
                    f"  Could not extract conversation HTML from {os.path.basename(file_path)}"
                )
        else:
            print(f"  No conversation found in {os.path.basename(file_path)}")
    except Exception as e:
        print(f"Error processing {file_path}: {e}")

    return None


# Process each category folder
for folder in category_folders:
    folder_path = os.path.join("chapters", folder)

    # Check if the folder exists
    if os.path.exists(folder_path):
        print(f"Processing {folder} folder...")

        # Get all chapter files in the folder
        chapter_files = sorted(glob.glob(os.path.join(folder_path, "chapter*.md")))

        all_conversations = f"# {folder} Conversations\n\n"
        conversation_count = 0

        # Process each chapter file
        for file_path in chapter_files:
            print(f"  Processing {os.path.basename(file_path)}...")

            conversation_text = extract_conversation_text(file_path)

            if conversation_text:
                all_conversations += f"{conversation_text}\n---\n\n"
                conversation_count += 1

        # Save the combined conversations to a file if any were found
        if conversation_count > 0:
            output_file_name = f"{folder}_conversations.txt"

            # Force BOM for UTF-8
            with open(output_file_name, "wb") as f:
                # Write UTF-8 BOM
                f.write(b"\xef\xbb\xbf")
                # Write content as UTF-8
                f.write(all_conversations.encode("utf-8"))

            print(
                f"Created {output_file_name} with {conversation_count} conversations."
            )
        else:
            print(f"No conversations found in {folder} folder.")
    else:
        print(f"Folder {folder_path} does not exist. Skipping.")

print("Conversation extraction complete.")
