#!/usr/bin/env python3
"""
Script to fix submenu functionality in main pages (games.html, chapter.html)
"""

import os
import re

# List of main page files to fix
MAIN_PAGE_FILES = [
    "games.html",
    "chapter.html"
]

def fix_submenu_in_file(file_path):
    """Fix submenu functionality in a main page file"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Fix the submenu click handler - add mobile menu condition
        old_pattern = r'(submenuHeader\.addEventListener\(\'click\', function\(e\) \{\s+e\.preventDefault\(\);\s+e\.stopPropagation\(\);\s+)(// Close other active submenus)'
        new_replacement = r'\1// Only handle submenu on desktop or when mobile menu is open\n                if (window.innerWidth > 767 || (navLinks && navLinks.classList.contains(\'show\'))) {\n                    \2'
        
        content = re.sub(old_pattern, new_replacement, content, flags=re.DOTALL)
        
        # Fix the toggle submenu part - add closing brace
        old_toggle = r'(// Toggle current submenu\s+submenu\.classList\.toggle\(\'active\'\);)\s+(\}\);)'
        new_toggle = r'\1\n                }\n            \2'
        
        content = re.sub(old_toggle, new_toggle, content, flags=re.DOTALL)
        
        # Fix the outside click handler
        old_outside = r'(// Close submenus when clicking outside\s+document\.addEventListener\(\'click\', function\(e\) \{\s+)(if \(!e\.target\.closest\(\'\.dropdown-submenu\'\)\) \{)'
        new_outside = r'\1// Don\'t close submenus if clicking on mobile menu elements\n            \2 && \n                !e.target.closest(\'.mobile-menu-toggle\') && \n                !e.target.closest(\'.nav-links\')) {'
        
        content = re.sub(old_outside, new_outside, content, flags=re.DOTALL)
        
        # Only write if content changed
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✓ Fixed submenu: {file_path}")
            return True
        else:
            print(f"- No changes needed: {file_path}")
            return False
            
    except Exception as e:
        print(f"✗ Error fixing {file_path}: {e}")
        return False

def main():
    """Main function"""
    print("Fixing submenu functionality in main pages...")
    
    updated_count = 0
    
    for file_path in MAIN_PAGE_FILES:
        if os.path.exists(file_path):
            if fix_submenu_in_file(file_path):
                updated_count += 1
        else:
            print(f"✗ File not found: {file_path}")
    
    print(f"\nFixed {updated_count} main page files")

if __name__ == "__main__":
    main()
