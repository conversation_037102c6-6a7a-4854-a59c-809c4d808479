# Chapter 35: DOM-manipulaatio / DOM Manipulation

## Objectives / Tavoitteet
- Learn vocabulary related to DOM manipulation in Finnish
- Understand how to discuss dynamic web page interactions
- Be able to explain how to select and modify HTML elements with JavaScript
- Master basic conversations about creating interactive web experiences

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. DOM - Document Object Model
2. Elementti - Element
3. Solmu - Node
4. Valitsin - Selector
5. <PERSON><PERSON>ht<PERSON>käsittelijä - Event handler
6. Kuuntelija - Listener
7. Attribuutti - Attribute
8. Sisältö - Content
9. Luokka - Class
10. Tunniste - ID
11. Lisääminen - Adding
12. Poistaminen - Removing
13. Muokkaaminen - Modifying
14. Läpikäynti - Traversal
15. Dynaaminen - Dynamic

## Grammar Points / Kielioppi
1. **Imperative Forms for DOM Operations**:
   - Commands for manipulating elements
   - Example: Valitse elementti ja muuta sen sisältöä. (Select an element and change its content.)

2. **Conditional Forms for Event Handling**:
   - Expressing event-based conditions
   - Example: <PERSON><PERSON> kä<PERSON><PERSON><PERSON><PERSON> klik<PERSON>a painiketta, elementti piilotettaisi<PERSON>. (When the user clicks the button, the element would be hidden.)

3. **Inessive Case (-ssa/-ssä) for DOM Contexts**:
   - In document structures
   - Example: DOM-puussa elementit ovat hierarkkisessa järjestyksessä. (In the DOM tree, elements are in hierarchical order.)

4. **Translative Case (-ksi) for DOM Transformations**:
   - Changing element states
   - Example: Elementti muutetaan näkyväksi. (The element is changed to visible.)

5. **Elative Case (-sta/-stä) for DOM Selection**:
   - From document structures
   - Example: Elementti valitaan dokumentista tunnisteen perusteella. (The element is selected from the document based on ID.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: DOM manipulation workshop / DOM-manipulaation työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa DOM-manipulaation työpajaan! Tänään opimme, miten voimme muokata verkkosivun rakennetta ja sisältöä JavaScriptillä.<br>
<em>(ter-ve-tu-lo-a DOM-ma-ni-pu-laa-ti-on työ-pa-jaan! tä-nään o-pim-me, mi-ten voim-me muo-ka-ta verk-ko-si-vun ra-ken-net-ta ja si-säl-tö-ä ja-va-scrip-til-lä.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen jo oppinut JavaScript-perusteita, mutta haluaisin tietää enemmän DOM:ista.<br>
<em>(kii-tos! o-len jo op-pi-nut ja-va-script-pe-rus-tei-ta, mut-ta ha-lu-ai-sin tie-tää e-nem-män DOM:is-ta.)</em></p>

<p><strong>Ohjaaja</strong>: Hienoa! DOM eli Document Object Model on ohjelmointirajapinta, joka esittää HTML-dokumentin puurakenteena. Tämän avulla JavaScript voi muokata sivun sisältöä, rakennetta ja tyylejä.<br>
<em>(hie-no-a! DOM e-li do-cu-ment ob-ject mo-del on oh-jel-moin-ti-ra-ja-pin-ta, jo-ka e-sit-tää HTML-do-ku-men-tin puu-ra-ken-tee-na. tä-män a-vul-la ja-va-script voi muo-ka-ta si-vun si-säl-tö-ä, ra-ken-net-ta ja tyy-le-jä.)</em></p>

<p><strong>Osallistuja</strong>: Miten voin valita elementtejä DOM:ista?<br>
<em>(mi-ten voin va-li-ta e-le-ment-te-jä DOM:is-ta?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin muuttaa elementin sisältöä?<br>
<em>(mi-ten voin muut-taa e-le-men-tin si-säl-tö-ä?)</em></p>

<p><strong>Osallistuja</strong>: Entä luokkien lisääminen ja poistaminen?<br>
<em>(en-tä luok-ki-en li-sää-mi-nen ja pois-ta-mi-nen?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin luoda uusia elementtejä ja lisätä niitä sivulle?<br>
<em>(mi-ten voin luo-da uu-si-a e-le-ment-te-jä ja li-sä-tä nii-tä si-vul-le?)</em></p>

<p><strong>Osallistuja</strong>: Entä elementtien poistaminen?<br>
<em>(en-tä e-le-ment-ti-en pois-ta-mi-nen?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin reagoida käyttäjän toimintoihin, kuten klikkauksiin?<br>
<em>(mi-ten voin re-a-goi-da käyt-tä-jän toi-min-toi-hin, ku-ten klik-ka-uk-siin?)</em></p>

<p><strong>Osallistuja</strong>: Entä lomakkeiden käsittely?<br>
<em>(en-tä lo-mak-kei-den kä-sit-te-ly?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen sovelluksen, joka käyttää DOM-manipulaatiota?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen so-vel-luk-sen, jo-ka käyt-tää DOM-ma-ni-pu-laa-ti-o-ta?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Tehdään yksinkertainen tehtävälista-sovellus. Tarvitset HTML-lomakkeen uusien tehtävien lisäämiseen, JavaScript-koodia tehtävien lisäämiseen listaan ja toiminnallisuuden tehtävien merkitsemiseen valmiiksi tai poistamiseen.<br>
<em>(eh-dot-to-mas-ti! teh-dään yk-sin-ker-tai-nen teh-tä-vä-lis-ta-so-vel-lus. tar-vit-set HTML-lo-mak-keen uu-si-en teh-tä-vi-en li-sää-mi-seen, ja-va-script-koo-di-a teh-tä-vi-en li-sää-mi-seen lis-taan ja toi-min-nal-li-suu-den teh-tä-vi-en mer-kit-se-mi-seen val-miik-si tai pois-ta-mi-seen.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin käyttää DOM-manipulaatiota interaktiivisten verkkosivujen luomiseen.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin käyt-tää DOM-ma-ni-pu-laa-ti-o-ta in-ter-ak-tii-vis-ten verk-ko-si-vu-jen luo-mi-seen.)</em></p>
</div>

### Cultural Notes:
- Finnish web developers often value clean, efficient DOM manipulation that enhances user experience without being intrusive
- In Finland's tech education, DOM manipulation is taught as a fundamental skill for creating interactive web applications
- Finnish websites often feature practical, user-friendly interfaces that reflect the national value of functionality
- Many Finnish tech companies develop web applications that require sophisticated DOM manipulation
- Finland's strong focus on accessibility means DOM manipulation is often taught with accessibility considerations in mind


