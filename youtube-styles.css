/**
 * YouTube Styles
 * Combined styles for YouTube integration
 */

/* Player Layout */
.youtube-player-layout {
    display: flex;
    gap: 25px;
    align-items: flex-start;
    flex-wrap: wrap;
    justify-content: center;
    overflow: visible;
    margin-bottom: 20px;
}

.youtube-player-container {
    flex: 2;
    max-width: 1200px;
    width: 100%;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    padding-top: 48%; /* Reduced from 50.625% to fix Play button visibility */
    max-height: 90vh;
}

.youtube-player-container[data-channel-key="kuulostaahyvalta"] {
    flex: 2;
    max-width: 1200px;
    width: 100%;
    background-color: transparent;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    padding-top: 48%; /* Reduced from 50.625% to fix Play button visibility */
    max-height: 90vh;
}

.youtube-player-iframe,
.youtube-player-container iframe {
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 10;
    border: none;
    overflow: hidden;
}

/* Video List */
.youtube-video-list-container {
    flex: 1;
    width: 100%;
    max-width: 100%;
    margin: 20px auto 0;
    height: auto;
    overflow-y: visible;
}

/* Remove custom scrollbar styles as we're not using overflow-y anymore */

.youtube-video-list {
    max-height: none;
    overflow-y: visible;
    overflow: visible;
    margin-bottom: 30px;
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
    width: 100%;
    max-width: 1200px;
}

/* On extra large screens, use 2 columns grid layout */
@media (min-width: 1400px) {
    .youtube-video-list {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
        width: 100% !important;
        max-width: 1200px !important;
    }
    
    .youtube-video-item {
        display: flex !important;
        width: 100% !important;
    }
}

.youtube-video-item {
    display: flex;
    padding: 8px;
    gap: 8px;
    border: 1px solid rgba(0,0,0,0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

/* On extra large screens, maintain card-like appearance */
@media (min-width: 1400px) {
    .youtube-video-item {
        border-radius: 8px;
        border: 1px solid rgba(0,0,0,0.1);
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        padding: 8px;
    }
}

.youtube-video-item:hover {
    background-color: rgba(0,0,0,0.05);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.youtube-video-item.active {
    background-color: rgba(0,0,0,0.1);
    border-color: rgba(0,0,0,0.2);
}

/* On extra large screens, maintain hover effects */
@media (min-width: 1400px) {
    .youtube-video-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
}

.youtube-video-thumbnail {
    width: 168px;
    height: 94px;
    margin-right: 10px;
    border-radius: 8px;
    overflow: hidden;
    flex-shrink: 0;
}

/* Make thumbnails responsive in grid layout */
@media (max-width: 1399px) {
    .youtube-video-thumbnail {
        width: 120px;
        height: 68px;
    }
}

.youtube-video-thumbnail img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.youtube-video-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.youtube-video-title {
    font-size: 14px;
    font-weight: 500;
    margin: 0 0 5px 0;
    line-height: 1.2;
    display: block;
    white-space: normal;
    overflow: visible;
    text-overflow: initial;
}

.youtube-video-date {
    font-size: 12px;
    color: #606060;
    margin: 0;
}

/* Loading Indicator */
.loading-indicator {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    font-size: 16px;
    color: #666;
}

.loading-indicator i {
    margin-right: 10px;
}

/* Pagination */
.pagination-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 0 0 20px 0;
    padding: 5px 15px 5px 15px;
    background-color: #e9f0f7;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    border: 1px solid #c0d6e8;
    position: relative;
    z-index: 100;
}

.pagination-controls {
    display: flex;
    gap: 5px;
}

.pagination-button {
    background-color: #fff;
    border: 1px solid #007bff;
    border-radius: 4px;
    padding: 5px 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 12px;
    color: #007bff;
    font-weight: 500;
    min-width: 30px;
    text-align: center;
}

.pagination-button:hover {
    background-color: #007bff;
    color: #fff;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.page-info {
    font-size: 14px;
    color: #2c3e50;
    font-weight: 600;
}

/* Error Messages */
.pagination-error {
    color: #721c24;
    font-weight: 500;
    flex-grow: 1;
    text-align: center;
}

.pagination-retry-button {
    background-color: #007bff;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    margin-left: 15px;
}

/* Tab Content */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* Channel Container */
.youtube-channel-container {
    display: none;
}

/* Responsive Adjustments */
/* Default layout for all screen sizes - video list below player */
.youtube-player-layout {
    flex-direction: column !important;
    align-items: center !important;
    flex-wrap: wrap !important;
    justify-content: center !important;
    overflow: visible !important; /* Changed from hidden to visible */
}

.youtube-player-container,
.youtube-player-container[data-channel-key="kuulostaahyvalta"] {
    max-width: 100% !important;
    width: 100% !important;
    flex: 1 1 100% !important;
    padding-top: 48% !important; /* Reduced to fix Play button visibility */
}

.youtube-video-list-container {
    width: 100% !important;
    max-width: 1200px !important;
    flex: 1 1 100% !important;
    margin: 20px auto 0 !important;
    height: auto !important;
    display: flex !important;
    flex-direction: column !important;
    align-items: center !important;
    overflow: visible !important;
}

/* Ensure videos container is visible */
.youtube-videos-container,
div[id^="youtube-videos-container-"] {
    display: block !important;
    width: 100% !important;
    overflow: visible !important; /* Changed from hidden to visible */
    max-height: none !important;
}

/* List column styles */
.youtube-list-column {
    width: 100% !important;
    max-width: 100% !important;
    min-width: auto !important;
    flex: 1 1 100% !important;
    overflow: visible !important;
    max-height: none !important; /* Remove max-height restriction */
}

/* Override inline styles for screens below 1400px */
@media (max-width: 1399px) {
    .youtube-list-column,
    div.youtube-list-column,
    #youtube-list-column-finnishcrashcourse,
    #youtube-list-column-finnishtogo,
    #youtube-list-column-suomenkurssiyt,
    #youtube-list-column-kuulostaahyvalta,
    div[id^="youtube-list-column-"] {
        width: 100% !important;
        max-width: 100% !important;
        min-width: auto !important;
        flex: 1 1 100% !important;
        overflow: visible !important;
        max-height: none !important; /* Remove max-height restriction */
    }
    
    /* Ensure video containers are visible */
    .youtube-video-list-container,
    .youtube-videos-container,
    div[id^="youtube-videos-container-"],
    div[id^="youtube-video-list-"] {
        overflow: visible !important; /* Changed from hidden to visible */
        max-height: none !important;
    }
}

/* Video list styles */
.youtube-video-list {
    width: 100% !important;
    display: grid !important;
    grid-template-columns: repeat(2, 1fr) !important;
    gap: 15px !important;
    padding-right: 0 !important;
    overflow: visible !important;
    max-height: none !important;
}

/* Only on extra large screens (1400px and up), show video list on the right */
@media (min-width: 1400px) {
    .youtube-player-layout {
        flex-direction: row !important;
        flex-wrap: nowrap !important;
        align-items: flex-start !important;
        justify-content: space-between !important;
        overflow: visible !important; /* Change from hidden to visible */
    }
    
    .youtube-player-container,
    .youtube-player-container[data-channel-key="kuulostaahyvalta"] {
        flex: 2 !important;
        max-width: 65% !important;
        width: 65% !important;
        padding-top: 36% !important; /* Reduced to fix Play button visibility */
        max-height: 95vh !important;
    }
    
    .youtube-video-list-container {
        flex: 1 !important;
        min-width: 350px !important;
        max-width: 35% !important;
        margin-top: 0 !important;
        display: block !important;
        align-items: normal !important;
        width: 35% !important;
        overflow: visible !important; /* Ensure no overflow-y scrollbar */
    }
    
    .youtube-list-column {
        width: 35% !important;
        max-width: 35% !important;
        min-width: 350px !important;
        flex: 1 !important;
        overflow: visible !important;
        max-height: none !important;
        display: block !important;
    }
    
    /* Ensure video list is visible */
    .youtube-videos-container,
    div[id^="youtube-videos-container-"] {
        display: block !important;
        width: 100% !important;
        overflow: visible !important; /* Change from hidden to visible */
        max-height: none !important; /* Remove any height restriction */
    }
    
    .youtube-video-list {
        display: grid !important;
        grid-template-columns: repeat(2, 1fr) !important;
        gap: 15px !important;
        width: 100% !important;
        overflow: visible !important;
        max-height: none !important; /* Remove any height restriction */
    }
}

/* Adjustments for medium screens */
@media (max-width: 1200px) {
    .youtube-video-thumbnail {
        width: 150px;
        height: 84px;
    }
}

/* Adjustments for small screens */
@media (max-width: 900px) {
    .youtube-video-thumbnail {
        width: 130px;
        height: 73px;
    }
}

/* Dark mode styles */
.dark-mode .youtube-channel-container {
    background-color: var(--dark-card-bg, #2d2d2d);
}

.dark-mode .youtube-video-item {
    background-color: var(--dark-card-bg, #2d2d2d);
    border-color: rgba(255, 255, 255, 0.1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.dark-mode .youtube-video-item:hover {
    background-color: var(--dark-hover-bg, #3d3d3d);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* On extra large screens, maintain dark mode styles */
@media (min-width: 1400px) {
    .dark-mode .youtube-video-item {
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    .dark-mode .youtube-video-item:hover {
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    }
}

/* Remove dark mode scrollbar styles as we're not using overflow-y anymore */

.dark-mode .youtube-video-title {
    color: var(--dark-heading-color, #e0e0e0);
}

.dark-mode .youtube-video-date {
    color: var(--dark-text-color-secondary, #a0a0a0);
}

.dark-mode .pagination-container {
    background-color: #2d3748;
    border-color: #4a5568;
}

.dark-mode .pagination-button {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #90cdf4;
}

.dark-mode .pagination-button:hover {
    background-color: #4a5568;
    color: #fff;
}

.dark-mode .page-info {
    color: #e2e8f0;
}

/* Ensure fullscreen works properly */
.youtube-player-container iframe:-webkit-full-screen,
.youtube-player-iframe:-webkit-full-screen,
iframe[allowfullscreen]:-webkit-full-screen,
iframe[src*="youtube.com"]:-webkit-full-screen,
.youtube-player-container iframe:fullscreen,
.youtube-player-iframe:fullscreen,
iframe[allowfullscreen]:fullscreen,
iframe[src*="youtube.com"]:fullscreen {
    width: 100vw !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 9999 !important;
    border: none !important;
    margin: 0 !important;
    padding: 0 !important;
    overflow: hidden !important;
    object-fit: contain !important;
}