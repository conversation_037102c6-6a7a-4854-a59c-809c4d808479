// Main JavaScript for Opiskelen Suomea

// Mobile menu toggle functionality
document.addEventListener("DOMContentLoaded", function () {
  const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  const navLinks = document.getElementById("nav-links");

  if (mobileMenuToggle && navLinks) {
    // Toggle mobile menu
    mobileMenuToggle.addEventListener("click", function () {
      navLinks.classList.toggle("show");
      this.classList.toggle("active");
    });
  }

  // Handle dropdown menus in mobile view
  const dropdowns = document.querySelectorAll(".dropdown");
  dropdowns.forEach((dropdown) => {
    const dropbtn = dropdown.querySelector(".dropbtn");
    if (dropbtn) {
      dropbtn.addEventListener("click", function (e) {
        // Only in mobile view
        if (window.innerWidth <= 767) {
          e.preventDefault();
          e.stopPropagation();

          // Close other active dropdowns
          dropdowns.forEach((otherDropdown) => {
            if (
              otherDropdown !== dropdown &&
              otherDropdown.classList.contains("active")
            ) {
              otherDropdown.classList.remove("active");
            }
          });

          // Toggle current dropdown
          dropdown.classList.toggle("active");
        }
      });
    }
  });

  // Close mobile menu when clicking outside
  document.addEventListener("click", function (e) {
    if (navLinks && navLinks.classList.contains("show")) {
      // Check if click is outside the nav menu
      if (!navLinks.contains(e.target) && e.target !== mobileMenuToggle) {
        navLinks.classList.remove("show");
        if (mobileMenuToggle) {
          mobileMenuToggle.classList.remove("active");
        }
      }
    }
  });
});

// Toggle dark mode function - this will be overridden by the custom function in index.html
// Keeping this here for compatibility with other pages
function toggleDarkMode_original() {
  document.body.classList.toggle("dark-mode");

  // Store preference in localStorage
  if (document.body.classList.contains("dark-mode")) {
    localStorage.setItem("darkMode", "enabled");

    // Change icon to sun
    const darkModeBtn = document.getElementById("index-toggle-dark");
    if (darkModeBtn) {
      const icon = darkModeBtn.querySelector("i");
      if (icon) {
        icon.classList.remove("fa-moon");
        icon.classList.add("fa-sun");
      }
    }
  } else {
    localStorage.setItem("darkMode", "disabled");

    // Change icon back to moon
    const darkModeBtn = document.getElementById("index-toggle-dark");
    if (darkModeBtn) {
      const icon = darkModeBtn.querySelector("i");
      if (icon) {
        icon.classList.remove("fa-sun");
        icon.classList.add("fa-moon");
      }
    }
  }
}

// Function to handle tab switching
function openCategory(evt, categoryName) {
  // Hide all category content
  var tabContent = document.getElementsByClassName("tab-content");
  for (var i = 0; i < tabContent.length; i++) {
    tabContent[i].style.display = "none";
  }

  // Remove "active" class from all tab buttons
  var tabButtons = document.getElementsByClassName("tab-button");
  for (var i = 0; i < tabButtons.length; i++) {
    tabButtons[i].className = tabButtons[i].className.replace(" active", "");
  }

  // Show the current tab and add "active" class to the button that opened the tab
  document.getElementById(categoryName).style.display = "block";
  evt.currentTarget.className += " active";
}

// Function to open chapter page
function openChapterPage(category, chapterNumber) {
  // Navigate to the chapter page with the category and chapter number as URL parameters
  window.location.href = `chapter.html?category=${category}&chapter=${chapterNumber}`;
}

// Function to fetch chapter content
async function fetchChapterContent(category, chapterNumber) {
  try {
    // Fetch the markdown content from our API
    const response = await fetch(
      `http://localhost:3000/api/chapter/${category}/${chapterNumber}`
    );

    if (!response.ok) {
      throw new Error(
        `Failed to load chapter content (Status: ${response.status})`
      );
    }

    // Get the markdown text
    const markdown = await response.text();

    // Convert markdown to HTML
    const html = convertMarkdownToHtml(markdown);

    return `<div class="chapter-modal-content">${html}</div>`;
  } catch (error) {
    console.error("Error fetching chapter content:", error);
    throw error;
  }
}

// Function to convert markdown to HTML
function convertMarkdownToHtml(markdown) {
  // This is a simple markdown to HTML converter
  // For a real implementation, you might want to use a library like marked.js

  let html = markdown;

  // Convert headers
  html = html.replace(/^# (.*$)/gm, "<h1>$1</h1>");
  html = html.replace(/^## (.*$)/gm, "<h2>$1</h2>");
  html = html.replace(/^### (.*$)/gm, "<h3>$1</h3>");

  // Convert lists
  html = html.replace(/^\- (.*$)/gm, "<li>$1</li>");
  html = html.replace(/^\d+\. (.*$)/gm, "<li>$1</li>");

  // Wrap lists
  html = html.replace(/<li>.*(?:\n<li>.*)*(?=\n\n|\n$|$)/g, function (match) {
    if (match.indexOf("1.") !== -1 || match.indexOf("2.") !== -1) {
      return "<ol>" + match + "</ol>";
    } else {
      return "<ul>" + match + "</ul>";
    }
  });

  // Convert bold text
  html = html.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

  // Convert italic text
  html = html.replace(/\*(.*?)\*/g, "<em>$1</em>");

  // Convert paragraphs
  html = html.replace(/^(?!<[a-z])(.*$)/gm, function (match) {
    if (match.trim() === "") return "";
    return "<p>" + match + "</p>";
  });

  // Clean up empty paragraphs
  html = html.replace(/<p><\/p>/g, "");

  return html;
}

// Function to load more chapters
function loadMoreChapters(category, startChapter, endChapter) {
  const chapterGrid = document.getElementById(`${category}-chapters`);
  const loadMoreContainer = chapterGrid.querySelector(".load-more-container");

  // Show loading state
  loadMoreContainer.innerHTML =
    '<div class="loader" style="display:block;"></div>';

  // Create HTML for additional chapters
  let chaptersHTML = "";

  for (let i = startChapter; i <= endChapter; i++) {
    let chapterTitle = "";
    let chapterDescription = "";

    // Set chapter titles and descriptions based on category and chapter number
    switch (category) {
      case "daily-life":
        if (i === 9) {
          chapterTitle = "Ravitsemus ja ateriat / Nutrition and Meals";
          chapterDescription =
            "Learn vocabulary related to food, nutrition, and meal planning in Finnish.";
        } else if (i === 10) {
          chapterTitle = "Erityisruokavaliot / Special Diets";
          chapterDescription =
            "Master vocabulary for different dietary requirements and food preferences.";
        } else if (i === 11) {
          chapterTitle = "Musiikki ja elokuvat / Music and Movies";
          chapterDescription =
            "Learn vocabulary related to entertainment, music genres, and film in Finnish.";
        } else if (i === 12) {
          chapterTitle = "Muoti ja vaatteet / Fashion and Clothing";
          chapterDescription =
            "Master vocabulary for different clothing items, styles, and shopping for clothes.";
        } else if (i === 13) {
          chapterTitle = "Ystävällinen rupattelu / Friendly Small Talk";
          chapterDescription =
            "Learn casual conversation topics and phrases for social interactions.";
        } else if (i === 14) {
          chapterTitle = "Juhlapäivät ja perinteet / Holidays and Traditions";
          chapterDescription =
            "Master vocabulary related to Finnish holidays, celebrations, and cultural traditions.";
        } else if (i === 15) {
          chapterTitle = "Koulutus ja opiskelu / Education and Study";
          chapterDescription =
            "Learn vocabulary related to education, courses, and academic environments.";
        } else if (i === 16) {
          chapterTitle = "Työpaikka ja ura / Workplace and Career";
          chapterDescription =
            "Master vocabulary for professional environments, job roles, and career development.";
        } else if (i === 17) {
          chapterTitle = "Raha ja pankkiasiat / Money and Banking";
          chapterDescription =
            "Learn vocabulary related to finances, banking services, and money management.";
        } else if (i === 18) {
          chapterTitle = "Puhelimet ja viestintä / Phones and Communication";
          chapterDescription =
            "Master vocabulary for different communication methods and devices.";
        } else if (i === 19) {
          chapterTitle = "Perustekniikan käyttö / Basic Tech Use";
          chapterDescription =
            "Learn vocabulary related to everyday technology and digital devices.";
        } else if (i === 20) {
          chapterTitle =
            "Netiketti ja sosiaalinen media / Netiquette and Social Media";
          chapterDescription =
            "Master vocabulary for online communication and social platforms.";
        } else if (i === 21) {
          chapterTitle = "Lemmikit ja eläimet / Pets and Animals";
          chapterDescription =
            "Learn vocabulary related to pets, wildlife, and animal care.";
        } else if (i === 22) {
          chapterTitle =
            "Ympäristö ja kestävyys / Environment and Sustainability";
          chapterDescription =
            "Master vocabulary for environmental topics and sustainable practices.";
        } else if (i === 23) {
          chapterTitle = "Kierrätys ja jätteet / Recycling and Waste";
          chapterDescription =
            "Learn vocabulary related to waste management and recycling systems in Finland.";
        } else if (i === 24) {
          chapterTitle = "Kodin turvallisuus / Home Safety";
          chapterDescription =
            "Master vocabulary for home security, safety measures, and emergency situations.";
        } else if (i === 25) {
          chapterTitle = "Ensiavun perusteet / First Aid Basics";
          chapterDescription =
            "Learn vocabulary related to basic first aid and emergency medical situations.";
        } else if (i === 26) {
          chapterTitle = "Paikallinen kulttuuri / Local Culture";
          chapterDescription =
            "Master vocabulary for discussing Finnish cultural norms and local customs.";
        } else if (i === 27) {
          chapterTitle = "Toimistot ja palvelut / Offices and Services";
          chapterDescription =
            "Learn vocabulary related to public services and administrative offices.";
        } else if (i === 28) {
          chapterTitle = "Verkkokauppa ja tilaukset / Online Ordering";
          chapterDescription =
            "Master vocabulary for online shopping, deliveries, and digital services.";
        } else if (i === 29) {
          chapterTitle = "Korjaukset ja huolto / Repairs and Maintenance";
          chapterDescription =
            "Learn vocabulary related to home repairs and maintenance services.";
        } else if (i === 30) {
          chapterTitle =
            "Päivittäisten keskustelujen kertaus / Daily Conversations Review";
          chapterDescription =
            "Comprehensive review of essential daily conversation vocabulary and phrases.";
        } else {
          chapterTitle = `Daily Life Chapter ${i}`;
          chapterDescription = `This is chapter ${i} in the Daily Life category.`;
        }
        break;

      case "web-development":
        if (i === 34) {
          chapterTitle = "JavaScript perusteet / JavaScript Basics";
          chapterDescription =
            "Learn fundamental JavaScript terminology and programming concepts in Finnish.";
        } else if (i === 35) {
          chapterTitle = "DOM-manipulointi / DOM Manipulation";
          chapterDescription =
            "Master vocabulary related to Document Object Model and dynamic web content.";
        } else if (i === 36) {
          chapterTitle = "Lomakkeet ja validointi / Forms and Validation";
          chapterDescription =
            "Learn vocabulary related to web forms, user input, and data validation.";
        } else if (i === 37) {
          chapterTitle = "AJAX ja API-kutsut / AJAX and API Calls";
          chapterDescription =
            "Master vocabulary for asynchronous requests and working with APIs.";
        } else if (i === 38) {
          chapterTitle = "Tietokannat ja SQL / Databases and SQL";
          chapterDescription =
            "Learn database terminology and SQL concepts in Finnish.";
        } else if (i === 39) {
          chapterTitle = "PHP perusteet / PHP Basics";
          chapterDescription =
            "Learn fundamental PHP terminology and server-side concepts in Finnish.";
        } else if (i === 40) {
          chapterTitle = "Node.js perusteet / Node.js Basics";
          chapterDescription =
            "Master vocabulary related to Node.js development and JavaScript runtime.";
        } else if (i === 41) {
          chapterTitle = "React perusteet / React Basics";
          chapterDescription =
            "Learn vocabulary for React development and component-based architecture.";
        } else if (i === 42) {
          chapterTitle = "Verkkotietoturva / Web Security";
          chapterDescription =
            "Master vocabulary related to web security, vulnerabilities, and protection measures.";
        } else if (i === 43) {
          chapterTitle =
            "Testaus ja laadunvarmistus / Testing and Quality Assurance";
          chapterDescription =
            "Learn vocabulary for software testing methodologies and quality control.";
        } else if (i === 44) {
          chapterTitle = "Versionhallinta / Version Control";
          chapterDescription =
            "Master vocabulary related to Git and collaborative development workflows.";
        } else if (i === 45) {
          chapterTitle = "Suorituskyvyn optimointi / Performance Optimization";
          chapterDescription =
            "Learn vocabulary for web performance, optimization techniques, and benchmarking.";
        } else if (i === 46) {
          chapterTitle = "Käyttöliittymäsuunnittelu / UI Design";
          chapterDescription =
            "Master vocabulary related to user interface design principles and visual elements.";
        } else if (i === 47) {
          chapterTitle = "Käyttäjäkokemus / User Experience";
          chapterDescription =
            "Learn vocabulary for UX design, user research, and interaction patterns.";
        } else if (i === 48) {
          chapterTitle = "Verkkosivuston käyttöönotto / Website Deployment";
          chapterDescription =
            "Master vocabulary related to hosting, deployment, and website maintenance.";
        } else if (i === 49) {
          chapterTitle = "Käyttöönotto ja julkaisu / Deployment and Release";
          chapterDescription =
            "Learn vocabulary for software release cycles and deployment strategies.";
        } else if (i === 50) {
          chapterTitle = "Kehittyvät verkkotrendit / Emerging Web Trends";
          chapterDescription =
            "Master vocabulary related to new web technologies and industry developments.";
        } else {
          chapterTitle = `Web Development Chapter ${i}`;
          chapterDescription = `This is chapter ${i} in the Web Development category.`;
        }
        break;

      case "cleaner":
        if (i === 51) {
          chapterTitle = "Siivousvälineet / Cleaning Tools";
          chapterDescription =
            "Learn vocabulary related to different cleaning tools and equipment.";
        } else if (i === 52) {
          chapterTitle = "Siivousaineet / Cleaning Products";
          chapterDescription =
            "Master vocabulary for various cleaning chemicals and their applications.";
        } else if (i === 53) {
          chapterTitle = "Kodin siivous / Home Cleaning";
          chapterDescription =
            "Learn vocabulary related to residential cleaning tasks and procedures.";
        } else if (i === 54) {
          chapterTitle = "Toimiston siivous / Office Cleaning";
          chapterDescription =
            "Master vocabulary for cleaning commercial office environments.";
        } else if (i === 55) {
          chapterTitle = "Hotellin siivous / Hotel Cleaning";
          chapterDescription =
            "Learn vocabulary related to hospitality cleaning and room service.";
        } else if (i === 56) {
          chapterTitle = "Teollisuussiivous / Industrial Cleaning";
          chapterDescription =
            "Master vocabulary for industrial cleaning environments and specialized procedures.";
        } else if (i === 57) {
          chapterTitle = "Lattioiden hoito / Floor Maintenance";
          chapterDescription =
            "Learn vocabulary related to different flooring types and maintenance methods.";
        } else if (i === 58) {
          chapterTitle = "Ikkunoiden puhdistus / Window Cleaning";
          chapterDescription =
            "Master vocabulary for window cleaning techniques and equipment.";
        } else if (i === 59) {
          chapterTitle = "Kylpyhuoneen siivous / Bathroom Cleaning";
          chapterDescription =
            "Learn vocabulary related to sanitizing bathrooms and plumbing fixtures.";
        } else if (i === 60) {
          chapterTitle = "Työturvallisuus / Work Safety";
          chapterDescription =
            "Master vocabulary for safety procedures and hazard prevention in cleaning work.";
        } else {
          chapterTitle = `Cleaner Chapter ${i}`;
          chapterDescription = `This is chapter ${i} in the Cleaner category.`;
        }
        break;

      case "kitchen-assistant":
        if (i === 61) {
          chapterTitle = "Keittiön perusteet / Kitchen Basics";
          chapterDescription =
            "Learn fundamental vocabulary for professional kitchen environments.";
        } else if (i === 62) {
          chapterTitle = "Ruoanvalmistusmenetelmät / Cooking Methods";
          chapterDescription =
            "Master vocabulary related to different cooking techniques and procedures.";
        } else if (i === 63) {
          chapterTitle =
            "Keittiövälineet ja -laitteet / Kitchen Utensils and Equipment";
          chapterDescription =
            "Learn vocabulary for various kitchen tools, appliances, and equipment.";
        } else if (i === 64) {
          chapterTitle =
            "Ruoan käsittely ja säilytys / Food Handling and Storage";
          chapterDescription =
            "Master vocabulary related to food safety, storage methods, and handling procedures.";
        } else if (i === 65) {
          chapterTitle =
            "Keittiön puhtaus ja hygienia / Kitchen Cleanliness and Hygiene";
          chapterDescription =
            "Learn vocabulary related to sanitation, cleaning protocols, and hygiene standards.";
        } else if (i === 66) {
          chapterTitle =
            "Erityisruokavaliot ja allergiat / Special Diets and Allergies";
          chapterDescription =
            "Master vocabulary for dietary restrictions, allergens, and special meal preparation.";
        } else if (i === 67) {
          chapterTitle =
            "Asiakaspalvelu keittiössä / Customer Service in Kitchen";
          chapterDescription =
            "Learn vocabulary for interacting with customers and handling food service requests.";
        } else if (i === 68) {
          chapterTitle = "Työturvallisuus keittiössä / Work Safety in Kitchen";
          chapterDescription =
            "Master vocabulary related to kitchen safety, accident prevention, and emergency procedures.";
        } else if (i === 69) {
          chapterTitle =
            "Tilaukset ja varastonhallinta / Orders and Inventory Management";
          chapterDescription =
            "Learn vocabulary for managing kitchen supplies, ordering, and inventory control.";
        } else if (i === 70) {
          chapterTitle =
            "Tiimityö ja viestintä keittiössä / Teamwork and Communication in Kitchen";
          chapterDescription =
            "Master vocabulary for effective communication and collaboration in busy kitchen environments.";
        } else {
          chapterTitle = `Kitchen Assistant Chapter ${i}`;
          chapterDescription = `This is chapter ${i} in the Kitchen Assistant category.`;
        }
        break;

      case "warehouse":
        if (i === 71) {
          chapterTitle = "Vastaanotto ja tarkastus / Receiving and Inspection";
          chapterDescription =
            "Learn vocabulary related to receiving shipments and quality control procedures.";
        } else if (i === 72) {
          chapterTitle = "Varastointitekniikat / Storage Techniques";
          chapterDescription =
            "Master vocabulary for different storage methods and warehouse organization.";
        } else if (i === 73) {
          chapterTitle = "Tilausten keräily / Order Picking";
          chapterDescription =
            "Learn vocabulary related to order fulfillment and picking processes.";
        } else if (i === 74) {
          chapterTitle = "Pakkaaminen ja lähettäminen / Packing and Shipping";
          chapterDescription =
            "Master vocabulary for packaging materials, shipping procedures, and documentation.";
        } else if (i === 75) {
          chapterTitle = "Varaston turvallisuus / Warehouse Safety";
          chapterDescription =
            "Learn vocabulary related to safety protocols and hazard prevention in warehouses.";
        } else if (i === 76) {
          chapterTitle = "Trukit ja laitteet / Forklifts and Equipment";
          chapterDescription =
            "Master vocabulary for material handling equipment and machinery operation.";
        } else if (i === 77) {
          chapterTitle = "Varastonhallinta / Inventory Management";
          chapterDescription =
            "Learn vocabulary related to tracking inventory, stock levels, and warehouse management.";
        } else if (i === 78) {
          chapterTitle = "Jätehuolto / Waste Management";
          chapterDescription =
            "Master vocabulary for handling, sorting, and disposing of warehouse waste materials.";
        } else if (i === 79) {
          chapterTitle = "Laatutarkastukset / Quality Inspections";
          chapterDescription =
            "Learn vocabulary related to quality control procedures and standards compliance.";
        } else if (i === 80) {
          chapterTitle = "Päivittäinen raportointi / Daily Reporting";
          chapterDescription =
            "Master vocabulary for warehouse metrics, documentation, and operational reporting.";
        } else {
          chapterTitle = `Warehouse Chapter ${i}`;
          chapterDescription = `This is chapter ${i} in the Warehouse category.`;
        }
        break;

      default:
        chapterTitle = `Chapter ${i}`;
        chapterDescription = `This is chapter ${i}.`;
    }

    chaptersHTML += `
            <div class="chapter-card ${category}" data-category="${category}" data-chapter="${i}">
                <div class="chapter-header">
                    <span class="chapter-number">${i}</span>
                    ${chapterTitle}
                </div>
                <div class="chapter-body">
                    <p>${chapterDescription}</p>
                </div>
                <div class="chapter-footer">
                    <span><i class="fas fa-clock"></i> 5 min</span>
                    <span><i class="fas fa-list"></i> 15+ terms</span>
                </div>
            </div>
        `;
  }

  // Remove the load more button
  loadMoreContainer.remove();

  // Add the new chapters to the grid
  chapterGrid.insertAdjacentHTML("beforeend", chaptersHTML);

  // Add click event listeners to the new chapter cards
  const newChapterCards = chapterGrid.querySelectorAll(
    `.chapter-card[data-chapter]`
  );
  newChapterCards.forEach((card) => {
    card.addEventListener("click", function () {
      const category = this.getAttribute("data-category");
      const chapterNumber = this.getAttribute("data-chapter");
      openChapterPage(category, chapterNumber);
    });
  });
}

// Function to toggle dark mode
// This will be used by all pages except index.html, which has its own implementation
function toggleDarkMode() {
  console.log("Script.js toggleDarkMode called");

  // Toggle dark mode class
  document.body.classList.toggle("dark-mode");

  // Toggle active class on button
  const darkModeBtn = document.getElementById("index-toggle-dark");
  if (darkModeBtn) {
    darkModeBtn.classList.toggle("active-tool");
  }

  // Store preference in localStorage
  const isDarkMode = document.body.classList.contains("dark-mode");
  console.log("Setting darkMode to:", isDarkMode ? "enabled" : "disabled");
  localStorage.setItem("darkMode", isDarkMode ? "enabled" : "disabled");

  // Update icon based on dark mode state
  if (darkModeBtn) {
    const icon = darkModeBtn.querySelector("i");
    if (icon) {
      if (isDarkMode) {
        icon.classList.remove("fa-moon");
        icon.classList.add("fa-sun");
      } else {
        icon.classList.remove("fa-sun");
        icon.classList.add("fa-moon");
      }
    }
  }

  console.log(
    "Dark mode is now:",
    document.body.classList.contains("dark-mode") ? "enabled" : "disabled"
  );
}

// Function to enable text highlighting when reading
function toggleHighlight() {
  // Toggle the text selection highlighting functionality
  const mainContent = document.querySelector(".main-content");
  const highlightBtn = document.getElementById("index-toggle-highlight");

  if (mainContent.classList.contains("highlight-mode")) {
    // Disable highlight mode
    mainContent.classList.remove("highlight-mode");
    document.removeEventListener("mouseup", highlightSelectedText);

    // Change button appearance to indicate it's off
    if (highlightBtn) {
      highlightBtn.classList.remove("active-tool");
      // Reset button styles explicitly to default
      highlightBtn.style.backgroundColor = "";
      highlightBtn.style.color = "";
      highlightBtn.style.fontWeight = "";
      highlightBtn.style.borderColor = "";
      highlightBtn.style.boxShadow = "";
      highlightBtn.style.textShadow = "";
    }

    // Store preference in localStorage
    localStorage.setItem("highlightMode", "disabled");
  } else {
    // Enable highlight mode
    mainContent.classList.add("highlight-mode");
    document.addEventListener("mouseup", highlightSelectedText);

    // Change button appearance to indicate it's on
    if (highlightBtn) {
      highlightBtn.classList.add("active-tool");

      // Get the highlight color from CSS variables
      const highlightColor = getComputedStyle(document.documentElement)
        .getPropertyValue("--highlight-color")
        .trim();

      // Apply styles directly to ensure they take effect
      highlightBtn.style.backgroundColor = highlightColor;
      highlightBtn.style.color = "black";
      highlightBtn.style.fontWeight = "bold";
      highlightBtn.style.borderColor = highlightColor;
      highlightBtn.style.boxShadow = `0 0 5px ${highlightColor}`;
      highlightBtn.style.textShadow = "0 0 1px rgba(0, 0, 0, 0.5)";
    }

    // Store preference in localStorage
    localStorage.setItem("highlightMode", "enabled");
  }

  // Force a repaint to ensure styles are applied
  if (highlightBtn) {
    highlightBtn.offsetHeight;
  }
}

// Function to highlight selected text - simplified version with single line restriction
// The full implementation is in highlight.js
function highlightSelectedText() {
  try {
    const selection = window.getSelection();

    if (!selection || selection.rangeCount === 0 || selection.isCollapsed) {
      return;
    }

    const range = selection.getRangeAt(0);

    // Get the selected text
    const selectedText = range.toString();

    // Check if the selection contains a newline character
    if (selectedText.includes("\n")) {
      // Show a message to the user
      alert(
        "Please highlight only one line at a time to maintain proper formatting."
      );
      selection.removeAllRanges();
      return;
    }

    // Don't highlight if selection is empty or just whitespace
    if (range.toString().trim() === "") {
      return;
    }

    // Create a span element for the highlight
    const highlightSpan = document.createElement("span");
    highlightSpan.className = "user-highlight";
    highlightSpan.style.backgroundColor = "var(--highlight-color)";
    highlightSpan.style.color = "black";

    try {
      // Try the simple approach first
      if (
        range.startContainer === range.endContainer &&
        range.startContainer.nodeType === Node.TEXT_NODE
      ) {
        range.surroundContents(highlightSpan);
      } else {
        // For complex selections, just insert the highlighted text
        const selectedText = range.toString();
        highlightSpan.textContent = selectedText;
        range.deleteContents();
        range.insertNode(highlightSpan);
      }
    } catch (e) {
      console.error("Highlighting failed:", e);
      // Fallback to simple text replacement
      try {
        const selectedText = range.toString();
        highlightSpan.textContent = selectedText;
        range.deleteContents();
        range.insertNode(highlightSpan);
      } catch (fallbackError) {
        console.error("All highlighting methods failed:", fallbackError);
      }
    }

    // Clear the selection
    selection.removeAllRanges();
  } catch (error) {
    console.error("Highlighting error:", error);
  }
}

// Initialize the page - show the first category by default
document.addEventListener("DOMContentLoaded", function () {
  // Get the first tab button and simulate a click
  const firstTabButton = document.querySelector(".tab-button");
  if (firstTabButton) {
    firstTabButton.click();
  }

  // Add click event listeners to all chapter cards
  const chapterCards = document.querySelectorAll(".chapter-card");
  chapterCards.forEach((card) => {
    card.addEventListener("click", function () {
      const category = this.getAttribute("data-category");
      const chapterNumber = this.getAttribute("data-chapter");
      openChapterPage(category, chapterNumber);
    });
  });

  // Set up highlight toggle
  const highlightToggle = document.getElementById("index-toggle-highlight");
  if (highlightToggle) {
    highlightToggle.addEventListener("click", toggleHighlight);
  }

  // Set up dark mode toggle - only for pages other than index.html
  // Index.html has its own implementation
  if (
    window.location.pathname.indexOf("index.html") === -1 &&
    window.location.pathname.slice(-1) !== "/"
  ) {
    console.log("Setting up dark mode toggle for non-index page");
    const darkModeToggle = document.getElementById("index-toggle-dark");
    if (darkModeToggle) {
      console.log("Adding click event to dark mode toggle");
      darkModeToggle.addEventListener("click", toggleDarkMode);
    }

    // Check if dark mode was previously enabled
    if (localStorage.getItem("darkMode") === "enabled") {
      console.log("Applying dark mode from localStorage for non-index page");
      document.body.classList.add("dark-mode");
      // Add active class to dark mode button
      const darkModeBtn = document.getElementById("index-toggle-dark");
      if (darkModeBtn) {
        darkModeBtn.classList.add("active-tool");
        // Change icon to sun
        const icon = darkModeBtn.querySelector("i");
        if (icon) {
          icon.classList.remove("fa-moon");
          icon.classList.add("fa-sun");
        }
      }
    }
  } else {
    console.log("Skipping dark mode setup for index.html");
  }

  // Check if highlight mode was previously enabled
  if (localStorage.getItem("highlightMode") === "enabled") {
    const mainContent = document.querySelector(".main-content");
    if (mainContent) {
      mainContent.classList.add("highlight-mode");
    }

    // Add active class and styles to highlight button
    const highlightBtn = document.getElementById("index-toggle-highlight");
    if (highlightBtn) {
      highlightBtn.classList.add("active-tool");

      // Get the highlight color from CSS variables
      const highlightColor = getComputedStyle(document.documentElement)
        .getPropertyValue("--highlight-color")
        .trim();

      // Apply styles directly
      highlightBtn.style.backgroundColor = highlightColor;
      highlightBtn.style.color = "black";
      highlightBtn.style.fontWeight = "bold";
      highlightBtn.style.borderColor = highlightColor;
      highlightBtn.style.boxShadow = `0 0 5px ${highlightColor}`;
      highlightBtn.style.textShadow = "0 0 1px rgba(0, 0, 0, 0.5)";

      // Add event listener for highlighting
      document.addEventListener("mouseup", highlightSelectedText);
    }
  }

  // Set up mobile menu toggle
  const mobileMenuToggle = document.getElementById("mobile-menu-toggle");
  const navLinksContainer = document.getElementById("nav-links");

  if (mobileMenuToggle && navLinksContainer) {
    mobileMenuToggle.addEventListener("click", function () {
      navLinksContainer.classList.toggle("show");

      // Change icon based on menu state
      const icon = this.querySelector("i");
      if (navLinksContainer.classList.contains("show")) {
        icon.classList.remove("fa-bars");
        icon.classList.add("fa-times");
      } else {
        icon.classList.remove("fa-times");
        icon.classList.add("fa-bars");
      }
    });

    // Handle dropdown clicks on mobile
    const dropdowns = document.querySelectorAll(".dropdown");
    dropdowns.forEach((dropdown) => {
      const dropbtn = dropdown.querySelector(".dropbtn");
      if (dropbtn) {
        dropbtn.addEventListener("click", function (e) {
          // Only handle click for mobile view
          if (window.innerWidth <= 768) {
            e.preventDefault();
            e.stopPropagation(); // Prevent event bubbling

            // Close other active dropdowns first
            dropdowns.forEach((otherDropdown) => {
              if (
                otherDropdown !== dropdown &&
                otherDropdown.classList.contains("active")
              ) {
                otherDropdown.classList.remove("active");
              }
            });

            // Toggle current dropdown
            dropdown.classList.toggle("active");
          }
        });
      }

      // Add click event to dropdown links to close the mobile menu
      const dropdownLinks = dropdown.querySelectorAll(".dropdown-content a");
      dropdownLinks.forEach((link) => {
        link.addEventListener("click", function () {
          if (window.innerWidth <= 768) {
            // Close the mobile menu
            if (
              navLinksContainer &&
              navLinksContainer.classList.contains("show")
            ) {
              navLinksContainer.classList.remove("show");

              // Change hamburger icon back to bars
              if (mobileMenuToggle) {
                const icon = mobileMenuToggle.querySelector("i");
                if (icon) {
                  icon.classList.remove("fa-times");
                  icon.classList.add("fa-bars");
                }
              }
            }
          }
        });
      });
    });

    // Add click event to all nav links (except dropdown buttons) to close the mobile menu
    const navLinkElements = document.querySelectorAll(
      ".nav-links > li > a:not(.dropbtn)"
    );
    navLinkElements.forEach((link) => {
      link.addEventListener("click", function () {
        if (window.innerWidth <= 768) {
          // Close the mobile menu
          if (
            navLinksContainer &&
            navLinksContainer.classList.contains("show")
          ) {
            navLinksContainer.classList.remove("show");

            // Change hamburger icon back to bars
            if (mobileMenuToggle) {
              const icon = mobileMenuToggle.querySelector("i");
              if (icon) {
                icon.classList.remove("fa-times");
                icon.classList.add("fa-bars");
              }
            }
          }
        }
      });
    });
  }
});

