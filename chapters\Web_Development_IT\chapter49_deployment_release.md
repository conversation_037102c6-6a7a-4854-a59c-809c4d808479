# Chapter 49: Sovellusten julkaisu / Deployment & Release

## Objectives / Tavoitteet
- Learn vocabulary related to software deployment and release processes in Finnish
- Understand how to discuss continuous integration, delivery, and deployment
- Be able to explain different release strategies and methodologies
- Master basic conversations about software release management

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. Sovellusten julkaisu - Application deployment/release
2. Jatkuva integraatio - Continuous integration
3. Jatkuva toimitus - Continuous delivery
4. Jat<PERSON><PERSON> käyttöönotto - Continuous deployment
5. Julkaisuputki - Release pipeline
6. Vaiheittainen julkaisu - Staged release
7. Sinivihreä julkaisu - Blue-green deployment
8. Kanariaversio - Canary release
9. Ominaisuuslippu - Feature flag
10. Jul<PERSON><PERSON> peruutus - Rollback
11. Versiointi - Versioning
12. Julkaisun automatisointi - Release automation
13. Tuotantoympäristö - Production environment
14. Testausympäristö - Testing environment
15. Julkaisun hyväksyntä - Release approval

## Grammar Points / Kielioppi
1. **Conditional Forms for Deployment Scenarios**:
   - Expressing hypothetical deployment situations
   - Example: <PERSON><PERSON> jul<PERSON>p<PERSON>ssi automatisoitaisiin, se säästäisi aikaa. (If the release process would be automated, it would save time.)

2. **Passive Voice for Deployment Processes**:
   - Describing actions without specifying who performs them
   - Example: Sovellus julkaistaan tuotantoon viikoittain. (The application is deployed to production weekly.)

3. **Translative Case (-ksi) for State Changes**:
   - Describing transformation from one state to another
   - Example: Kehitysversio muutetaan tuotantoversioksi. (The development version is changed into a production version.)

4. **Elative Case (-sta/-stä) for Source Environments**:
   - Describing movement from one environment to another
   - Example: Sovellus siirretään testausympäristöstä tuotantoon. (The application is moved from testing environment to production.)

5. **Instructive Case (-in) for Methods**:
   - Describing how something is done
   - Example: Julkaisu tehdään automaattisin työkaluin. (The release is done with automatic tools.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: Software deployment planning meeting / Ohjelmiston julkaisusuunnittelukokous

<div class="conversation">
<p><strong>Projektipäällikkö</strong>: Tervetuloa julkaisusuunnittelukokoukseen! Tänään keskustelemme uuden sovelluksemme julkaisustrategiasta.<br>
<em>(ter-ve-tu-lo-a jul-kai-su-suun-nit-te-lu-ko-ko-uk-seen! tä-nään kes-kus-te-lem-me uu-den so-vel-luk-sem-me jul-kai-su-stra-te-gi-as-ta.)</em></p>

<p><strong>Kehittäjä</strong>: Kiitos! Olen työstänyt julkaisuputkea, ja haluaisin esitellä ehdotukseni.<br>
<em>(kii-tos! o-len työs-tä-nyt jul-kai-su-put-ke-a, ja ha-lu-ai-sin e-si-tel-lä eh-do-tuk-se-ni.)</em></p>

<p><strong>Projektipäällikkö</strong>: Hienoa! Millainen julkaisustrategia sinulla on mielessä?<br>
<em>(hie-no-a! mil-lai-nen jul-kai-su-stra-te-gi-a si-nul-la on mie-les-sä?)</em></p>

<p><strong>Kehittäjä</strong>: Ehdotan, että ottaisimme käyttöön jatkuvan toimituksen mallin ja sinivihreän julkaisustrategian.<br>
<em>(eh-do-tan, et-tä ot-tai-sim-me käyt-töön jat-ku-van toi-mi-tuk-sen mal-lin ja si-ni-vih-re-än jul-kai-su-stra-te-gi-an.)</em></p>

<p><strong>Projektipäällikkö</strong>: Voisitko selittää tarkemmin, miten sinivihreä julkaisustrategia toimii?<br>
<em>(voi-sit-ko se-lit-tää tar-kem-min, mi-ten si-ni-vih-re-ä jul-kai-su-stra-te-gi-a toi-mii?)</em></p>

<p><strong>Projektipäällikkö</strong>: Se kuulostaa järkevältä. Entä miten varmistamme, että julkaisuprosessi on luotettava?<br>
<em>(se kuu-los-taa jär-ke-väl-tä. ent-tä mi-ten var-mis-tam-me, et-tä jul-kai-su-pro-ses-si on luo-tet-ta-va?)</em></p>

<p><strong>Projektipäällikkö</strong>: Entä julkaisuaikataulu? Kuinka usein suunnittelemme julkaisevamme uusia versioita?<br>
<em>(ent-tä jul-kai-su-ai-ka-tau-lu? kuin-ka u-sein suun-nit-te-lem-me jul-kai-se-vam-me uu-si-a ver-si-oi-ta?)</em></p>

<p><strong>Projektipäällikkö</strong>: Tämä kuulostaa kattavalta suunnitelmalta. Onko meillä prosessi julkaisun hyväksyntää varten?<br>
<em>(tä-mä kuu-los-taa kat-ta-val-ta suun-ni-tel-mal-ta. on-ko meil-lä pro-ses-si jul-kai-sun hy-väk-syn-tää var-ten?)</em></p>

<p><strong>Projektipäällikkö</strong>: Erinomaista työtä! Tämä suunnitelma antaa meille hyvän pohjan luotettavalle ja tehokkaalle julkaisuprosessille. Aloitetaan sen toteuttaminen heti.<br>
<em>(e-ri-no-mais-ta työ-tä! tä-mä suun-ni-tel-ma an-taa meil-le hy-vän poh-jan luo-tet-ta-val-le ja te-hok-kaal-le jul-kai-su-pro-ses-sil-le. a-loi-te-taan sen to-teut-ta-mi-nen he-ti.)</em></p>

<p><strong>Kehittäjä</strong>: Kiitos! Olen innoissani päästessäni toteuttamaan tätä prosessia. Uskon, että se parantaa merkittävästi sovelluksemme laatua ja vakautta.<br>
<em>(kii-tos! o-len in-nois-sa-ni pääs-tes-sä-ni to-teut-ta-maan tä-tä pro-ses-si-a. us-kon, et-tä se pa-ran-taa mer-kit-tä-väs-ti so-vel-luk-sem-me laa-tu-a ja va-kaut-ta.)</em></p>
</div>

### Cultural Notes:
- Finnish software companies often emphasize reliability and stability in their deployment processes
- There is a strong engineering culture in Finland that values automation and efficiency
- Finnish tech companies typically follow international best practices for software deployment
- Work-life balance is important in Finnish culture, so deployment processes are often designed to avoid after-hours work
- Finnish software development teams typically have flat hierarchies with collaborative decision-making
- Documentation and process clarity are highly valued in Finnish technical environments


