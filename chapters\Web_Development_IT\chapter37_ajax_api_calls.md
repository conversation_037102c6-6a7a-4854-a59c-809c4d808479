# Chapter 37: AJAX ja API-kutsut / AJAX & API Calls

## Objectives / Tavoitteet
- Learn vocabulary related to AJAX and API calls in Finnish
- Understand how to discuss asynchronous data fetching and APIs
- Be able to explain how to request and process data from servers
- Master basic conversations about integrating external data into websites

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. AJAX - Asynchronous JavaScript and XML
2. API - Application Programming Interface
3. Pyyntö - Request
4. Vastaus - Response
5. <PERSON>yn<PERSON><PERSON>inen - Asynchronous
6. Tiedonsiirto - Data transfer
7. Päätepiste - Endpoint
8. JSON - JavaScript Object Notation
9. Hakea - To fetch
10. Käsitellä - To process/handle
11. Autentikointi - Authentication
12. Parametri - Parameter
13. Otsikkotieto - Header
14. Ta<PERSON>sinkutsu - Callback
15. Virheenkäsittely - Error handling

## Grammar Points / Kielioppi
1. **Technical Verbs for API Operations**:
   - Action verbs for data operations
   - Example: Haen tietoja palvelimelta. (I fetch data from the server.)

2. **Conditional Forms for API Logic**:
   - Expressing API conditions
   - Example: <PERSON><PERSON>nistu<PERSON>, tiedot näytettäisiin käyttäjälle. (If the request succeeds, the data would be shown to the user.)

3. **Inessive Case (-ssa/-ssä) for API Contexts**:
   - In API operations
   - Example: Vastauksessa on JSON-muotoista dataa. (The response contains JSON-formatted data.)

4. **Elative Case (-sta/-stä) for Data Sources**:
   - From data sources
   - Example: Tiedot haetaan ulkoisesta rajapinnasta. (The data is fetched from an external interface.)

5. **Translative Case (-ksi) for Data Transformations**:
   - Converting data formats
   - Example: Data muunnetaan JSON-muotoon. (The data is converted to JSON format.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: AJAX and API workshop / AJAX ja API-työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa AJAX- ja API-työpajaan! Tänään opimme, miten voimme hakea ja käsitellä tietoja ulkoisista lähteistä verkkosovelluksissamme.<br>
<em>(ter-ve-tu-lo-a AJAX- ja API-työ-pa-jaan! tä-nään o-pim-me, mi-ten voim-me ha-ke-a ja kä-si-tel-lä tie-to-ja ul-koi-sis-ta läh-teis-tä verk-ko-so-vel-luk-sis-sam-me.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen kuullut AJAX:sta ja API:sta, mutta en ole varma, mitä ne tarkalleen tarkoittavat.<br>
<em>(kii-tos! o-len kuul-lut AJAX:s-ta ja API:s-ta, mut-ta en o-le var-ma, mi-tä ne tar-kal-leen tar-koit-ta-vat.)</em></p>

<p><strong>Ohjaaja</strong>: Hyvä kysymys! AJAX tarkoittaa Asynchronous JavaScript and XML:ää. Se on tekniikka, jolla voidaan hakea tietoja palvelimelta ilman, että koko sivua tarvitsee ladata uudelleen. API taas tarkoittaa Application Programming Interface:a eli ohjelmointirajapintaa, joka mahdollistaa eri ohjelmistojen välisen kommunikaation.<br>
<em>(hy-vä ky-sy-mys! AJAX tar-koit-taa a-synch-ro-nous ja-va-script and XML:ää. se on tek-niik-ka, jol-la voi-daan ha-ke-a tie-to-ja pal-ve-li-mel-ta il-man, et-tä ko-ko si-vu-a tar-vit-see la-da-ta uu-del-leen. API ta-as tar-koit-taa ap-pli-ca-tion prog-ram-ming in-ter-face:a e-li oh-jel-moin-ti-ra-ja-pin-taa, jo-ka mah-dol-lis-taa e-ri oh-jel-mis-to-jen vä-li-sen kom-mu-ni-kaa-ti-on.)</em></p>

<p><strong>Osallistuja</strong>: Miten AJAX-pyyntö tehdään käytännössä?<br>
<em>(mi-ten AJAX-pyyn-tö teh-dään käy-tän-nös-sä?)</em></p>

<p><strong>Osallistuja</strong>: Entä jos haluan lähettää tietoja palvelimelle?<br>
<em>(en-tä jos ha-lu-an lä-het-tää tie-to-ja pal-ve-li-mel-le?)</em></p>

<p><strong>Osallistuja</strong>: Mitä ovat API-avaimet ja miten niitä käytetään?<br>
<em>(mi-tä o-vat API-a-vai-met ja mi-ten nii-tä käy-te-tään?)</em></p>

<p><strong>Osallistuja</strong>: Miten käsittelen asynkronisia operaatioita?<br>
<em>(mi-ten kä-sit-te-len a-synk-ro-ni-si-a o-pe-raa-ti-oi-ta?)</em></p>

<p><strong>Osallistuja</strong>: Miten voin käsitellä virheitä API-kutsuissa?<br>
<em>(mi-ten voin kä-si-tel-lä vir-hei-tä API-kut-suis-sa?)</em></p>

<p><strong>Osallistuja</strong>: Entä CORS? Olen törmännyt siihen ongelmana.<br>
<em>(en-tä CORS? o-len tör-män-nyt sii-hen on-gel-ma-na.)</em></p>

<p><strong>Osallistuja</strong>: Voitko antaa esimerkin, miten näyttäisin API:sta haettuja tietoja verkkosivulla?<br>
<em>(voit-ko an-taa e-si-mer-kin, mi-ten näyt-täi-sin API:s-ta ha-et-tu-ja tie-to-ja verk-ko-si-vul-la?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen sovelluksen, joka käyttää jotain julkista API:a?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen so-vel-luk-sen, jo-ka käyt-tää jo-tain jul-kis-ta API:a?)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten voin käyttää AJAX:ia ja API-kutsuja verkkosovelluksissani.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten voin käyt-tää AJAX:i-a ja API-kut-su-ja verk-ko-so-vel-luk-sis-sa-ni.)</em></p>
</div>

### Cultural Notes:
- Finland has a strong tech ecosystem with many companies providing and consuming APIs
- Finnish digital services often integrate multiple data sources through APIs to provide comprehensive services
- Open data initiatives in Finland have made many public sector APIs available for developers
- Finnish developers value clean, efficient API implementations that follow best practices
- Many Finnish startups and tech companies build products that rely heavily on API integrations


