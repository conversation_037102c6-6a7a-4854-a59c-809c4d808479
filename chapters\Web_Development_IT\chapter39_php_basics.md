# Chapter 39: PHP-perusteet / PHP Basics

## Objectives / Tavoitteet
- Learn vocabulary related to PHP programming in Finnish
- Understand how to discuss server-side web development concepts
- Be able to explain basic PHP syntax and functionality
- Master basic conversations about creating dynamic websites with PHP

## Audio Duration / Äänitys kesto
- 5 minutes / 5 minuuttia

## Vocabulary / Sanasto
1. <PERSON><PERSON><PERSON><PERSON>oli - Server-side
2. Muuttuja - Variable
3. Funktio - Function
4. Ehto - Condition
5. Silmukka - Loop
6. Taulukko - Array
7. Merkkijono - String
8. Istunto - Session
9. Eväste - Cookie
10. Lomake - Form
11. Tietokantayhteys - Database connection
12. Sisällyttäminen - Including
13. Luokka - Class
14. Olio - Object
15. Virheenk<PERSON><PERSON>ttely - Error handling

## Grammar Points / Kielioppi
1. **Technical Verbs for PHP Operations**:
   - Action verbs for programming
   - Example: Käsittelen lomakkeen tiedot PHP:llä. (I process the form data with PHP.)

2. **Conditional Forms for PHP Logic**:
   - Expressing programming conditions
   - Example: <PERSON><PERSON> on kirjaut<PERSON><PERSON>, n<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hallintapaneeli. (If the user is logged in, the admin panel would be shown.)

3. **Inessive Case (-ssa/-ssä) for PHP Contexts**:
   - In programming structures
   - Example: PHP-tiedostossa on HTML- ja PHP-koodia. (There is HTML and PHP code in the PHP file.)

4. **Elative Case (-sta/-stä) for PHP Data Sources**:
   - From data sources
   - Example: Tiedot haetaan tietokannasta. (The information is retrieved from the database.)

5. **Translative Case (-ksi) for PHP Transformations**:
   - Converting data types
   - Example: Muunnan merkkijonon numeroksi. (I convert the string to a number.)

## Script & Notes / Käsikirjoitus & muistiinpanot

### Scene: PHP programming workshop / PHP-ohjelmoinnin työpaja

<div class="conversation">
<p><strong>Ohjaaja</strong>: Tervetuloa PHP-perusteiden työpajaan! Tänään opimme, miten PHP:tä käytetään dynaamisten verkkosivujen luomiseen.<br>
<em>(ter-ve-tu-lo-a PHP-pe-rus-tei-den työ-pa-jaan! tä-nään o-pim-me, mi-ten PHP:tä käy-te-tään dy-naa-mis-ten verk-ko-si-vu-jen luo-mi-seen.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos! Olen tehnyt staattisia verkkosivuja HTML:llä ja CSS:llä, mutta en ole käyttänyt PHP:tä aiemmin.<br>
<em>(kii-tos! o-len teh-nyt staat-ti-si-a verk-ko-si-vu-ja HTML:l-lä ja CSS:l-lä, mut-ta en o-le käyt-tä-nyt PHP:tä ai-em-min.)</em></p>

<p><strong>Ohjaaja</strong>: PHP on palvelinpuolen ohjelmointikieli, joka on suunniteltu erityisesti verkkokehitykseen. Toisin kuin HTML ja CSS, PHP suoritetaan palvelimella ennen kuin sivu lähetetään selaimelle. Tämä mahdollistaa dynaamisen sisällön luomisen, tietokantojen käytön ja paljon muuta.<br>
<em>(PHP on pal-ve-lin-puo-len oh-jel-moin-ti-kie-li, jo-ka on suun-ni-tel-tu e-ri-tyi-ses-ti verk-ko-ke-hi-tyk-seen. toi-sin kuin HTML ja CSS, PHP suo-ri-te-taan pal-ve-li-mel-la en-nen kuin si-vu lä-he-te-tään se-lai-mel-le. tä-mä mah-dol-lis-taa dy-naa-mi-sen si-säl-lön luo-mi-sen, tie-to-kan-to-jen käy-tön ja pal-jon muu-ta.)</em></p>

<p><strong>Osallistuja</strong>: Miten PHP-koodi kirjoitetaan?<br>
<em>(mi-ten PHP-koo-di kir-joi-te-taan?)</em></p>

<p><strong>Osallistuja</strong>: Miten muuttujia käytetään PHP:ssä?<br>
<em>(mi-ten muut-tu-ji-a käy-te-tään PHP:s-sä?)</em></p>

<p><strong>Osallistuja</strong>: Entä ehtolauseet ja silmukat?<br>
<em>(en-tä eh-to-lau-seet ja sil-mu-kat?)</em></p>

<p><strong>Osallistuja</strong>: Miten taulukoita käytetään PHP:ssä?<br>
<em>(mi-ten tau-lu-koi-ta käy-te-tään PHP:s-sä?)</em></p>

<p><strong>Osallistuja</strong>: Miten funktioita määritellään PHP:ssä?<br>
<em>(mi-ten funk-ti-oi-ta mää-ri-tel-lään PHP:s-sä?)</em></p>

<p><strong>Osallistuja</strong>: Miten lomakkeiden tietoja käsitellään PHP:llä?<br>
<em>(mi-ten lo-mak-kei-den tie-to-ja kä-si-tel-lään PHP:l-lä?)</em></p>

<p><strong>Osallistuja</strong>: Miten PHP:llä voidaan käyttää tietokantoja?<br>
<em>(mi-ten PHP:l-lä voi-daan käyt-tää tie-to-kan-to-ja?)</em></p>

<p><strong>Osallistuja</strong>: Miten istuntoja ja evästeitä käytetään PHP:ssä?<br>
<em>(mi-ten is-tun-to-ja ja e-väs-tei-tä käy-te-tään PHP:s-sä?)</em></p>

<p><strong>Osallistuja</strong>: Tämä on todella hyödyllistä! Voinko nyt kokeilla tehdä yksinkertaisen PHP-sovelluksen?<br>
<em>(tä-mä on to-del-la hyö-dyl-lis-tä! voin-ko nyt ko-keil-la teh-dä yk-sin-ker-tai-sen PHP-so-vel-luk-sen?)</em></p>

<p><strong>Ohjaaja</strong>: Ehdottomasti! Aloitetaan yksinkertaisella lomakkeella, joka käsittelee käyttäjän syötteet ja tallentaa ne tietokantaan. Tarvitset PHP-palvelimen, kuten XAMPP tai WAMP, jotta voit testata sovellusta paikallisesti. Muista myös, että PHP-koodi suoritetaan vain palvelimella, joten tiedostoja ei voi avata suoraan selaimessa, vaan ne pitää avata palvelimen kautta.<br>
<em>(eh-dot-to-mas-ti! a-loi-te-taan yk-sin-ker-tai-sel-la lo-mak-keel-la, jo-ka kä-sit-te-lee käyt-tä-jän syöt-teet ja tal-len-taa ne tie-to-kan-taan. tar-vit-set PHP-pal-ve-li-men, ku-ten XAMPP tai WAMP, jot-ta voit tes-ta-ta so-vel-lus-ta pai-kal-li-ses-ti. muis-ta my-ös, et-tä PHP-koo-di suo-ri-te-taan vain pal-ve-li-mel-la, jo-ten tie-dos-to-ja ei voi a-va-ta suo-raan se-lai-mes-sa, vaan ne pi-tää a-va-ta pal-ve-li-men kaut-ta.)</em></p>

<p><strong>Osallistuja</strong>: Kiitos paljon tästä työpajasta! Nyt ymmärrän paremmin, miten PHP toimii ja miten voin käyttää sitä dynaamisten verkkosivujen luomiseen.<br>
<em>(kii-tos pal-jon täs-tä työ-pa-jas-ta! nyt ym-mär-rän pa-rem-min, mi-ten PHP toi-mii ja mi-ten voin käyt-tää si-tä dy-naa-mis-ten verk-ko-si-vu-jen luo-mi-seen.)</em></p>
</div>

### Cultural Notes:
- While newer technologies have gained popularity, PHP remains widely used in Finland, especially in legacy systems and content management systems like WordPress
- Finnish web developers often value PHP for its practicality and wide hosting support
- Many Finnish educational institutions teach PHP as an introduction to server-side programming
- Finnish PHP developers typically emphasize security practices due to the language's historical vulnerabilities
- Finland has a community of PHP developers who contribute to open-source projects and organize meetups


